<config xmlns='http://www.ehcache.org/v3'
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns:jsr107="http://www.ehcache.org/v3/jsr107"
        xsi:schemaLocation="http://www.ehcache.org/v3 http://www.ehcache.org/schema/ehcache-core-3.3.xsd
                            http://www.ehcache.org/v3/jsr107 http://www.ehcache.org/schema/ehcache-107-ext-3.3.xsd">
    <service>
        <jsr107:defaults enable-management="true" enable-statistics="true"/>
    </service>
    <cache alias="lemonMsgInfo">
        <expiry>
            <ttl unit="minutes">5</ttl>
        </expiry>
        <heap unit="entries">10000</heap>
        <heap-store-settings>
            <max-object-size unit="kB">10</max-object-size>
        </heap-store-settings>
    </cache>
    <cache alias="accDojo">
        <expiry>
            <ttl unit="minutes">1</ttl>
        </expiry>
        <heap unit="entries">10</heap>
        <heap-store-settings>
            <max-object-size unit="kB">2</max-object-size>
        </heap-store-settings>
    </cache>

    <cache alias="common">
        <expiry>
            <ttl unit="minutes">1</ttl>
        </expiry>
        <heap unit="entries">10000</heap>
        <heap-store-settings>
            <max-object-size unit="kB">10</max-object-size>
        </heap-store-settings>
    </cache>
    <cache alias="envTxDt">
        <expiry>
            <ttl unit="minutes">1</ttl>
        </expiry>
        <heap unit="entries">10</heap>
        <heap-store-settings>
            <max-object-size unit="kB">2</max-object-size>
        </heap-store-settings>
    </cache>

    <cache alias="HACP_WEB_ADMIN_CIPHER">
        <expiry>
            <ttl unit="minutes">1</ttl>
        </expiry>
        <heap unit="entries">10000</heap>
        <heap-store-settings>
            <max-object-size unit="kB">2</max-object-size>
        </heap-store-settings>
    </cache>

    <cache alias="HACP_WEB_ADMIN_PERMISSION">
        <expiry>
            <ttl unit="minutes">5</ttl>
        </expiry>
        <heap unit="entries">10000</heap>
        <heap-store-settings>
            <max-object-size unit="kB">2</max-object-size>
        </heap-store-settings>
    </cache>

    <cache alias="HACP_WEB_ADMIN_MENU">
        <expiry>
            <ttl unit="minutes">5</ttl>
        </expiry>
        <heap unit="entries">10000</heap>
        <heap-store-settings>
            <max-object-size unit="kB">2</max-object-size>
        </heap-store-settings>
    </cache>

    <cache alias="HACP_WEB_ADMIN_USER">
        <expiry>
            <ttl unit="minutes">5</ttl>
        </expiry>
        <heap unit="entries">10000</heap>
        <heap-store-settings>
            <max-object-size unit="kB">2</max-object-size>
        </heap-store-settings>
    </cache>

</config>