# 按日巡检报告重新设计方案

## 🎯 设计原则

### 1. 分离关注点
- **按次巡检报告**：负责单次巡检任务的执行和结果记录
- **按日汇总报告**：负责汇总统计和分析展示
- **报告生成服务**：负责按日报告的生成和调度
- **报告查询服务**：负责按日报告的查询和展示

### 2. 数据持久化
- 按日汇总数据独立存储，避免实时计算性能问题
- 支持历史数据查询和趋势分析
- 提供数据一致性保障

### 3. 生成时机设计
- **定时生成**：每日凌晨自动生成前一日报告
- **手动生成**：支持手动触发生成指定日期报告
- **补偿生成**：自动检测并补偿缺失的报告

## 🏗️ 架构设计

### 数据模型设计

#### 1. 按次巡检报告表 (inspection_report)
```sql
-- 现有表，保持不变
-- 存储单次巡检任务的执行结果
```

#### 2. 按日汇总报告表 (daily_inspection_report)
```sql
CREATE TABLE `daily_inspection_report` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `report_date` date NOT NULL COMMENT '报告日期',
  `daily_report_id` varchar(64) NOT NULL COMMENT '日报告编号',
  `report_status` tinyint NOT NULL DEFAULT '0' COMMENT '报告状态',
  
  -- 汇总统计字段
  `per_report_count` int NOT NULL DEFAULT '0' COMMENT '按次报告数量',
  `task_count` int NOT NULL DEFAULT '0' COMMENT '任务数量',
  `total_rules` int NOT NULL DEFAULT '0' COMMENT '规则总数',
  `total_checks` int NOT NULL DEFAULT '0' COMMENT '检查总数',
  `success_checks` int NOT NULL DEFAULT '0' COMMENT '成功检查数',
  `warning_checks` int NOT NULL DEFAULT '0' COMMENT '告警检查数',
  `failed_checks` int NOT NULL DEFAULT '0' COMMENT '失败检查数',
  `overall_pass_rate` int NOT NULL DEFAULT '0' COMMENT '整体通过率',
  
  -- 任务维度统计
  `success_tasks` int NOT NULL DEFAULT '0' COMMENT '成功任务数',
  `warning_tasks` int NOT NULL DEFAULT '0' COMMENT '告警任务数',
  `failed_tasks` int NOT NULL DEFAULT '0' COMMENT '失败任务数',
  
  -- 异常统计
  `critical_exceptions` int NOT NULL DEFAULT '0' COMMENT '高危异常数',
  `warning_exceptions` int NOT NULL DEFAULT '0' COMMENT '中危异常数',
  `info_exceptions` int NOT NULL DEFAULT '0' COMMENT '低危异常数',
  
  -- 详细内容（JSON）
  `content` json DEFAULT NULL COMMENT '详细统计数据',
  
  -- 审计字段
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_daily_report_date` (`report_date`, `tenant_id`, `deleted`)
);
```

#### 3. 按日报告生成任务表 (daily_report_generation_task)
```sql
-- 用于跟踪报告生成任务的状态和进度
```

### 服务层设计

#### 1. DailyReportGenerationService (报告生成服务)
**职责**：
- 按日报告的生成逻辑
- 定时任务调度
- 失败重试机制
- 数据完整性验证

**核心方法**：
- `generateDailyReport(date, triggerType)` - 生成指定日期报告
- `generateDailyReportAsync(date, triggerType)` - 异步生成报告
- `compensateMissingReports(startDate, endDate)` - 补偿缺失报告
- `scheduleGenerateYesterdayReport()` - 定时生成昨日报告

#### 2. DailyInspectionReportService (报告查询服务)
**职责**：
- 按日报告的查询展示
- 数据格式转换
- 趋势分析计算

**核心方法**：
- `getDailyReportSummary(date)` - 获取日报告汇总
- `getDailyReportList(startDate, endDate, page)` - 获取报告列表
- `getDailyReportDetail(date)` - 获取报告详情
- `getPassRateTrend(endDate, days)` - 获取趋势数据

## 🔄 生成时机设计

### 1. 定时生成策略
```java
@Scheduled(cron = "0 30 1 * * ?") // 每日凌晨1:30执行
public void scheduleGenerateYesterdayReport() {
    LocalDate yesterday = LocalDate.now().minusDays(1);
    generateDailyReportAsync(yesterday, TriggerType.SCHEDULED);
}
```

**优势**：
- 确保数据完整性（前一日的按次报告已全部完成）
- 避免业务高峰期影响
- 提供稳定的数据更新时间

### 2. 手动生成策略
```java
// 支持手动触发生成指定日期的报告
public DailyReportDO generateDailyReport(LocalDate date, TriggerType.MANUAL);
```

**场景**：
- 补充历史数据
- 重新生成有问题的报告
- 即时查看当日数据

### 3. 补偿生成策略
```java
@Scheduled(cron = "0 0 * * * ?") // 每小时执行
public void scheduleRetryFailedTasks() {
    // 检查失败的生成任务并重试
    // 检查缺失的报告并补偿生成
}
```

**场景**：
- 系统故障导致的生成失败
- 数据源问题导致的数据缺失
- 定时任务执行异常

## 📊 数据流程设计

### 1. 报告生成流程
```mermaid
graph TD
    A[定时触发/手动触发] --> B[检查是否已存在]
    B --> C{存在且完成?}
    C -->|是| D[返回现有报告]
    C -->|否| E[获取按次报告数据]
    E --> F[计算汇总统计]
    F --> G[构建详细内容]
    G --> H[保存按日报告]
    H --> I[更新生成状态]
```

### 2. 数据查询流程
```mermaid
graph TD
    A[查询请求] --> B[检查按日报告表]
    B --> C{报告存在?}
    C -->|是| D[返回汇总数据]
    C -->|否| E[返回空数据/触发生成]
```

## 🚀 实现优势

### 1. 性能优势
- **查询性能**：直接从汇总表查询，避免实时计算
- **存储优化**：汇总数据量小，索引效率高
- **缓存友好**：汇总数据变化频率低，适合缓存

### 2. 数据一致性
- **事务保障**：报告生成过程使用事务确保一致性
- **版本控制**：支持数据版本管理和并发控制
- **完整性验证**：提供数据验证机制

### 3. 可扩展性
- **水平扩展**：支持分布式部署和异步处理
- **功能扩展**：易于添加新的统计维度和分析功能
- **数据源扩展**：支持多种数据源的汇总

### 4. 运维友好
- **监控支持**：提供生成进度和状态监控
- **故障恢复**：支持失败重试和数据补偿
- **历史数据**：支持历史数据的查询和分析

## 🔧 配置和部署

### 1. 定时任务配置
```yaml
# application.yml
spring:
  task:
    scheduling:
      pool:
        size: 5
    execution:
      pool:
        core-size: 10
        max-size: 20
```

### 2. 异步执行配置
```java
@Configuration
@EnableAsync
public class AsyncConfig {
    @Bean("asyncDailyReportExecutor")
    public TaskExecutor asyncDailyReportExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(10);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("DailyReport-");
        return executor;
    }
}
```

## 📈 监控和告警

### 1. 关键指标监控
- 报告生成成功率
- 报告生成耗时
- 数据完整性检查
- 系统资源使用情况

### 2. 告警策略
- 报告生成失败告警
- 数据缺失告警
- 性能异常告警

## 🎯 总结

重新设计的按日巡检报告架构具有以下特点：

1. **清晰的职责分离**：生成和查询分离，提高系统可维护性
2. **高效的数据存储**：独立的汇总表，提供优秀的查询性能
3. **可靠的生成机制**：多种触发方式和补偿机制，确保数据完整性
4. **灵活的扩展能力**：支持新功能和新需求的快速实现

这种设计能够很好地满足产品原型图片中展示的各种功能需求，同时具备良好的性能和可扩展性。
