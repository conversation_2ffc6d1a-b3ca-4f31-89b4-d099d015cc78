export DB_PASSWORD="Has@2024#qsde"
export EMAIL_HOST="x"
export EMAIL_PORT="x"
export EMAIL_USER="x"
export EMAIL_SECRET="x"
export K8S_URL="http://*************:31407"
export AES_KEY="c74ff85723086efb96c05bb0dde4e928"
export DEFAULT_AES_KEY="abc1234da"
export DEFAULT_SM4_KEY="00796ea4b720826730c20997dd709c3e"

export SPRING_APPLICATION_JSON='{
  "lemon.dataSources.primary.password":"'${DB_PASSWORD}'",
  "hacp.admin.email-server.host":"'${EMAIL_HOST}'",
  "hacp.admin.email-server.port":"'${EMAIL_PORT}'",
  "hacp.admin.email-server.user":"'${EMAIL_USER}'",
  "hacp.admin.email-server.secret":"'${EMAIL_SECRET}'",
  "hacp.emergence.kubesphere.properties.url":"'${K8S_URL}'",
  "hacp.web.admin.aesKey":"'${AES_KEY}'",
  "hacp.web.admin.Sensitive.aesKey":"'${DEFAULT_AES_KEY}'",
  "hacp.web.admin.Sensitive.SM4Key":"'${DEFAULT_SM4_KEY}'"
}'