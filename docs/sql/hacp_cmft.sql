-- ----------------------------
-- Table structure for cmft_dispatch_config
-- ----------------------------
DROP TABLE IF EXISTS `cmft_dispatch_config`;
CREATE TABLE `cmft_dispatch_config`  (
 `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
 `workspace_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目编号',
 `external_public_key` varchar(254) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '公钥',
 `external_private_key` varchar(254) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '私钥',
 `cmft_public_key` varchar(254) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '双活公钥',
 `project_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '在双活的项目id',
 `operator_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作员',
 `operator_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作员名称',
 `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
 `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
 `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态',
 PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;


INSERT INTO `sys_dict` VALUES ('108', '7', '系统-流量调度', 'taskType', '系统-流量调度', 4, '100', '7', '2024-09-03 17:38:46', '1', '2024-09-03 17:38:46', '系统-流量调度', '0');

INSERT INTO `sys_menu` VALUES (189198, 189186, '双活管控配置', NULL, '/EmergencyCase/cmft/config', 'emergency:cmft:config', 'M', 'PlusSquareOutlined', 7, NULL, '2024-11-11 18:05:14.000000', '2024-11-11 18:05:14.000000', '{}', '/EmergencyCase/cmft/edit', NULL, 'cmftConfig', NULL, 1, 0, 0, 1, 1);
INSERT INTO `sys_menu` VALUES (189199, 189198, '新增', NULL, 'CmftDispatchConfigController', 'emergency:cmft:config:add', 'B', NULL, 0, NULL, '2024-11-11 18:06:35.296227', '2024-11-11 18:06:35.296227', '{}', NULL, NULL, NULL, NULL, 1, 0, 0, 1, 1);
INSERT INTO `sys_menu` VALUES (189200, 189198, '修改', NULL, 'CmftDispatchConfigController', 'emergency:cmft:config:update', 'B', NULL, 0, NULL, '2024-11-11 18:07:00.882100', '2024-11-11 18:07:00.882100', '{}', NULL, NULL, 'emergency:cmft:config:update', NULL, 1, 0, 0, 1, 1);
INSERT INTO `sys_menu` VALUES (189201, 189198, '生成密钥', NULL, 'CmftDispatchConfigController', 'emergency:cmft:config:generate', 'B', NULL, 0, NULL, '2024-11-11 18:07:00.882100', '2024-11-11 18:07:00.882100', '{}', NULL, NULL, 'emergency:cmft:config:generate', NULL, 1, 0, 0, 1, 1);

INSERT INTO `sys_role_menu` VALUES ('1-189198', 1, 189198);
INSERT INTO `sys_role_menu` VALUES ('1-189199', 1, 189199);
INSERT INTO `sys_role_menu` VALUES ('1-189200', 1, 189200);
INSERT INTO `sys_role_menu` VALUES ('1-189200', 1, 189201);

INSERT INTO `sys_role_menu` VALUES ('1076-189198', 1076, 189198);
INSERT INTO `sys_role_menu` VALUES ('1076-189199', 1076, 189199);
INSERT INTO `sys_role_menu` VALUES ('1076-189200', 1076, 189200);
INSERT INTO `sys_role_menu` VALUES ('1076-189200', 1076, 189201);

