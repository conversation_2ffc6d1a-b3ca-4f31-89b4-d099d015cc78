-- ----------------------------
-- Table structure for tenant
-- ----------------------------
DROP TABLE IF EXISTS `tenant`;
CREATE TABLE `tenant`
(
	`tenant_id`   varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '租户ID',
	`tenant_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '租户名称',
	`dept_id`     varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL     DEFAULT NULL COMMENT '部门编号',
	`create_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT 'system' COMMENT '创建者',
	`create_time` datetime                                                      NOT NULL COMMENT '创建时间',
	`update_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT 'system' COMMENT '更新者',
	`update_time` datetime                                                      NOT NULL COMMENT '更新时间',
	`remarks`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL     DEFAULT NULL COMMENT '备注信息',
	`status`      varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT '0' COMMENT '删除标记',
	PRIMARY KEY (`tenant_id`) USING BTREE,
	UNIQUE INDEX `tenant_name_pk` (`tenant_name` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT = '系统管理-租户表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tenant_user
-- ----------------------------
DROP TABLE IF EXISTS `tenant_user`;
CREATE TABLE `tenant_user`
(
	`id`               varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '主键ID',
	`tenant_id`        varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '租户ID',
	`user_id`          varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '用户ID',
	`tenant_user_type` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci   NOT NULL DEFAULT '0' COMMENT '租户用户类型(1-租户管理员，0-普通用户)',
	`create_user`      varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT 'system' COMMENT '创建者',
	`create_time`      datetime                                                      NOT NULL COMMENT '创建时间',
	`update_user`      varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT 'system' COMMENT '更新者',
	`update_time`      datetime                                                      NOT NULL COMMENT '更新时间',
	`remarks`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL     DEFAULT NULL COMMENT '备注信息',
	`status`           varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT '0' COMMENT '删除标记',
	PRIMARY KEY (`id`) USING BTREE,
	UNIQUE INDEX `tenant_user_pk` (`tenant_id` ASC, `user_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT = '系统管理-租户成员表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tenant_workspace
-- ----------------------------
DROP TABLE IF EXISTS `tenant_workspace`;
CREATE TABLE `tenant_workspace`
(
	`id`           varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '主键ID',
	`tenant_id`    varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '租户ID',
	`workspace_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '项目ID',
	`create_user`  varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT 'system' COMMENT '创建者',
	`create_time`  datetime                                                      NOT NULL COMMENT '创建时间',
	`update_user`  varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT 'system' COMMENT '更新者',
	`update_time`  datetime                                                      NOT NULL COMMENT '更新时间',
	`remarks`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL     DEFAULT NULL COMMENT '备注信息',
	`status`       varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT '0' COMMENT '删除标记',
	PRIMARY KEY (`id`) USING BTREE,
	UNIQUE INDEX `tenant_workplace_pk` (`tenant_id` ASC, `workspace_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT = '系统管理-租户项目关联表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tenant_workspace_role
-- ----------------------------
DROP TABLE IF EXISTS `tenant_workspace_role`;
CREATE TABLE `tenant_workspace_role`
(
	`workspace_role_id`   varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '项目角色ID',
	`workspace_role_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '项目角色名称',
	`workspace_role_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '项目角色类型',
	`workspace_id`        varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '项目ID',
	`create_user`         varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT 'system' COMMENT '创建者',
	`create_time`         datetime                                                      NOT NULL COMMENT '创建时间',
	`update_user`         varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT 'system' COMMENT '更新者',
	`update_time`         datetime                                                      NOT NULL COMMENT '更新时间',
	`remarks`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL     DEFAULT NULL COMMENT '备注信息',
	`status`              varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT '0' COMMENT '删除标记',
	PRIMARY KEY (`workspace_role_id`) USING BTREE,
	UNIQUE INDEX `workplace_role_pk` (`workspace_role_name` ASC, `workspace_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT = '系统管理-项目角色表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tenant_workspace_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `tenant_workspace_role_menu`;
CREATE TABLE `tenant_workspace_role_menu`
(
	`id`                varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '主键ID',
	`workspace_role_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '项目角色ID',
	`menu_id`           int(11)                                                       NOT NULL COMMENT '菜单ID',
	`create_user`       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT 'system' COMMENT '创建者',
	`create_time`       datetime                                                      NOT NULL COMMENT '创建时间',
	`update_user`       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT 'system' COMMENT '更新者',
	`update_time`       datetime                                                      NOT NULL COMMENT '更新时间',
	`remarks`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL     DEFAULT NULL COMMENT '备注信息',
	`status`            varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT '0' COMMENT '删除标记',
	PRIMARY KEY (`id`) USING BTREE,
	UNIQUE INDEX `workplace_role_menu_pk` (`workspace_role_id` ASC, `menu_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT = '系统管理-项目角色菜单关联表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tenant_workspace_user
-- ----------------------------
DROP TABLE IF EXISTS `tenant_workspace_user`;
CREATE TABLE `tenant_workspace_user`
(
	`id`                  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '主键ID',
	`workspace_id`        varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '项目ID',
	`user_id`             varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '用户ID',
	`workspace_user_type` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci   NOT NULL DEFAULT '0' COMMENT '项目成员类型(1-项目管理员，0-普通用户)',
	`create_user`         varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT 'system' COMMENT '创建者',
	`create_time`         datetime                                                      NOT NULL COMMENT '创建时间',
	`update_user`         varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT 'system' COMMENT '更新者',
	`update_time`         datetime                                                      NOT NULL COMMENT '更新时间',
	`remarks`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL     DEFAULT NULL COMMENT '备注信息',
	`status`              varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT '0' COMMENT '删除标记',
	PRIMARY KEY (`id`) USING BTREE,
	UNIQUE INDEX `workplace_user_pk` (`workspace_id` ASC, `user_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT = '系统管理-项目成员表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tenant_workspace_user_role
-- ----------------------------
DROP TABLE IF EXISTS `tenant_workspace_user_role`;
CREATE TABLE `tenant_workspace_user_role`
(
	`id`                varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '主键ID',
	`workspace_role_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '项目角色ID',
	`user_id`           varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '用户ID',
	`create_user`       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT 'system' COMMENT '创建者',
	`create_time`       datetime                                                      NOT NULL COMMENT '创建时间',
	`update_user`       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT 'system' COMMENT '更新者',
	`update_time`       datetime                                                      NOT NULL COMMENT '更新时间',
	`remarks`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL     DEFAULT NULL COMMENT '备注信息',
	`status`            varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT '0' COMMENT '删除标记',
	PRIMARY KEY (`id`) USING BTREE,
	UNIQUE INDEX `workplace_user_role_pk` (`workspace_role_id` ASC, `user_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT = '系统管理-项目用户角色关联表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for workspace
-- ----------------------------
DROP TABLE IF EXISTS `workspace`;
CREATE TABLE `workspace`
(
	`workspace_id`   varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '项目ID',
	`workspace_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '项目名称',
	`profile`        varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NULL     DEFAULT NULL COMMENT '环境',
	`create_user`    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT 'system' COMMENT '创建者',
	`create_time`    datetime                                                      NOT NULL COMMENT '创建时间',
	`update_user`    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT 'system' COMMENT '更新者',
	`update_time`    datetime                                                      NOT NULL COMMENT '更新时间',
	`remarks`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL     DEFAULT NULL COMMENT '备注信息',
	`status`         varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT '0' COMMENT '删除标记',
	PRIMARY KEY (`workspace_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT = '系统管理-项目表'
  ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;

INSERT INTO `sys_menu` VALUES (188936, 0, '租户运维', NULL, '/tenantMaintenance', 'tenant:admin', 'D', 'AppstoreOutlined', 9, NULL, '2023-08-03 10:39:55.000000', '2024-06-20 15:02:36.000000', '{\"hiddenTenantHeader\" :true,\"name\":\"Tenant Management\"}', 'routerView', '/tenantMaintenance/tenantManagement', 'TenantMaintenance', NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (188937, 188936, '租户管理', NULL, '/tenantMaintenance/tenantManagement', 'tenant:admin', 'M', 'AppstoreOutlined', 0, NULL, '2023-08-03 17:01:59.470736', '2024-07-01 10:43:44.860772', '{}', '/tenantMaintenance/tenantManagement', NULL, 'TenantManagement', NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (188938, 188936, '项目管理', NULL, '/tenantMaintenance/projectManagement', 'tenant:workplace:admin', 'D', 'SettingOutlined', 0, NULL, '2023-08-03 17:04:32.000000', '2024-07-01 10:43:49.426436', '{}', 'routerView', NULL, 'ProjectManagement', NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (188939, 188938, '项目列表', NULL, '/tenantMaintenance/projectManagement/projectCreation', 'tenant:workplace:list', 'M', 'FolderAddOutlined', 1, NULL, '2023-08-03 17:07:01.000000', '2024-07-01 10:44:02.567125', '{}', '/tenantMaintenance/projectManagement/projectCreation', NULL, 'ProjectCreation', NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (188940, 188938, '项目成员', NULL, '/tenantMaintenance/projectManagement/projectMembers', 'tenant:workplace:member', 'M', 'UserOutlined', 2, NULL, '2023-08-03 17:08:14.000000', '2024-07-01 10:44:09.589589', '{}', '/tenantMaintenance/projectManagement/projectMembers', NULL, 'ProjectMembers', NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (188941, 188938, '项目角色', NULL, '/tenantMaintenance/projectManagement/rolesAdmin', 'tenant:workplace:role', 'M', 'TeamOutlined', 3, NULL, '2023-08-03 17:09:22.000000', '2024-07-01 10:44:12.474076', '{}', '/tenantMaintenance/projectManagement/rolesAdmin', NULL, 'RolesAdmin', NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (188947, 188937, '新增', NULL, 'tenant:admin:add', 'tenant:admin:add', 'B', NULL, 0, NULL, '2023-08-31 14:36:54.924103', '2023-08-31 14:41:58.557591', '{}', NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (188948, 188937, '修改', NULL, 'tenant:admin:update', 'tenant:admin:update', 'B', NULL, 0, NULL, '2023-08-31 14:37:38.158001', '2023-08-31 14:41:58.828841', '{}', NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (188949, 188937, '删除', NULL, 'tenant:admin:delete', 'tenant:admin:delete', 'B', NULL, 0, NULL, '2023-08-31 14:38:28.508629', '2023-08-31 14:41:59.014388', '{}', NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (188950, 188937, '查询', NULL, 'tenant:admin:query', 'tenant:admin:query', 'B', NULL, 0, NULL, '2023-08-31 14:39:48.992764', '2023-08-31 14:41:59.226399', '{}', NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (188951, 188939, '新增', NULL, 'tenant:workspace:admin:add', 'tenant:workspace:admin:add', 'B', NULL, 0, NULL, '2023-08-31 14:53:34.725642', '2024-06-24 17:26:05.702658', '{}', NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (188952, 188939, '修改', NULL, 'tenant:workspace:admin:update', 'tenant:workspace:admin:update', 'B', NULL, 0, NULL, '2023-08-31 14:53:34.725642', '2024-06-24 17:26:11.525123', '{}', NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (188953, 188939, '删除', NULL, 'tenant:workspace:admin:delete', 'tenant:workspace:admin:delete', 'B', NULL, 0, NULL, '2023-08-31 14:53:34.725642', '2024-06-24 17:26:16.423586', '{}', NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (188954, 188939, '查询', NULL, 'tenant:workspace:admin:query', 'tenant:workspace:admin:query', 'B', NULL, 0, NULL, '2023-08-31 14:53:34.725642', '2024-06-24 17:26:25.295919', '{}', NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (188955, 188940, '新增', NULL, 'tenant:workspace:member:add', 'tenant:workspace:member:add', 'B', NULL, 0, NULL, '2023-08-31 15:14:20.044017', '2024-06-24 17:26:31.636023', '{}', NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (188956, 188940, '修改', NULL, 'tenant:workspace:member:update', 'tenant:workspace:member:update', 'B', NULL, 0, NULL, '2023-08-31 15:14:20.044017', '2024-06-24 17:26:35.960917', '{}', NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (188957, 188940, '删除', NULL, 'tenant:workspace:member:delete', 'tenant:workspace:member:delete', 'B', NULL, 0, NULL, '2023-08-31 15:14:20.044017', '2024-06-24 17:26:40.626331', '{}', NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (188958, 188940, '查询', NULL, 'tenant:workspace:member:query', 'tenant:workspace:member:query', 'B', NULL, 0, NULL, '2023-08-31 15:14:20.044017', '2024-06-24 17:26:45.413530', '{}', NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (188959, 188941, '新增', NULL, 'tenant:workplace:role:add', 'tenant:workspace:role:add', 'B', NULL, 0, NULL, '2023-08-31 15:14:20.044017', '2024-06-24 17:26:51.133633', '{}', NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (188960, 188941, '修改', NULL, 'tenant:workspace:role:update', 'tenant:workspace:role:update', 'B', NULL, 0, NULL, '2023-08-31 15:14:20.044017', '2024-06-24 17:26:55.214830', '{}', NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (188961, 188941, '删除', NULL, 'tenant:workspace:role:delete', 'tenant:workspace:role:delete', 'B', NULL, 0, NULL, '2023-08-31 15:14:20.044017', '2024-06-24 17:26:58.878510', '{}', NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0);
INSERT INTO `sys_menu` VALUES (188962, 188941, '查询', NULL, 'tenant:workspace:role:query', 'tenant:workspace:role:query', 'B', NULL, 0, NULL, '2023-08-31 15:14:20.044017', '2024-06-24 17:26:01.161479', '{}', NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0);

-- 租户
INSERT INTO `sys_role_menu` VALUES ('1-188936', 1, 188936);
INSERT INTO `sys_role_menu` VALUES ('1-188937', 1, 188937);
INSERT INTO `sys_role_menu` VALUES ('1-188938', 1, 188938);
INSERT INTO `sys_role_menu` VALUES ('1-188939', 1, 188939);
INSERT INTO `sys_role_menu` VALUES ('1-188940', 1, 188940);
INSERT INTO `sys_role_menu` VALUES ('1-188941', 1, 188941);
INSERT INTO `sys_role_menu` VALUES ('1-188947', 1, 188947);
INSERT INTO `sys_role_menu` VALUES ('1-188948', 1, 188948);
INSERT INTO `sys_role_menu` VALUES ('1-188949', 1, 188949);
INSERT INTO `sys_role_menu` VALUES ('1-188950', 1, 188950);
INSERT INTO `sys_role_menu` VALUES ('1-188951', 1, 188951);
INSERT INTO `sys_role_menu` VALUES ('1-188952', 1, 188952);
INSERT INTO `sys_role_menu` VALUES ('1-188953', 1, 188953);
INSERT INTO `sys_role_menu` VALUES ('1-188954', 1, 188954);
INSERT INTO `sys_role_menu` VALUES ('1-188955', 1, 188955);
INSERT INTO `sys_role_menu` VALUES ('1-188956', 1, 188956);
INSERT INTO `sys_role_menu` VALUES ('1-188957', 1, 188957);
INSERT INTO `sys_role_menu` VALUES ('1-188958', 1, 188958);
INSERT INTO `sys_role_menu` VALUES ('1-188959', 1, 188959);
INSERT INTO `sys_role_menu` VALUES ('1-188960', 1, 188960);
INSERT INTO `sys_role_menu` VALUES ('1-188961', 1, 188961);
INSERT INTO `sys_role_menu` VALUES ('1-188962', 1, 188962);
INSERT INTO `sys_role_menu` VALUES ('1076-188936', 1076, 188936);
INSERT INTO `sys_role_menu` VALUES ('1076-188937', 1076, 188937);
INSERT INTO `sys_role_menu` VALUES ('1076-188938', 1076, 188938);
INSERT INTO `sys_role_menu` VALUES ('1076-188939', 1076, 188939);
INSERT INTO `sys_role_menu` VALUES ('1076-188940', 1076, 188940);
INSERT INTO `sys_role_menu` VALUES ('1076-188941', 1076, 188941);
INSERT INTO `sys_role_menu` VALUES ('1076-188947', 1076, 188947);
INSERT INTO `sys_role_menu` VALUES ('1076-188948', 1076, 188948);
INSERT INTO `sys_role_menu` VALUES ('1076-188949', 1076, 188949);
INSERT INTO `sys_role_menu` VALUES ('1076-188950', 1076, 188950);
INSERT INTO `sys_role_menu` VALUES ('1076-188951', 1076, 188951);
INSERT INTO `sys_role_menu` VALUES ('1076-188952', 1076, 188952);
INSERT INTO `sys_role_menu` VALUES ('1076-188953', 1076, 188953);
INSERT INTO `sys_role_menu` VALUES ('1076-188954', 1076, 188954);
INSERT INTO `sys_role_menu` VALUES ('1076-188955', 1076, 188955);
INSERT INTO `sys_role_menu` VALUES ('1076-188956', 1076, 188956);
INSERT INTO `sys_role_menu` VALUES ('1076-188957', 1076, 188957);
INSERT INTO `sys_role_menu` VALUES ('1076-188958', 1076, 188958);
INSERT INTO `sys_role_menu` VALUES ('1076-188959', 1076, 188959);
INSERT INTO `sys_role_menu` VALUES ('1076-188960', 1076, 188960);
INSERT INTO `sys_role_menu` VALUES ('1076-188961', 1076, 188961);
INSERT INTO `sys_role_menu` VALUES ('1076-188962', 1076, 188962);
