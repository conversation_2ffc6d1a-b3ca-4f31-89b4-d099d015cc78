-- 按日巡检汇总报告表
CREATE TABLE `daily_inspection_report` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `report_date` date NOT NULL COMMENT '报告日期',
  `daily_report_id` varchar(64) NOT NULL COMMENT '日报告编号（如：DAILY-20250124）',
  `report_status` tinyint NOT NULL DEFAULT '0' COMMENT '报告状态（0:生成中 1:已完成 2:生成失败 3:部分完成）',
  
  -- 汇总统计字段
  `per_report_count` int NOT NULL DEFAULT '0' COMMENT '汇总的按次报告数量',
  `task_count` int NOT NULL DEFAULT '0' COMMENT '汇总的任务数量',
  `total_rules` int NOT NULL DEFAULT '0' COMMENT '汇总的规则总数',
  `total_checks` int NOT NULL DEFAULT '0' COMMENT '汇总的检查总数',
  `success_checks` int NOT NULL DEFAULT '0' COMMENT '成功检查数',
  `warning_checks` int NOT NULL DEFAULT '0' COMMENT '告警检查数',
  `failed_checks` int NOT NULL DEFAULT '0' COMMENT '失败检查数',
  `overall_pass_rate` int NOT NULL DEFAULT '0' COMMENT '整体通过率（百分比）',
  
  -- 任务维度统计
  `success_tasks` int NOT NULL DEFAULT '0' COMMENT '成功任务数',
  `warning_tasks` int NOT NULL DEFAULT '0' COMMENT '告警任务数',
  `failed_tasks` int NOT NULL DEFAULT '0' COMMENT '失败任务数',
  
  -- 异常统计
  `critical_exceptions` int NOT NULL DEFAULT '0' COMMENT '高危异常数量',
  `warning_exceptions` int NOT NULL DEFAULT '0' COMMENT '中危异常数量',
  `info_exceptions` int NOT NULL DEFAULT '0' COMMENT '低危异常数量',
  
  -- 生成信息
  `generate_start_time` datetime DEFAULT NULL COMMENT '报告生成开始时间',
  `generate_end_time` datetime DEFAULT NULL COMMENT '报告生成完成时间',
  `generate_duration` bigint DEFAULT NULL COMMENT '报告生成耗时（毫秒）',
  
  -- 详细内容（JSON）
  `content` json DEFAULT NULL COMMENT '详细统计数据（JSON格式）',
  
  -- 版本控制
  `data_version` int NOT NULL DEFAULT '1' COMMENT '数据版本号',
  `remarks` varchar(500) DEFAULT NULL COMMENT '备注信息',
  
  -- 审计字段
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '删除标记（0:未删除 1:已删除）',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_daily_report_date` (`report_date`, `tenant_id`, `deleted`) COMMENT '日期唯一索引',
  UNIQUE KEY `uk_daily_report_id` (`daily_report_id`, `deleted`) COMMENT '报告ID唯一索引',
  KEY `idx_report_status` (`report_status`) COMMENT '报告状态索引',
  KEY `idx_create_time` (`create_time`) COMMENT '创建时间索引',
  KEY `idx_tenant_deleted` (`tenant_id`, `deleted`) COMMENT '租户删除标记索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='按日巡检汇总报告表';

-- 按日报告生成任务表（用于跟踪生成任务状态）
CREATE TABLE `daily_report_generation_task` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_id` varchar(64) NOT NULL COMMENT '任务ID',
  `report_date` date NOT NULL COMMENT '报告日期',
  `task_status` tinyint NOT NULL DEFAULT '0' COMMENT '任务状态（0:待执行 1:执行中 2:已完成 3:失败）',
  `trigger_type` tinyint NOT NULL DEFAULT '0' COMMENT '触发类型（0:定时触发 1:手动触发 2:补偿触发）',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `duration` bigint DEFAULT NULL COMMENT '执行耗时（毫秒）',
  `error_message` text DEFAULT NULL COMMENT '错误信息',
  `processed_reports` int DEFAULT '0' COMMENT '已处理的按次报告数量',
  `total_reports` int DEFAULT '0' COMMENT '总按次报告数量',
  `retry_count` int DEFAULT '0' COMMENT '重试次数',
  `max_retry_count` int DEFAULT '3' COMMENT '最大重试次数',
  
  -- 审计字段
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '删除标记（0:未删除 1:已删除）',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_id` (`task_id`, `deleted`) COMMENT '任务ID唯一索引',
  KEY `idx_report_date` (`report_date`) COMMENT '报告日期索引',
  KEY `idx_task_status` (`task_status`) COMMENT '任务状态索引',
  KEY `idx_trigger_type` (`trigger_type`) COMMENT '触发类型索引',
  KEY `idx_tenant_deleted` (`tenant_id`, `deleted`) COMMENT '租户删除标记索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='按日报告生成任务表';

-- 为按次巡检报告表添加索引优化（如果还没有的话）
-- ALTER TABLE `inspection_report` ADD INDEX `idx_start_time` (`start_time`) COMMENT '开始时间索引';
-- ALTER TABLE `inspection_report` ADD INDEX `idx_report_date` (DATE(`start_time`)) COMMENT '报告日期索引';

-- 插入示例数据（可选）
-- INSERT INTO `daily_inspection_report` (
--   `report_date`, `daily_report_id`, `report_status`, 
--   `per_report_count`, `task_count`, `total_checks`, 
--   `success_checks`, `failed_checks`, `overall_pass_rate`,
--   `success_tasks`, `failed_tasks`
-- ) VALUES (
--   '2025-01-24', 'DAILY-20250124', 1,
--   5, 5, 100,
--   85, 15, 85,
--   4, 1
-- );
