package com.cmpay.hacp.dashboard.bo;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class Summary {
    private String tenantId;
    private String tenantName;
    private String workspaceId;
    private String workspaceName;
    private List<ZoneInfo> zoneList = new ArrayList<>();
    private List<DispatchInfo> dispatchList = new ArrayList<>();
    private List<LocationInfo> locationList = new ArrayList<>();

    @Data
    public static class ZoneInfo {
        private Long id;
        private String zoneName;
        private String zoneLabel;
        private List<NodeInfo> nodeList = new ArrayList<>();
    }

    @Data
    public static class NodeInfo {
        private Integer dispatchNodeId;
        private String dispatchNodeName;
        private String runningDispatchVersion;
    }

    @Data
    public static class DispatchInfo {
        private Integer dispatchId;
        private String dispatchName;
        private Byte isEmergency;
    }

    @Data
    public static class LocationInfo {
        private Integer apiLocationId;
        private String apiLocationName;
    }
}
