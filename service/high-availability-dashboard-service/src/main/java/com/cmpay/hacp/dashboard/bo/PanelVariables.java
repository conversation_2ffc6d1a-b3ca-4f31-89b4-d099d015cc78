package com.cmpay.hacp.dashboard.bo;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

public class PanelVariables {
    private static final Map<PanelType, Map<String, Object>> VARIABLES = new HashMap<>();

    static {
        Map<String, Object> zone = new HashMap<>();
        zone.put("var-query_workspace", "WORKSPACE");
        VARIABLES.put(PanelType.ZONE, zone);
        VARIABLES.put(PanelType.ZONE_PERCENTAGE, zone);
        Map<String, Object> dispatch = new HashMap<>();
        dispatch.put("var-query_workspace", "WORKSPACE");
        dispatch.put("var-query_zone", "ZONE");
        dispatch.put("var-query_dispatch", "DISPATCH");
        VARIABLES.put(PanelType.DISPATCH, dispatch);
        VARIABLES.put(PanelType.DISPATCH_PERCENTAGE, dispatch);
        Map<String, Object> location = new HashMap<>();
        location.put("var-query_workspace", "WORKSPACE");
        location.put("var-query_zone", "ZONE");
        location.put("var-query_location", "LOCATION");
        VARIABLES.put(PanelType.LOCATION, location);
        VARIABLES.put(PanelType.AGENT, Collections.emptyMap());
    }

    public static Map<String, Object> get(PanelType type) {
        return VARIABLES.get(type);
    }
}
