package com.cmpay.hacp.inspection.application.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.hacp.inspection.application.service.DailyInspectionReportService;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.ReportContent;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.ReportDO;
import com.cmpay.hacp.inspection.infrastructure.repository.ReportRepository;
import com.cmpay.hacp.inspection.domain.model.enums.ExecutionStatus;
import com.cmpay.hacp.inspection.domain.model.enums.TriggerMode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * 按日巡检报告服务测试类
 */
@ExtendWith(MockitoExtension.class)
class DailyInspectionReportServiceImplTest {

    @Mock
    private ReportRepository reportRepository;

    @InjectMocks
    private DailyInspectionReportServiceImpl dailyInspectionReportService;

    private List<ReportDO> mockReports;
    private LocalDate testDate;

    @BeforeEach
    void setUp() {
        testDate = LocalDate.of(2025, 1, 24);
        mockReports = createMockReports();
    }

    @Test
    void testGetDailyReportSummary_WithData() {
        // Given
        when(reportRepository.list(any())).thenReturn(mockReports);

        // When
        Map<String, Object> result = dailyInspectionReportService.getDailyReportSummary(testDate);

        // Then
        assertNotNull(result);
        assertEquals(testDate, result.get("reportDate"));
        assertEquals(true, result.get("hasData"));
        assertEquals(2, result.get("taskCount"));
        assertTrue((Integer) result.get("totalChecks") > 0);
        assertTrue((Integer) result.get("passRate") >= 0);
        assertNotNull(result.get("executionSummary"));
        assertNotNull(result.get("executionDistribution"));
    }

    @Test
    void testGetDailyReportSummary_NoData() {
        // Given
        when(reportRepository.list(any())).thenReturn(List.of());

        // When
        Map<String, Object> result = dailyInspectionReportService.getDailyReportSummary(testDate);

        // Then
        assertNotNull(result);
        assertEquals(testDate, result.get("reportDate"));
        assertEquals(false, result.get("hasData"));
        assertEquals(0, result.get("totalChecks"));
        assertEquals(0, result.get("passRate"));
    }

    @Test
    void testGetDailyReportList() {
        // Given
        LocalDate startDate = testDate.minusDays(1);
        LocalDate endDate = testDate;
        Page<Object> page = new Page<>(1, 10);
        when(reportRepository.list(any())).thenReturn(mockReports);

        // When
        IPage<Map<String, Object>> result = dailyInspectionReportService.getDailyReportList(startDate, endDate, page);

        // Then
        assertNotNull(result);
        assertTrue(result.getTotal() >= 0);
        assertNotNull(result.getRecords());
    }

    @Test
    void testGetDailyReportDetail() {
        // Given
        when(reportRepository.list(any())).thenReturn(mockReports);

        // When
        Map<String, Object> result = dailyInspectionReportService.getDailyReportDetail(testDate);

        // Then
        assertNotNull(result);
        assertEquals(testDate, result.get("reportDate"));
        assertEquals(true, result.get("hasData"));
        assertNotNull(result.get("exceptionSummary"));
        assertNotNull(result.get("exceptionDetails"));
    }

    @Test
    void testGetPassRateTrend() {
        // Given
        when(reportRepository.list(any())).thenReturn(mockReports);

        // When
        Map<String, Object> result = dailyInspectionReportService.getPassRateTrend(testDate, 7);

        // Then
        assertNotNull(result);
        assertEquals("7天", result.get("period"));
        assertEquals(testDate, result.get("endDate"));
        assertNotNull(result.get("trendData"));
        
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> trendData = (List<Map<String, Object>>) result.get("trendData");
        assertEquals(7, trendData.size());
    }

    @Test
    void testGenerateDailyReport() {
        // Given
        when(reportRepository.list(any())).thenReturn(mockReports);

        // When
        boolean result = dailyInspectionReportService.generateDailyReport(testDate);

        // Then
        assertTrue(result);
    }

    @Test
    void testGenerateDailyReport_NoData() {
        // Given
        when(reportRepository.list(any())).thenReturn(List.of());

        // When
        boolean result = dailyInspectionReportService.generateDailyReport(testDate);

        // Then
        assertFalse(result);
    }

    /**
     * 创建模拟报告数据
     */
    private List<ReportDO> createMockReports() {
        ReportDO report1 = new ReportDO();
        report1.setId(1L);
        report1.setReportId("RPT-20250124001");
        report1.setTaskId("TASK-001");
        report1.setTaskName("网络连通性检测");
        report1.setTriggerMode(TriggerMode.SCHEDULED);
        report1.setExecutionStatus(ExecutionStatus.SUCCESS);
        report1.setStartTime(testDate.atTime(9, 0));
        report1.setEndTime(testDate.atTime(9, 5));
        report1.setExecutionDuration(300000L);
        report1.setExecutionTimeStr("5分钟");
        report1.setGenerateTime(LocalDateTime.now());
        report1.setContent(createMockReportContent());

        ReportDO report2 = new ReportDO();
        report2.setId(2L);
        report2.setReportId("RPT-20250124002");
        report2.setTaskId("TASK-002");
        report2.setTaskName("服务健康检查");
        report2.setTriggerMode(TriggerMode.MANUAL);
        report2.setExecutionStatus(ExecutionStatus.WARNING);
        report2.setStartTime(testDate.atTime(14, 0));
        report2.setEndTime(testDate.atTime(14, 3));
        report2.setExecutionDuration(180000L);
        report2.setExecutionTimeStr("3分钟");
        report2.setGenerateTime(LocalDateTime.now());
        report2.setContent(createMockReportContent());

        return Arrays.asList(report1, report2);
    }

    /**
     * 创建模拟报告内容
     */
    private ReportContent createMockReportContent() {
        ReportContent.ExecutionSummary executionSummary = ReportContent.ExecutionSummary.builder()
                .startTime(testDate.atTime(9, 0))
                .endTime(testDate.atTime(9, 5))
                .duration(300L)
                .durationStr("5分钟")
                .totalRules(5)
                .successRules(4)
                .build();

        List<ReportContent.ExecutionDistribution.StatusCount> statusCounts = Arrays.asList(
                ReportContent.ExecutionDistribution.StatusCount.builder()
                        .status("通过")
                        .statusCode("passed")
                        .count(4)
                        .build(),
                ReportContent.ExecutionDistribution.StatusCount.builder()
                        .status("失败")
                        .statusCode("failed")
                        .count(1)
                        .build()
        );

        ReportContent.ExecutionDistribution executionDistribution = ReportContent.ExecutionDistribution.builder()
                .total(5)
                .successRate(80)
                .statusCounts(statusCounts)
                .build();

        List<ReportContent.ExceptionDetail> exceptionDetails = Arrays.asList(
                ReportContent.ExceptionDetail.builder()
                        .ruleId("RULE-001")
                        .ruleName("端口连通性检查")
                        .description("端口8080连接超时")
                        .resourceName("web-server-1")
                        .suggestion("检查服务状态和网络连接")
                        .build()
        );

        return ReportContent.builder()
                .executionSummary(executionSummary)
                .executionDistribution(executionDistribution)
                .exceptionDetails(exceptionDetails)
                .build();
    }
}
