package com.cmpay.hacp.inspection.application.service.impl;

import com.cmpay.hacp.inspection.domain.model.enums.ScriptResultFieldType;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.PluginScriptOutputFieldDO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 脚本结果解析服务测试
 */
@ExtendWith(MockitoExtension.class)
class ScriptResultParserServiceImplTest {

    @InjectMocks
    private ScriptResultParserServiceImpl scriptResultParserService;

    private List<PluginScriptOutputFieldDO> fieldDefinitions;

    @BeforeEach
    void setUp() {
        // 设置字段定义
        PluginScriptOutputFieldDO cpuUsage = new PluginScriptOutputFieldDO();
        cpuUsage.setFieldName("cpu.usage");
        cpuUsage.setFieldType(ScriptResultFieldType.NUMERIC);
        cpuUsage.setFieldUnit("%");

        PluginScriptOutputFieldDO memoryUsage = new PluginScriptOutputFieldDO();
        memoryUsage.setFieldName("memory.usage");
        memoryUsage.setFieldType(ScriptResultFieldType.NUMERIC);
        memoryUsage.setFieldUnit("MB");

        PluginScriptOutputFieldDO serviceStatus = new PluginScriptOutputFieldDO();
        serviceStatus.setFieldName("service.status");
        serviceStatus.setFieldType(ScriptResultFieldType.BOOLEAN);

        PluginScriptOutputFieldDO serviceName = new PluginScriptOutputFieldDO();
        serviceName.setFieldName("service.name");
        serviceName.setFieldType(ScriptResultFieldType.STRING);

        fieldDefinitions = Arrays.asList(cpuUsage, memoryUsage, serviceStatus, serviceName);
    }

    @Test
    void testParseJsonOutput() {
        // 测试JSON格式输出解析
        String jsonOutput = "{\n" +
                "  \"cpu\": {\n" +
                "    \"usage\": 85.5\n" +
                "  },\n" +
                "  \"memory\": {\n" +
                "    \"usage\": 1024\n" +
                "  },\n" +
                "  \"service\": {\n" +
                "    \"status\": true,\n" +
                "    \"name\": \"nginx\"\n" +
                "  }\n" +
                "}";

        Map<String, Object> result = scriptResultParserService.parseScriptOutput(jsonOutput, fieldDefinitions);

        assertEquals(4, result.size());
        assertEquals(new BigDecimal("85.5"), result.get("cpu.usage"));
        assertEquals(new BigDecimal("1024"), result.get("memory.usage"));
        assertEquals(true, result.get("service.status"));
        assertEquals("nginx", result.get("service.name"));
    }

    @Test
    void testParseKeyValueOutput() {
        // 测试键值对格式输出解析
        String keyValueOutput = "cpu.usage=75.2\n" +
                "memory.usage=2048\n" +
                "service.status=true\n" +
                "service.name=apache\n";

        Map<String, Object> result = scriptResultParserService.parseScriptOutput(keyValueOutput, fieldDefinitions);

        assertEquals(4, result.size());
        assertEquals(new BigDecimal("75.2"), result.get("cpu.usage"));
        assertEquals(new BigDecimal("2048"), result.get("memory.usage"));
        assertEquals(true, result.get("service.status"));
        assertEquals("apache", result.get("service.name"));
    }

    @Test
    void testParseKeyValueOutputWithColon() {
        // 测试冒号分隔的键值对格式
        String keyValueOutput = "cpu.usage: 90.0\n" +
                "memory.usage: 512\n" +
                "service.status: false\n" +
                "service.name: mysql\n";

        Map<String, Object> result = scriptResultParserService.parseScriptOutput(keyValueOutput, fieldDefinitions);

        assertEquals(4, result.size());
        assertEquals(new BigDecimal("90.0"), result.get("cpu.usage"));
        assertEquals(new BigDecimal("512"), result.get("memory.usage"));
        assertEquals(false, result.get("service.status"));
        assertEquals("mysql", result.get("service.name"));
    }

    @Test
    void testConvertFieldValue_Numeric() {
        // 测试数值类型转换
        Object result1 = scriptResultParserService.convertFieldValue("85.5", ScriptResultFieldType.NUMERIC);
        assertEquals(new BigDecimal("85.5"), result1);

        Object result2 = scriptResultParserService.convertFieldValue("100%", ScriptResultFieldType.NUMERIC);
        assertEquals(new BigDecimal("100"), result2);

        Object result3 = scriptResultParserService.convertFieldValue("1024MB", ScriptResultFieldType.NUMERIC);
        assertEquals(new BigDecimal("1024"), result3);
    }

    @Test
    void testConvertFieldValue_Boolean() {
        // 测试布尔类型转换
        assertTrue((Boolean) scriptResultParserService.convertFieldValue("true", ScriptResultFieldType.BOOLEAN));
        assertTrue((Boolean) scriptResultParserService.convertFieldValue("1", ScriptResultFieldType.BOOLEAN));
        assertTrue((Boolean) scriptResultParserService.convertFieldValue("yes", ScriptResultFieldType.BOOLEAN));
        assertTrue((Boolean) scriptResultParserService.convertFieldValue("enabled", ScriptResultFieldType.BOOLEAN));

        assertFalse((Boolean) scriptResultParserService.convertFieldValue("false", ScriptResultFieldType.BOOLEAN));
        assertFalse((Boolean) scriptResultParserService.convertFieldValue("0", ScriptResultFieldType.BOOLEAN));
        assertFalse((Boolean) scriptResultParserService.convertFieldValue("no", ScriptResultFieldType.BOOLEAN));
        assertFalse((Boolean) scriptResultParserService.convertFieldValue("disabled", ScriptResultFieldType.BOOLEAN));
    }

    @Test
    void testConvertFieldValue_String() {
        // 测试字符串类型转换
        Object result = scriptResultParserService.convertFieldValue("test value", ScriptResultFieldType.STRING);
        assertEquals("test value", result);
    }

    @Test
    void testValidateFieldType() {
        // 测试字段类型验证
        assertTrue(scriptResultParserService.validateFieldType("85.5", ScriptResultFieldType.NUMERIC));
        assertTrue(scriptResultParserService.validateFieldType("true", ScriptResultFieldType.BOOLEAN));
        assertTrue(scriptResultParserService.validateFieldType("test", ScriptResultFieldType.STRING));

        assertFalse(scriptResultParserService.validateFieldType("invalid", ScriptResultFieldType.NUMERIC));
        assertFalse(scriptResultParserService.validateFieldType("maybe", ScriptResultFieldType.BOOLEAN));
    }

    @Test
    void testParseEmptyOutput() {
        // 测试空输出
        Map<String, Object> result = scriptResultParserService.parseScriptOutput("", fieldDefinitions);
        assertTrue(result.isEmpty());

        result = scriptResultParserService.parseScriptOutput(null, fieldDefinitions);
        assertTrue(result.isEmpty());
    }

    @Test
    void testParseOutputWithEmptyFieldDefinitions() {
        // 测试空字段定义
        String output = "cpu.usage=85.5";
        Map<String, Object> result = scriptResultParserService.parseScriptOutput(output, null);
        assertTrue(result.isEmpty());

        result = scriptResultParserService.parseScriptOutput(output, Arrays.asList());
        assertTrue(result.isEmpty());
    }

    @Test
    void testParsePartialMatch() {
        // 测试部分匹配场景
        String output = "cpu.usage=85.5\n" +
                "unknown.field=123\n" +
                "service.name=nginx\n";

        Map<String, Object> result = scriptResultParserService.parseScriptOutput(output, fieldDefinitions);

        assertEquals(2, result.size()); // 只有匹配的字段
        assertEquals(new BigDecimal("85.5"), result.get("cpu.usage"));
        assertEquals("nginx", result.get("service.name"));
        assertNull(result.get("unknown.field")); // 未定义的字段不会被包含
    }
}
