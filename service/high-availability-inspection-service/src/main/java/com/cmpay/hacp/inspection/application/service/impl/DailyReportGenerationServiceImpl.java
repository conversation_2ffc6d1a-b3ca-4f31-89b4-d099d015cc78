package com.cmpay.hacp.inspection.application.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cmpay.hacp.inspection.application.service.DailyReportGenerationService;
import com.cmpay.hacp.inspection.application.service.PerInspectionReportService;
import com.cmpay.hacp.inspection.domain.model.enums.ReportStatus;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.DailyReportContent;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.DailyReportDO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.ReportDO;
import com.cmpay.hacp.inspection.infrastructure.repository.DailyReportRepository;
import com.cmpay.hacp.inspection.infrastructure.repository.ReportRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 按日报告生成服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DailyReportGenerationServiceImpl implements DailyReportGenerationService {

    private final DailyReportRepository dailyReportRepository;
    private final ReportRepository reportRepository;
    private final PerInspectionReportService perInspectionReportService;

    @Override
    @Transactional
    public DailyReportDO generateDailyReport(LocalDate reportDate, Integer triggerType) {
        log.info("Starting daily report generation for date: {}, triggerType: {}", reportDate, triggerType);
        
        LocalDateTime startTime = LocalDateTime.now();
        
        try {
            // 1. 检查是否已存在该日期的报告
            DailyReportDO existingReport = getDailyReportByDate(reportDate);
            if (existingReport != null && ReportStatus.COMPLETED.equals(existingReport.getReportStatus())) {
                log.info("Daily report already exists for date: {}, reportId: {}", reportDate, existingReport.getDailyReportId());
                return existingReport;
            }

            // 2. 获取该日期的所有按次报告
            List<ReportDO> perReports = getPerInspectionReportsByDate(reportDate);
            if (perReports.isEmpty()) {
                log.warn("No per-inspection reports found for date: {}", reportDate);
                return createEmptyDailyReport(reportDate, startTime);
            }

            // 3. 创建或更新按日报告
            DailyReportDO dailyReport = existingReport != null ? existingReport : createNewDailyReport(reportDate);
            
            // 4. 设置生成状态
            dailyReport.setReportStatus(ReportStatus.GENERATING);
            dailyReport.setGenerateStartTime(startTime);
            
            // 5. 计算汇总统计数据
            calculateSummaryStatistics(dailyReport, perReports);
            
            // 6. 构建详细内容
            DailyReportContent content = buildDailyReportContent(perReports, reportDate);
            dailyReport.setContent(content);
            
            // 7. 设置完成状态
            LocalDateTime endTime = LocalDateTime.now();
            dailyReport.setReportStatus(ReportStatus.COMPLETED);
            dailyReport.setGenerateEndTime(endTime);
            dailyReport.setGenerateDuration(java.time.Duration.between(startTime, endTime).toMillis());
            
            // 8. 保存报告
            dailyReportRepository.saveOrUpdate(dailyReport);
            
            log.info("Daily report generated successfully for date: {}, reportId: {}, duration: {}ms", 
                    reportDate, dailyReport.getDailyReportId(), dailyReport.getGenerateDuration());
            
            return dailyReport;
            
        } catch (Exception e) {
            log.error("Failed to generate daily report for date: {}", reportDate, e);
            
            // 更新失败状态
            DailyReportDO failedReport = getDailyReportByDate(reportDate);
            if (failedReport != null) {
                failedReport.setReportStatus(ReportStatus.FAILED);
                failedReport.setGenerateEndTime(LocalDateTime.now());
                failedReport.setRemarks("生成失败: " + e.getMessage());
                dailyReportRepository.saveOrUpdate(failedReport);
            }
            
            throw new RuntimeException("Failed to generate daily report for date: " + reportDate, e);
        }
    }

    @Override
    @Async("asyncDailyReportExecutor")
    public String generateDailyReportAsync(LocalDate reportDate, Integer triggerType) {
        String taskId = generateTaskId(reportDate);
        log.info("Starting async daily report generation for date: {}, taskId: {}", reportDate, taskId);
        
        try {
            generateDailyReport(reportDate, triggerType);
            log.info("Async daily report generation completed for date: {}, taskId: {}", reportDate, taskId);
        } catch (Exception e) {
            log.error("Async daily report generation failed for date: {}, taskId: {}", reportDate, taskId, e);
        }
        
        return taskId;
    }

    @Override
    public List<String> batchGenerateDailyReports(LocalDate startDate, LocalDate endDate, Integer triggerType) {
        log.info("Starting batch daily report generation from {} to {}", startDate, endDate);
        
        List<String> taskIds = new ArrayList<>();
        LocalDate currentDate = startDate;
        
        while (!currentDate.isAfter(endDate)) {
            try {
                String taskId = generateDailyReportAsync(currentDate, triggerType);
                taskIds.add(taskId);
            } catch (Exception e) {
                log.error("Failed to start daily report generation for date: {}", currentDate, e);
            }
            currentDate = currentDate.plusDays(1);
        }
        
        log.info("Batch daily report generation started for {} dates", taskIds.size());
        return taskIds;
    }

    @Override
    @Transactional
    public DailyReportDO regenerateDailyReport(LocalDate reportDate, boolean force) {
        log.info("Regenerating daily report for date: {}, force: {}", reportDate, force);
        
        if (!force) {
            DailyReportDO existingReport = getDailyReportByDate(reportDate);
            if (existingReport != null && ReportStatus.COMPLETED.equals(existingReport.getReportStatus())) {
                log.info("Daily report already exists and force=false, skipping regeneration for date: {}", reportDate);
                return existingReport;
            }
        }
        
        // 删除现有报告（如果存在）
        dailyReportRepository.remove(
                Wrappers.lambdaQuery(DailyReportDO.class)
                        .eq(DailyReportDO::getReportDate, reportDate)
        );
        
        // 重新生成
        return generateDailyReport(reportDate, 2); // 2表示补偿触发
    }

    @Override
    public int compensateMissingReports(LocalDate startDate, LocalDate endDate) {
        log.info("Starting compensation for missing daily reports from {} to {}", startDate, endDate);
        
        int compensatedCount = 0;
        LocalDate currentDate = startDate;
        
        while (!currentDate.isAfter(endDate)) {
            DailyReportDO existingReport = getDailyReportByDate(currentDate);
            
            // 检查是否缺失或生成失败
            if (existingReport == null || ReportStatus.FAILED.equals(existingReport.getReportStatus())) {
                try {
                    generateDailyReportAsync(currentDate, 2); // 2表示补偿触发
                    compensatedCount++;
                    log.info("Scheduled compensation for missing daily report on date: {}", currentDate);
                } catch (Exception e) {
                    log.error("Failed to schedule compensation for date: {}", currentDate, e);
                }
            }
            
            currentDate = currentDate.plusDays(1);
        }
        
        log.info("Compensation completed, scheduled {} missing daily reports", compensatedCount);
        return compensatedCount;
    }

    @Override
    @Scheduled(cron = "0 30 1 * * ?") // 每日凌晨1:30执行
    public void scheduleGenerateYesterdayReport() {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        log.info("Scheduled task: generating yesterday's daily report for date: {}", yesterday);
        
        try {
            generateDailyReportAsync(yesterday, 0); // 0表示定时触发
        } catch (Exception e) {
            log.error("Failed to schedule yesterday's daily report generation for date: {}", yesterday, e);
        }
    }

    @Override
    @Scheduled(cron = "0 0 * * * ?") // 每小时执行一次
    public void scheduleRetryFailedTasks() {
        log.info("Scheduled task: retrying failed daily report generation tasks");
        
        try {
            // 查找最近7天内失败的报告
            LocalDate endDate = LocalDate.now().minusDays(1);
            LocalDate startDate = endDate.minusDays(6);
            
            List<DailyReportDO> failedReports = dailyReportRepository.list(
                    Wrappers.lambdaQuery(DailyReportDO.class)
                            .eq(DailyReportDO::getReportStatus, ReportStatus.FAILED)
                            .between(DailyReportDO::getReportDate, startDate, endDate)
            );
            
            for (DailyReportDO failedReport : failedReports) {
                try {
                    generateDailyReportAsync(failedReport.getReportDate(), 2); // 2表示补偿触发
                    log.info("Scheduled retry for failed daily report on date: {}", failedReport.getReportDate());
                } catch (Exception e) {
                    log.error("Failed to schedule retry for date: {}", failedReport.getReportDate(), e);
                }
            }
            
        } catch (Exception e) {
            log.error("Failed to execute retry failed tasks schedule", e);
        }
    }

    @Override
    @Scheduled(cron = "0 0 2 * * ?") // 每日凌晨2:00执行
    public void scheduleCleanupExpiredTasks() {
        log.info("Scheduled task: cleaning up expired generation task records");
        
        try {
            // 清理30天前的任务记录（这里可以根据需要实现具体的清理逻辑）
            LocalDate cutoffDate = LocalDate.now().minusDays(30);
            log.info("Cleanup cutoff date: {}", cutoffDate);
            
            // TODO: 实现具体的清理逻辑
            
        } catch (Exception e) {
            log.error("Failed to execute cleanup expired tasks schedule", e);
        }
    }

    @Override
    public GenerationProgress getGenerationProgress(String taskId) {
        // TODO: 实现进度查询逻辑
        GenerationProgress progress = new GenerationProgress();
        progress.setTaskId(taskId);
        progress.setStatus("UNKNOWN");
        return progress;
    }

    @Override
    public boolean cancelGenerationTask(String taskId) {
        // TODO: 实现任务取消逻辑
        log.info("Cancelling generation task: {}", taskId);
        return false;
    }

    @Override
    public ValidationResult validateDailyReport(LocalDate reportDate) {
        log.info("Validating daily report for date: {}", reportDate);
        
        ValidationResult result = new ValidationResult();
        List<String> issues = new ArrayList<>();
        
        try {
            DailyReportDO dailyReport = getDailyReportByDate(reportDate);
            if (dailyReport == null) {
                issues.add("Daily report not found for date: " + reportDate);
                result.setValid(false);
                result.setIssues(issues);
                return result;
            }
            
            // 验证数据完整性
            List<ReportDO> perReports = getPerInspectionReportsByDate(reportDate);
            
            // 验证统计数据是否正确
            int expectedPerReportCount = perReports.size();
            if (!Objects.equals(dailyReport.getPerReportCount(), expectedPerReportCount)) {
                issues.add("Per report count mismatch: expected " + expectedPerReportCount + 
                          ", actual " + dailyReport.getPerReportCount());
            }
            
            // 可以添加更多验证逻辑...
            
            result.setValid(issues.isEmpty());
            result.setIssues(issues);
            result.setSummary(issues.isEmpty() ? "Validation passed" : "Found " + issues.size() + " issues");
            
        } catch (Exception e) {
            log.error("Failed to validate daily report for date: {}", reportDate, e);
            issues.add("Validation failed: " + e.getMessage());
            result.setValid(false);
            result.setIssues(issues);
        }
        
        return result;
    }

    /**
     * 获取指定日期的按日报告
     */
    private DailyReportDO getDailyReportByDate(LocalDate reportDate) {
        return dailyReportRepository.getOne(
                Wrappers.lambdaQuery(DailyReportDO.class)
                        .eq(DailyReportDO::getReportDate, reportDate)
        );
    }

    /**
     * 获取指定日期的所有按次报告
     */
    private List<ReportDO> getPerInspectionReportsByDate(LocalDate reportDate) {
        return perInspectionReportService.getPerInspectionReportsByDate(reportDate);
    }

    /**
     * 创建空的按日报告（当没有按次报告时）
     */
    private DailyReportDO createEmptyDailyReport(LocalDate reportDate, LocalDateTime startTime) {
        DailyReportDO dailyReport = createNewDailyReport(reportDate);
        dailyReport.setReportStatus(ReportStatus.COMPLETED);
        dailyReport.setGenerateStartTime(startTime);
        dailyReport.setGenerateEndTime(LocalDateTime.now());
        dailyReport.setGenerateDuration(java.time.Duration.between(startTime, LocalDateTime.now()).toMillis());
        dailyReport.setRemarks("当日无巡检数据");

        dailyReportRepository.save(dailyReport);
        return dailyReport;
    }

    /**
     * 创建新的按日报告对象
     */
    private DailyReportDO createNewDailyReport(LocalDate reportDate) {
        DailyReportDO dailyReport = new DailyReportDO();
        dailyReport.setReportDate(reportDate);
        dailyReport.setDailyReportId(generateDailyReportId(reportDate));
        dailyReport.setReportStatus(ReportStatus.GENERATING);
        dailyReport.setDataVersion(1);
        return dailyReport;
    }

    /**
     * 生成按日报告ID
     */
    private String generateDailyReportId(LocalDate reportDate) {
        return "DAILY-" + reportDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
    }

    /**
     * 生成任务ID
     */
    private String generateTaskId(LocalDate reportDate) {
        return "TASK-DAILY-" + reportDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")) +
               "-" + System.currentTimeMillis();
    }

    /**
     * 计算汇总统计数据
     */
    private void calculateSummaryStatistics(DailyReportDO dailyReport, List<ReportDO> perReports) {
        // 基础统计
        dailyReport.setPerReportCount(perReports.size());
        dailyReport.setTaskCount(perReports.size()); // 假设一个报告对应一个任务

        // 计算检查统计
        int totalChecks = 0;
        int successChecks = 0;
        int warningChecks = 0;
        int failedChecks = 0;
        int totalRules = 0;

        // 任务状态统计
        int successTasks = 0;
        int warningTasks = 0;
        int failedTasks = 0;

        // 异常统计
        int criticalExceptions = 0;
        int warningExceptions = 0;
        int infoExceptions = 0;

        for (ReportDO report : perReports) {
            // 统计检查数据
            if (report.getContent() != null && report.getContent().getExecutionDistribution() != null) {
                totalChecks += report.getContent().getExecutionDistribution().getTotal();

                report.getContent().getExecutionDistribution().getStatusCounts().forEach(status -> {
                    switch (status.getStatusCode()) {
                        case "passed":
                            successChecks += status.getCount();
                            break;
                        case "warning":
                            warningChecks += status.getCount();
                            break;
                        case "failed":
                            failedChecks += status.getCount();
                            break;
                    }
                });
            }

            // 统计规则数据
            if (report.getContent() != null && report.getContent().getExecutionSummary() != null) {
                totalRules += report.getContent().getExecutionSummary().getTotalRules();
            }

            // 统计任务状态
            switch (report.getExecutionStatus()) {
                case SUCCESS:
                    successTasks++;
                    break;
                case WARNING:
                    warningTasks++;
                    break;
                case FAILED:
                    failedTasks++;
                    break;
            }

            // 统计异常（简化处理，实际可以根据异常内容进行分类）
            if (report.getContent() != null && report.getContent().getExceptionDetails() != null) {
                warningExceptions += report.getContent().getExceptionDetails().size();
            }
        }

        // 设置统计数据
        dailyReport.setTotalRules(totalRules);
        dailyReport.setTotalChecks(totalChecks);
        dailyReport.setSuccessChecks(successChecks);
        dailyReport.setWarningChecks(warningChecks);
        dailyReport.setFailedChecks(failedChecks);
        dailyReport.setOverallPassRate(totalChecks > 0 ? (successChecks * 100) / totalChecks : 0);

        dailyReport.setSuccessTasks(successTasks);
        dailyReport.setWarningTasks(warningTasks);
        dailyReport.setFailedTasks(failedTasks);

        dailyReport.setCriticalExceptions(criticalExceptions);
        dailyReport.setWarningExceptions(warningExceptions);
        dailyReport.setInfoExceptions(infoExceptions);
    }

    /**
     * 构建按日报告详细内容
     */
    private DailyReportContent buildDailyReportContent(List<ReportDO> perReports, LocalDate reportDate) {
        DailyReportContent.DailyReportContentBuilder builder = DailyReportContent.builder();

        // 构建执行时间分布
        builder.timeDistribution(buildTimeDistribution(perReports));

        // 构建任务类型分布
        builder.taskTypeDistribution(buildTaskTypeDistribution(perReports));

        // 构建异常汇总
        builder.exceptionSummaries(buildExceptionSummaries(perReports));

        // 构建资源统计
        builder.resourceStatistics(buildResourceStatistics(perReports));

        // 构建趋势对比
        builder.trendComparison(buildTrendComparison(reportDate));

        // 构建按次报告汇总
        builder.perReportSummaries(buildPerReportSummaries(perReports));

        return builder.build();
    }

    /**
     * 构建执行时间分布
     */
    private DailyReportContent.ExecutionTimeDistribution buildTimeDistribution(List<ReportDO> perReports) {
        // 按时间段统计
        Map<String, Integer> timeSlotCounts = new HashMap<>();
        timeSlotCounts.put("00:00-06:00", 0);
        timeSlotCounts.put("06:00-12:00", 0);
        timeSlotCounts.put("12:00-18:00", 0);
        timeSlotCounts.put("18:00-24:00", 0);

        Map<String, Integer> timeSlotSuccess = new HashMap<>();
        timeSlotSuccess.put("00:00-06:00", 0);
        timeSlotSuccess.put("06:00-12:00", 0);
        timeSlotSuccess.put("12:00-18:00", 0);
        timeSlotSuccess.put("18:00-24:00", 0);

        List<Long> executionTimes = new ArrayList<>();

        for (ReportDO report : perReports) {
            if (report.getStartTime() != null) {
                int hour = report.getStartTime().getHour();
                String timeSlot = getTimeSlot(hour);

                timeSlotCounts.merge(timeSlot, 1, Integer::sum);
                if ("SUCCESS".equals(report.getExecutionStatus().name())) {
                    timeSlotSuccess.merge(timeSlot, 1, Integer::sum);
                }
            }

            if (report.getExecutionDuration() != null) {
                executionTimes.add(report.getExecutionDuration() / 1000); // 转换为秒
            }
        }

        // 构建时间段统计
        List<DailyReportContent.ExecutionTimeDistribution.TimeSlotStatistics> timeSlots =
                timeSlotCounts.entrySet().stream()
                        .map(entry -> {
                            String slot = entry.getKey();
                            Integer count = entry.getValue();
                            Integer success = timeSlotSuccess.get(slot);
                            Integer passRate = count > 0 ? (success * 100) / count : 0;

                            return DailyReportContent.ExecutionTimeDistribution.TimeSlotStatistics.builder()
                                    .timeSlot(slot)
                                    .taskCount(count)
                                    .passRate(passRate)
                                    .build();
                        })
                        .collect(Collectors.toList());

        // 计算执行时间统计
        Double avgTime = executionTimes.isEmpty() ? 0.0 :
                executionTimes.stream().mapToLong(Long::longValue).average().orElse(0.0);
        Long maxTime = executionTimes.isEmpty() ? 0L : Collections.max(executionTimes);
        Long minTime = executionTimes.isEmpty() ? 0L : Collections.min(executionTimes);

        return DailyReportContent.ExecutionTimeDistribution.builder()
                .timeSlots(timeSlots)
                .averageExecutionTime(avgTime)
                .maxExecutionTime(maxTime)
                .minExecutionTime(minTime)
                .build();
    }

    /**
     * 获取时间段
     */
    private String getTimeSlot(int hour) {
        if (hour >= 0 && hour < 6) return "00:00-06:00";
        if (hour >= 6 && hour < 12) return "06:00-12:00";
        if (hour >= 12 && hour < 18) return "12:00-18:00";
        return "18:00-24:00";
    }

    /**
     * 构建任务类型分布（简化实现）
     */
    private DailyReportContent.TaskTypeDistribution buildTaskTypeDistribution(List<ReportDO> perReports) {
        Map<String, List<ReportDO>> taskTypeGroups = perReports.stream()
                .collect(Collectors.groupingBy(report ->
                        report.getTaskName() != null ? report.getTaskName() : "未知类型"));

        List<DailyReportContent.TaskTypeDistribution.TaskTypeStatistics> taskTypes =
                taskTypeGroups.entrySet().stream()
                        .map(entry -> {
                            String taskType = entry.getKey();
                            List<ReportDO> reports = entry.getValue();

                            int taskCount = reports.size();
                            long successCount = reports.stream()
                                    .filter(r -> "SUCCESS".equals(r.getExecutionStatus().name()))
                                    .count();
                            int passRate = taskCount > 0 ? (int) ((successCount * 100) / taskCount) : 0;

                            double avgExecutionTime = reports.stream()
                                    .filter(r -> r.getExecutionDuration() != null)
                                    .mapToLong(r -> r.getExecutionDuration() / 1000)
                                    .average()
                                    .orElse(0.0);

                            return DailyReportContent.TaskTypeDistribution.TaskTypeStatistics.builder()
                                    .taskType(taskType)
                                    .taskTypeName(taskType)
                                    .taskCount(taskCount)
                                    .passRate(passRate)
                                    .avgExecutionTime(avgExecutionTime)
                                    .build();
                        })
                        .collect(Collectors.toList());

        return DailyReportContent.TaskTypeDistribution.builder()
                .taskTypes(taskTypes)
                .build();
    }

    /**
     * 构建异常汇总（简化实现）
     */
    private List<DailyReportContent.ExceptionSummary> buildExceptionSummaries(List<ReportDO> perReports) {
        List<DailyReportContent.ExceptionSummary> summaries = new ArrayList<>();

        int warningCount = 0;
        Set<String> affectedResources = new HashSet<>();

        for (ReportDO report : perReports) {
            if (report.getContent() != null && report.getContent().getExceptionDetails() != null) {
                warningCount += report.getContent().getExceptionDetails().size();

                report.getContent().getExceptionDetails().forEach(exception -> {
                    if (exception.getResourceName() != null) {
                        affectedResources.add(exception.getResourceName());
                    }
                });
            }
        }

        if (warningCount > 0) {
            DailyReportContent.ExceptionSummary.ExceptionTypeCount typeCount =
                    DailyReportContent.ExceptionSummary.ExceptionTypeCount.builder()
                            .exceptionType("配置异常")
                            .count(warningCount)
                            .typicalDescription("巡检规则执行失败")
                            .build();

            DailyReportContent.ExceptionSummary summary = DailyReportContent.ExceptionSummary.builder()
                    .level("warning")
                    .levelName("中危")
                    .count(warningCount)
                    .exceptionTypes(List.of(typeCount))
                    .affectedResources(new ArrayList<>(affectedResources))
                    .build();

            summaries.add(summary);
        }

        return summaries;
    }

    /**
     * 构建资源统计（简化实现）
     */
    private List<DailyReportContent.ResourceStatistics> buildResourceStatistics(List<ReportDO> perReports) {
        Map<String, List<ReportDO>> resourceGroups = new HashMap<>();

        for (ReportDO report : perReports) {
            if (report.getContent() != null && report.getContent().getRuleCheckDetails() != null) {
                report.getContent().getRuleCheckDetails().forEach(detail -> {
                    String resourceName = detail.getResourceName() != null ? detail.getResourceName() : "未知资源";
                    resourceGroups.computeIfAbsent(resourceName, k -> new ArrayList<>()).add(report);
                });
            }
        }

        return resourceGroups.entrySet().stream()
                .map(entry -> {
                    String resourceName = entry.getKey();
                    List<ReportDO> reports = entry.getValue();

                    int totalChecks = reports.size();
                    long successCount = reports.stream()
                            .filter(r -> "SUCCESS".equals(r.getExecutionStatus().name()))
                            .count();
                    int failedCount = totalChecks - (int) successCount;
                    int passRate = totalChecks > 0 ? (int) ((successCount * 100) / totalChecks) : 0;

                    return DailyReportContent.ResourceStatistics.builder()
                            .resourceId(resourceName)
                            .resourceName(resourceName)
                            .resourceType("HOST")
                            .totalChecks(totalChecks)
                            .successCount((int) successCount)
                            .failedCount(failedCount)
                            .passRate(passRate)
                            .exceptionCount(failedCount)
                            .build();
                })
                .collect(Collectors.toList());
    }

    /**
     * 构建趋势对比（简化实现）
     */
    private DailyReportContent.TrendComparison buildTrendComparison(LocalDate reportDate) {
        // 这里可以查询历史数据进行对比，暂时返回空对象
        return DailyReportContent.TrendComparison.builder()
                .recentTrend(new ArrayList<>())
                .build();
    }

    /**
     * 构建按次报告汇总
     */
    private List<DailyReportContent.PerReportSummary> buildPerReportSummaries(List<ReportDO> perReports) {
        return perReports.stream()
                .map(report -> {
                    int totalChecks = 0;
                    int exceptionCount = 0;
                    int passRate = 0;

                    if (report.getContent() != null) {
                        if (report.getContent().getExecutionDistribution() != null) {
                            totalChecks = report.getContent().getExecutionDistribution().getTotal();
                            passRate = report.getContent().getExecutionDistribution().getSuccessRate();
                        }
                        if (report.getContent().getExceptionDetails() != null) {
                            exceptionCount = report.getContent().getExceptionDetails().size();
                        }
                    }

                    return DailyReportContent.PerReportSummary.builder()
                            .reportId(report.getReportId())
                            .taskId(report.getTaskId())
                            .taskName(report.getTaskName())
                            .executionTime(report.getStartTime())
                            .executionStatus(report.getExecutionStatus().name())
                            .passRate(passRate)
                            .totalChecks(totalChecks)
                            .exceptionCount(exceptionCount)
                            .executionDuration(report.getExecutionDuration() != null ? report.getExecutionDuration() / 1000 : 0L)
                            .build();
                })
                .collect(Collectors.toList());
    }
