package com.cmpay.hacp.inspection.application.service;

import com.cmpay.hacp.inspection.infrastructure.database.dataobject.DailyReportDO;

import java.time.LocalDate;
import java.util.List;

/**
 * 按日报告生成服务接口
 * 负责按日巡检报告的生成、调度和管理
 */
public interface DailyReportGenerationService {

    /**
     * 生成指定日期的按日报告
     * 
     * @param reportDate 报告日期
     * @param triggerType 触发类型（0:定时触发 1:手动触发 2:补偿触发）
     * @return 生成的报告对象
     */
    DailyReportDO generateDailyReport(LocalDate reportDate, Integer triggerType);

    /**
     * 异步生成指定日期的按日报告
     * 
     * @param reportDate 报告日期
     * @param triggerType 触发类型
     * @return 任务ID
     */
    String generateDailyReportAsync(LocalDate reportDate, Integer triggerType);

    /**
     * 批量生成指定日期范围的按日报告
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param triggerType 触发类型
     * @return 生成的任务ID列表
     */
    List<String> batchGenerateDailyReports(LocalDate startDate, LocalDate endDate, Integer triggerType);

    /**
     * 重新生成指定日期的按日报告
     * 
     * @param reportDate 报告日期
     * @param force 是否强制重新生成（忽略已存在的报告）
     * @return 生成的报告对象
     */
    DailyReportDO regenerateDailyReport(LocalDate reportDate, boolean force);

    /**
     * 检查并补偿缺失的按日报告
     * 扫描指定日期范围内缺失的报告并自动生成
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 补偿生成的报告数量
     */
    int compensateMissingReports(LocalDate startDate, LocalDate endDate);

    /**
     * 定时任务：生成昨日的按日报告
     * 每日凌晨自动执行
     */
    void scheduleGenerateYesterdayReport();

    /**
     * 定时任务：检查并重试失败的报告生成任务
     * 每小时执行一次
     */
    void scheduleRetryFailedTasks();

    /**
     * 定时任务：清理过期的生成任务记录
     * 每日执行一次
     */
    void scheduleCleanupExpiredTasks();

    /**
     * 获取报告生成进度
     * 
     * @param taskId 任务ID
     * @return 进度信息
     */
    GenerationProgress getGenerationProgress(String taskId);

    /**
     * 取消正在执行的报告生成任务
     * 
     * @param taskId 任务ID
     * @return 是否取消成功
     */
    boolean cancelGenerationTask(String taskId);

    /**
     * 验证按日报告数据的完整性和准确性
     * 
     * @param reportDate 报告日期
     * @return 验证结果
     */
    ValidationResult validateDailyReport(LocalDate reportDate);

    /**
     * 报告生成进度信息
     */
    class GenerationProgress {
        private String taskId;
        private LocalDate reportDate;
        private String status;
        private Integer processedReports;
        private Integer totalReports;
        private Integer progressPercentage;
        private String currentStep;
        private String errorMessage;
        
        // getters and setters
        public String getTaskId() { return taskId; }
        public void setTaskId(String taskId) { this.taskId = taskId; }
        
        public LocalDate getReportDate() { return reportDate; }
        public void setReportDate(LocalDate reportDate) { this.reportDate = reportDate; }
        
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        
        public Integer getProcessedReports() { return processedReports; }
        public void setProcessedReports(Integer processedReports) { this.processedReports = processedReports; }
        
        public Integer getTotalReports() { return totalReports; }
        public void setTotalReports(Integer totalReports) { this.totalReports = totalReports; }
        
        public Integer getProgressPercentage() { return progressPercentage; }
        public void setProgressPercentage(Integer progressPercentage) { this.progressPercentage = progressPercentage; }
        
        public String getCurrentStep() { return currentStep; }
        public void setCurrentStep(String currentStep) { this.currentStep = currentStep; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    }

    /**
     * 验证结果
     */
    class ValidationResult {
        private boolean valid;
        private List<String> issues;
        private String summary;
        
        // getters and setters
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }
        
        public List<String> getIssues() { return issues; }
        public void setIssues(List<String> issues) { this.issues = issues; }
        
        public String getSummary() { return summary; }
        public void setSummary(String summary) { this.summary = summary; }
    }
}
