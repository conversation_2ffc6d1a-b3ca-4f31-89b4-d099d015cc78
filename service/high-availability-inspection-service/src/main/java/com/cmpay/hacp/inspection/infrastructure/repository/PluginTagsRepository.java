package com.cmpay.hacp.inspection.infrastructure.repository;

import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.PluginTagsDO;
import com.cmpay.hacp.inspection.infrastructure.mapper.PluginTagsMapper;
import org.springframework.stereotype.Repository;

/**
 * 插件标签关联Repository
 */
@Repository
public class PluginTagsRepository extends CrudRepository<PluginTagsMapper, PluginTagsDO> {
}
