package com.cmpay.hacp.inspection.application.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.ReportDO;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 按日巡检报告服务接口
 * 负责生成、汇总和查询按日巡检报告数据
 */
public interface DailyInspectionReportService {

    /**
     * 获取指定日期的巡检报告汇总
     * 
     * @param date 巡检日期
     * @return 日报告汇总数据
     */
    Map<String, Object> getDailyReportSummary(LocalDate date);

    /**
     * 获取日期范围内的巡检报告列表
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param page 分页参数
     * @return 分页的日报告列表
     */
    IPage<Map<String, Object>> getDailyReportList(LocalDate startDate, LocalDate endDate, IPage<?> page);

    /**
     * 获取指定日期的巡检报告详情
     * 
     * @param date 巡检日期
     * @return 日报告详情数据
     */
    Map<String, Object> getDailyReportDetail(LocalDate date);

    /**
     * 获取近期通过率趋势数据
     * 
     * @param endDate 结束日期
     * @param days 天数（7天或30天）
     * @return 趋势数据
     */
    Map<String, Object> getPassRateTrend(LocalDate endDate, int days);

    /**
     * 生成指定日期的按日汇总报告
     * 该方法会汇总当日所有按次报告数据
     * 
     * @param date 日期
     * @return 是否生成成功
     */
    boolean generateDailyReport(LocalDate date);

    /**
     * 获取指定日期的所有按次报告
     * 
     * @param date 日期
     * @return 按次报告列表
     */
    List<ReportDO> getPerInspectionReportsByDate(LocalDate date);

    /**
     * 导出指定日期的巡检报告
     * 
     * @param date 巡检日期
     * @param format 导出格式（PDF、EXCEL、HTML等）
     * @return 导出结果
     */
    Map<String, Object> exportDailyReport(LocalDate date, String format);

    /**
     * 导出日期范围内的巡检报告
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param format 导出格式
     * @return 导出结果
     */
    Map<String, Object> exportDailyReportRange(LocalDate startDate, LocalDate endDate, String format);
}
