package com.cmpay.hacp.inspection.application.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.DailyReportDO;

import java.time.LocalDate;
import java.util.Map;

/**
 * 按日巡检报告查询服务接口
 * 负责查询和展示按日巡检报告数据（基于已生成的按日汇总表）
 *
 * 注意：报告生成功能由 DailyReportGenerationService 负责
 */
public interface DailyInspectionReportService {

    /**
     * 获取指定日期的巡检报告汇总
     * 从按日汇总表中查询数据
     *
     * @param date 巡检日期
     * @return 日报告汇总数据
     */
    Map<String, Object> getDailyReportSummary(LocalDate date);

    /**
     * 获取日期范围内的巡检报告列表
     * 从按日汇总表中分页查询数据
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param page 分页参数
     * @return 分页的日报告列表
     */
    IPage<Map<String, Object>> getDailyReportList(LocalDate startDate, LocalDate endDate, IPage<?> page);

    /**
     * 获取指定日期的巡检报告详情
     * 包含异常汇总、异常详情等详细信息
     *
     * @param date 巡检日期
     * @return 日报告详情数据
     */
    Map<String, Object> getDailyReportDetail(LocalDate date);

    /**
     * 获取近期通过率趋势数据
     * 基于按日汇总表生成趋势图数据
     *
     * @param endDate 结束日期
     * @param days 天数（7天或30天）
     * @return 趋势数据
     */
    Map<String, Object> getPassRateTrend(LocalDate endDate, int days);

    /**
     * 获取按日报告实体对象
     *
     * @param date 巡检日期
     * @return 按日报告实体，如果不存在则返回null
     */
    DailyReportDO getDailyReport(LocalDate date);

    /**
     * 检查指定日期的报告是否存在且已完成
     *
     * @param date 巡检日期
     * @return 是否存在且已完成
     */
    boolean isDailyReportAvailable(LocalDate date);

    /**
     * 导出指定日期的巡检报告
     *
     * @param date 巡检日期
     * @param format 导出格式（PDF、EXCEL、HTML等）
     * @return 导出结果
     */
    Map<String, Object> exportDailyReport(LocalDate date, String format);

    /**
     * 导出日期范围内的巡检报告
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param format 导出格式
     * @return 导出结果
     */
    Map<String, Object> exportDailyReportRange(LocalDate startDate, LocalDate endDate, String format);
}
