package com.cmpay.hacp.inspection.infrastructure.database.dataobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 按日巡检报告详细内容结构化数据类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DailyReportContent {
    
    /**
     * 执行时间分布统计
     */
    private ExecutionTimeDistribution timeDistribution;
    
    /**
     * 任务类型分布统计
     */
    private TaskTypeDistribution taskTypeDistribution;
    
    /**
     * 异常详情汇总
     */
    private List<ExceptionSummary> exceptionSummaries;
    
    /**
     * 资源维度统计
     */
    private List<ResourceStatistics> resourceStatistics;
    
    /**
     * 趋势对比数据（与前一日、前一周对比）
     */
    private TrendComparison trendComparison;
    
    /**
     * 按次报告汇总列表
     */
    private List<PerReportSummary> perReportSummaries;
    
    /**
     * 执行时间分布
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExecutionTimeDistribution {
        /**
         * 时间段统计
         */
        private List<TimeSlotStatistics> timeSlots;
        
        /**
         * 平均执行时间（秒）
         */
        private Double averageExecutionTime;
        
        /**
         * 最长执行时间（秒）
         */
        private Long maxExecutionTime;
        
        /**
         * 最短执行时间（秒）
         */
        private Long minExecutionTime;
        
        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class TimeSlotStatistics {
            /**
             * 时间段（如：00:00-06:00）
             */
            private String timeSlot;
            
            /**
             * 该时间段的任务数量
             */
            private Integer taskCount;
            
            /**
             * 该时间段的通过率
             */
            private Integer passRate;
        }
    }
    
    /**
     * 任务类型分布
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TaskTypeDistribution {
        /**
         * 各类型任务统计
         */
        private List<TaskTypeStatistics> taskTypes;
        
        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class TaskTypeStatistics {
            /**
             * 任务类型
             */
            private String taskType;
            
            /**
             * 任务类型名称
             */
            private String taskTypeName;
            
            /**
             * 任务数量
             */
            private Integer taskCount;
            
            /**
             * 通过率
             */
            private Integer passRate;
            
            /**
             * 平均执行时间
             */
            private Double avgExecutionTime;
        }
    }
    
    /**
     * 异常汇总
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExceptionSummary {
        /**
         * 异常级别
         */
        private String level;
        
        /**
         * 异常级别名称
         */
        private String levelName;
        
        /**
         * 异常数量
         */
        private Integer count;
        
        /**
         * 异常类型分布
         */
        private List<ExceptionTypeCount> exceptionTypes;
        
        /**
         * 涉及的资源列表
         */
        private List<String> affectedResources;
        
        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class ExceptionTypeCount {
            /**
             * 异常类型
             */
            private String exceptionType;
            
            /**
             * 数量
             */
            private Integer count;
            
            /**
             * 典型异常描述
             */
            private String typicalDescription;
        }
    }
    
    /**
     * 资源维度统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ResourceStatistics {
        /**
         * 资源ID
         */
        private String resourceId;
        
        /**
         * 资源名称
         */
        private String resourceName;
        
        /**
         * 资源类型
         */
        private String resourceType;
        
        /**
         * 检查总数
         */
        private Integer totalChecks;
        
        /**
         * 成功数
         */
        private Integer successCount;
        
        /**
         * 失败数
         */
        private Integer failedCount;
        
        /**
         * 通过率
         */
        private Integer passRate;
        
        /**
         * 异常数量
         */
        private Integer exceptionCount;
    }
    
    /**
     * 趋势对比
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TrendComparison {
        /**
         * 与前一日对比
         */
        private DayComparison previousDay;
        
        /**
         * 与前一周同期对比
         */
        private DayComparison previousWeek;
        
        /**
         * 近7日趋势数据
         */
        private List<DailyTrendPoint> recentTrend;
        
        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class DayComparison {
            /**
             * 对比日期
             */
            private LocalDate compareDate;
            
            /**
             * 通过率变化（百分点）
             */
            private Integer passRateChange;
            
            /**
             * 总检查数变化
             */
            private Integer totalChecksChange;
            
            /**
             * 异常数变化
             */
            private Integer exceptionCountChange;
            
            /**
             * 变化趋势（上升、下降、持平）
             */
            private String trend;
        }
        
        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class DailyTrendPoint {
            /**
             * 日期
             */
            private LocalDate date;
            
            /**
             * 通过率
             */
            private Integer passRate;
            
            /**
             * 总检查数
             */
            private Integer totalChecks;
            
            /**
             * 异常数
             */
            private Integer exceptionCount;
        }
    }
    
    /**
     * 按次报告汇总
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PerReportSummary {
        /**
         * 按次报告ID
         */
        private String reportId;
        
        /**
         * 任务ID
         */
        private String taskId;
        
        /**
         * 任务名称
         */
        private String taskName;
        
        /**
         * 执行时间
         */
        private LocalDateTime executionTime;
        
        /**
         * 执行状态
         */
        private String executionStatus;
        
        /**
         * 通过率
         */
        private Integer passRate;
        
        /**
         * 检查总数
         */
        private Integer totalChecks;
        
        /**
         * 异常数
         */
        private Integer exceptionCount;
        
        /**
         * 执行耗时（秒）
         */
        private Long executionDuration;
    }
}
