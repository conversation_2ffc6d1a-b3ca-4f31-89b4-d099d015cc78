package com.cmpay.hacp.inspection.domain.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 报告状态枚举
 */
@Getter
@AllArgsConstructor
public enum ReportStatus {
    
    /**
     * 生成中
     */
    GENERATING(0, "生成中"),
    
    /**
     * 已完成
     */
    COMPLETED(1, "已完成"),
    
    /**
     * 生成失败
     */
    FAILED(2, "生成失败"),
    
    /**
     * 部分完成（部分数据缺失但可用）
     */
    PARTIAL(3, "部分完成");
    
    @EnumValue
    private final Integer code;
    
    @JsonValue
    private final String description;
    
    /**
     * 根据代码获取枚举
     */
    public static ReportStatus fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ReportStatus status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown ReportStatus code: " + code);
    }
}
