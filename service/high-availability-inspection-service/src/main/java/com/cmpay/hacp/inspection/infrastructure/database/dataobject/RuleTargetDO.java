package com.cmpay.hacp.inspection.infrastructure.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.cmpay.hacp.inspection.domain.model.enums.RuleAngleViewEnum;
import com.cmpay.hacp.inspection.domain.model.enums.RuleDeployEnvEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 巡检规则目标范围表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("rule_target")
public class RuleTargetDO extends BaseDO {
    /**
     * 规则ID
     */
    private String ruleId;

    /**
     * 功能视角
     * {@link RuleAngleViewEnum}
     */
    private Integer angleView;

    /**
     * 部署环境
     * {@link RuleDeployEnvEnum}
     */
    private Integer deployEnv;

}
