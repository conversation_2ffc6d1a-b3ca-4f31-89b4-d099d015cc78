package com.cmpay.hacp.inspection.application.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cmpay.hacp.inspection.application.service.RuleMatchingService;
import com.cmpay.hacp.inspection.application.service.ScriptResultParserService;
import com.cmpay.hacp.inspection.domain.model.enums.RuleComparisonOperator;
import com.cmpay.hacp.inspection.domain.model.rule.RuleMatchingResult;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.PluginScriptOutputFieldDO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.RuleConditionDO;
import com.cmpay.hacp.inspection.infrastructure.repository.PluginScriptOutputFiledRepository;
import com.cmpay.hacp.inspection.infrastructure.repository.RuleConditionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 规则匹配服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RuleMatchingServiceImpl implements RuleMatchingService {

    private final ScriptResultParserService scriptResultParserService;
    private final RuleConditionRepository ruleConditionRepository;
    private final PluginScriptOutputFiledRepository pluginScriptOutputFiledRepository;

    @Override
    public RuleMatchingResult matchRule(String ruleId, String pluginId, String scriptOutput) {
        try {
            // 获取规则条件
            RuleConditionDO ruleCondition = ruleConditionRepository.getOne(
                    Wrappers.lambdaQuery(RuleConditionDO.class)
                            .eq(RuleConditionDO::getRuleId, ruleId)
                            .eq(RuleConditionDO::getPluginId, pluginId)
            );

            if (ruleCondition == null) {
                return RuleMatchingResult.builder()
                        .success(false)
                        .ruleId(ruleId)
                        .pluginId(pluginId)
                        .errorMessage("Rule condition not found for ruleId: " + ruleId + ", pluginId: " + pluginId)
                        .build();
            }

            // 获取字段定义
            PluginScriptOutputFieldDO fieldDefinition = pluginScriptOutputFiledRepository.getById(ruleCondition.getOutputFiledId());
            if (fieldDefinition == null) {
                return RuleMatchingResult.builder()
                        .success(false)
                        .ruleId(ruleId)
                        .pluginId(pluginId)
                        .errorMessage("Field definition not found for pluginResultId: " + ruleCondition.getOutputFiledId())
                        .build();
            }

            // 获取所有字段定义用于解析
            List<PluginScriptOutputFieldDO> allFieldDefinitions = pluginScriptOutputFiledRepository.list(
                    Wrappers.lambdaQuery(PluginScriptOutputFieldDO.class)
                            .eq(PluginScriptOutputFieldDO::getPluginId, pluginId)
            );

            // 解析脚本输出
            Map<String, Object> parsedValues = scriptResultParserService.parseScriptOutput(scriptOutput, allFieldDefinitions);

            // 匹配规则条件
            return matchParsedValues(parsedValues, ruleCondition, fieldDefinition);

        } catch (Exception e) {
            log.error("Error matching rule: ruleId={}, pluginId={}", ruleId, pluginId, e);
            return RuleMatchingResult.builder()
                    .success(false)
                    .ruleId(ruleId)
                    .pluginId(pluginId)
                    .errorMessage("Rule matching error: " + e.getMessage())
                    .build();
        }
    }

    @Override
    public RuleMatchingResult matchParsedValues(Map<String, Object> parsedValues,
                                              RuleConditionDO ruleCondition,
                                              PluginScriptOutputFieldDO fieldDefinition) {

        String fieldName = fieldDefinition.getFieldName();
        Object actualValue = parsedValues.get(fieldName);

        // 检查字段值是否存在
        if (actualValue == null) {
            return RuleMatchingResult.builder()
                    .success(false)
                    .ruleId(ruleCondition.getRuleId())
                    .pluginId(ruleCondition.getPluginId())
                    .fieldName(fieldName)
                    .errorMessage("Field value not found in script output: " + fieldName)
                    .build();
        }

        // 评估字段条件
        boolean conditionMet = evaluateFieldCondition(actualValue, ruleCondition);

        // 构建匹配详情
        RuleMatchingResult.FieldMatchingDetail detail = RuleMatchingResult.FieldMatchingDetail.builder()
                .fieldName(fieldName)
                .actualValue(actualValue)
                .expectedValue(ruleCondition.getComparisonValue())
                .condition(buildConditionMessage(ruleCondition.getComparisonOperator()))
                .matched(conditionMet)
                .message(buildFieldMatchingMessage(actualValue, ruleCondition, conditionMet))
                .build();

        return RuleMatchingResult.builder()
                .success(conditionMet)
                .ruleId(ruleCondition.getRuleId())
                .pluginId(ruleCondition.getPluginId())
                .fieldName(fieldName)
                .actualValue(actualValue)
                .expectedValue(ruleCondition.getComparisonValue())
                .condition(buildConditionMessage(ruleCondition.getComparisonOperator()))
                .message(buildRuleMatchingMessage(conditionMet, fieldName, actualValue, ruleCondition))
                .suggestion(ruleCondition.getSuggestion())
                .fieldMatchingDetails(Collections.singletonList(detail))
                .build();
    }

    @Override
    public boolean evaluateFieldCondition(Object fieldValue, RuleConditionDO ruleCondition) {
        try {
            // 转换字段值为BigDecimal进行数值比较
            BigDecimal actualValue = convertToBigDecimal(fieldValue);
            BigDecimal expectedValue = ruleCondition.getComparisonValue();
            RuleComparisonOperator operator = ruleCondition.getComparisonOperator();

            if (actualValue == null || expectedValue == null || operator == null) {
                log.warn("Invalid values for condition evaluation: actual={}, expected={}, judge={}",
                        actualValue, expectedValue, operator);
                return false;
            }

            int result = actualValue.compareTo(expectedValue);

            switch (operator) {
                case GREATER_THAN:
                    return result > 0;
                case LESS_THAN:
                    return result < 0;
                case GREATER_THAN_OR_EQUAL:
                    return result >= 0;
                case LESS_THAN_OR_EQUAL:
                    return result <= 0;
                case EQUAL:
                    return result == 0;
                default:
                    log.warn("Unsupported comparison operator: {}", operator);
                    return false;
            }

        } catch (Exception e) {
            log.error("Error evaluating field condition: fieldValue={}, ruleCondition={}", fieldValue, ruleCondition, e);
            return false;
        }
    }

    /**
     * 转换值为BigDecimal
     */
    private BigDecimal convertToBigDecimal(Object value) {
        if (value == null) {
            return null;
        }

        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        }

        if (value instanceof Number) {
            return BigDecimal.valueOf(((Number) value).doubleValue());
        }

        try {
            String stringValue = value.toString().trim();
            // 移除可能的单位符号
            String cleanValue = stringValue.replaceAll("[^0-9.-]", "");
            return new BigDecimal(cleanValue);
        } catch (NumberFormatException e) {
            log.warn("Cannot convert value to BigDecimal: {}", value);
            return null;
        }
    }

    /**
     * 获取判断条件描述
     */
    private String buildConditionMessage(RuleComparisonOperator comparisonOperator) {
        return comparisonOperator != null ? comparisonOperator.getDesc() : "未知条件";
    }

    /**
     * 构建字段匹配消息
     */
    private String buildFieldMatchingMessage(Object actualValue, RuleConditionDO ruleCondition, boolean matched) {
        String condition = buildConditionMessage(ruleCondition.getComparisonOperator());
        String status = matched ? "满足" : "不满足";

        return String.format("字段值 %s %s %s，条件%s",
                actualValue, condition, ruleCondition.getComparisonValue(), status);
    }

    /**
     * 构建规则匹配消息
     */
    private String buildRuleMatchingMessage(boolean success, String fieldName, Object actualValue, RuleConditionDO ruleCondition) {
        String status = success ? "通过" : "失败";
        String condition = buildConditionMessage(ruleCondition.getComparisonOperator());

        return String.format("巡检规则%s：字段 %s 实际值 %s，期望 %s %s，结果：%s",
                status, fieldName, actualValue, condition, ruleCondition.getComparisonValue(), status);
    }
}
