package com.cmpay.hacp.inspection.domain.model.task;

import com.cmpay.hacp.inspection.domain.model.enums.ScheduleType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalTime;

/**
 * 固定时间调度配置
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FixedTimeScheduleConfig extends ScheduleConfig {
    private LocalDate executionDate;
    private LocalTime executionTime;

    @Override
    public ScheduleType getType() {
        return ScheduleType.FIXED_TIME;
    }

    @Override
    public boolean isValid() {
        return executionDate != null && executionTime != null;
    }
}
