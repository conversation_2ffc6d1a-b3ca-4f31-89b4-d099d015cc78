package com.cmpay.hacp.inspection.infrastructure.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.cmpay.hacp.inspection.domain.model.enums.ExecutionStatus;
import com.cmpay.hacp.inspection.domain.model.enums.TriggerMode;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 巡检报告表 - inspection_report
 * 基于任务执行记录生成的分析报告
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("inspection_report")
public class ReportDO extends BaseDO {
    /**
     * 关联的任务执行记录ID
     */
    private Long taskExecutionId;

    /**
     * 报告编号（唯一标识，如：RPT-202503240003）
     */
    private String reportId;

    /**
     * 任务ID（用于关联任务信息）
     */
    private String taskId;

    /**
     * 任务名称（如：网络连通性检测）
     */
    private String taskName;

    /**
     * 触发方式 0:定时触发 1:手动触发
     */
    private TriggerMode triggerMode;

    /**
     * 执行状态（成功、告警、失败、执行中）
     */
    private ExecutionStatus executionStatus;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 执行耗时（毫秒）
     */
    private Long executionDuration;

    /**
     * 执行耗时（格式化字符串，如：42秒）
     */
    private String executionTimeStr;

    /**
     * 执行结果统计（JSON格式：{"passed": 3, "warning": 1, "failed": 2}）
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private String resultStats;

    /**
     * 报告生成时间
     */
    private LocalDateTime generateTime;

    /**
     * 报告详细内容（JSON格式存储结构化数据）
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private ReportContent content;
}
