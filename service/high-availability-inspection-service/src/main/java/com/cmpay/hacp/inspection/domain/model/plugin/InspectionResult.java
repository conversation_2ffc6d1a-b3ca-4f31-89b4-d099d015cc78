package com.cmpay.hacp.inspection.domain.model.plugin;

import com.cmpay.hacp.inspection.domain.model.rule.RuleMatchingResult;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class InspectionResult {
    private boolean success;
    private String message;
    private String details;
    private String pluginName;

    /**
     * 规则匹配结果
     */
    private RuleMatchingResult ruleMatchingResult;

    /**
     * 脚本执行是否成功（技术层面）
     */
    private boolean scriptExecutionSuccess;

    /**
     * 规则匹配是否成功（业务层面）
     */
    private boolean ruleMatchingSuccess;
}
