package com.cmpay.hacp.inspection.infrastructure.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.cmpay.hacp.inspection.domain.model.enums.ReportStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 按日巡检汇总报告表 - daily_inspection_report
 * 基于按次巡检报告汇总生成的日级别分析报告
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("daily_inspection_report")
public class DailyReportDO extends BaseDO {
    
    /**
     * 报告日期（YYYY-MM-DD）
     */
    private LocalDate reportDate;
    
    /**
     * 日报告编号（唯一标识，如：DAILY-20250124）
     */
    private String dailyReportId;
    
    /**
     * 报告状态（生成中、已完成、失败）
     */
    private ReportStatus reportStatus;
    
    /**
     * 汇总的按次报告数量
     */
    private Integer perReportCount;
    
    /**
     * 汇总的任务数量
     */
    private Integer taskCount;
    
    /**
     * 汇总的规则总数
     */
    private Integer totalRules;
    
    /**
     * 汇总的检查总数
     */
    private Integer totalChecks;
    
    /**
     * 成功检查数
     */
    private Integer successChecks;
    
    /**
     * 告警检查数
     */
    private Integer warningChecks;
    
    /**
     * 失败检查数
     */
    private Integer failedChecks;
    
    /**
     * 整体通过率（百分比）
     */
    private Integer overallPassRate;
    
    /**
     * 成功任务数
     */
    private Integer successTasks;
    
    /**
     * 告警任务数
     */
    private Integer warningTasks;
    
    /**
     * 失败任务数
     */
    private Integer failedTasks;
    
    /**
     * 高危异常数量
     */
    private Integer criticalExceptions;
    
    /**
     * 中危异常数量
     */
    private Integer warningExceptions;
    
    /**
     * 低危异常数量
     */
    private Integer infoExceptions;
    
    /**
     * 报告生成开始时间
     */
    private LocalDateTime generateStartTime;
    
    /**
     * 报告生成完成时间
     */
    private LocalDateTime generateEndTime;
    
    /**
     * 报告生成耗时（毫秒）
     */
    private Long generateDuration;
    
    /**
     * 详细统计数据（JSON格式存储）
     * 包含：执行时间分布、异常详情汇总、趋势对比等
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private DailyReportContent content;
    
    /**
     * 数据版本号（用于并发控制和增量更新）
     */
    private Integer dataVersion;
    
    /**
     * 备注信息
     */
    private String remarks;
}
