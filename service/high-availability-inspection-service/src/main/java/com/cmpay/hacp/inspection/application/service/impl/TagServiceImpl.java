package com.cmpay.hacp.inspection.application.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cmpay.hacp.inspection.application.assembler.TagMapper;
import com.cmpay.hacp.inspection.application.service.TagService;
import com.cmpay.hacp.inspection.domain.model.common.Tag;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.TagDO;
import com.cmpay.hacp.inspection.infrastructure.repository.TagsRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 标签服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TagServiceImpl implements TagService {

    private final TagsRepository tagsRepository;
    private final TagMapper tagMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createTag(String name) {
        // 保存标签信息
        TagDO tagDO = new TagDO();
        tagDO.setName(name);
        tagsRepository.save(tagDO);

        return tagDO.getId();
    }

    @Override
    public List<Tag> getTagByTagIds(List<Long> tagIds) {
        List<TagDO> tagDOS = tagsRepository.list(
                Wrappers.lambdaQuery(TagDO.class)
                        .in(TagDO::getId, tagIds));
        if (CollectionUtils.isEmpty(tagDOS)) {
            return new ArrayList<>();
        }
        return tagMapper.toTagList(tagDOS);
    }
}
