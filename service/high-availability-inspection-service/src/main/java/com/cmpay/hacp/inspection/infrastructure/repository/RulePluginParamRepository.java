package com.cmpay.hacp.inspection.infrastructure.repository;

import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.RulePluginParamDO;
import com.cmpay.hacp.inspection.infrastructure.mapper.RulePluginParamMapper;
import org.springframework.stereotype.Repository;

@Repository
public class RulePluginParamRepository extends CrudRepository<RulePluginParamMapper, RulePluginParamDO> {
}
