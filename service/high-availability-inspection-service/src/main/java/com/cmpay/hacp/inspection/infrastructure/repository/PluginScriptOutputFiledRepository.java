package com.cmpay.hacp.inspection.infrastructure.repository;

import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.PluginScriptOutputFieldDO;
import com.cmpay.hacp.inspection.infrastructure.mapper.PluginScriptOutputFiledMapper;
import org.springframework.stereotype.Repository;

/**
 * 插件脚本结构化输出Repository
 */
@Repository
public class PluginScriptOutputFiledRepository extends CrudRepository<PluginScriptOutputFiledMapper, PluginScriptOutputFieldDO> {
}
