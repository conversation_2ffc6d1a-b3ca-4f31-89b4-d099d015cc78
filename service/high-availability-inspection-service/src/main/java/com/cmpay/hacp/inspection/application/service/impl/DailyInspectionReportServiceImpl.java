package com.cmpay.hacp.inspection.application.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.hacp.inspection.application.service.DailyInspectionReportService;
import com.cmpay.hacp.inspection.domain.model.enums.ReportStatus;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.DailyReportContent;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.DailyReportDO;
import com.cmpay.hacp.inspection.infrastructure.repository.DailyReportRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 按日巡检报告查询服务实现
 * 基于按日汇总表提供查询功能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DailyInspectionReportServiceImpl implements DailyInspectionReportService {

    private final DailyReportRepository dailyReportRepository;

    @Override
    public Map<String, Object> getDailyReportSummary(LocalDate date) {
        log.info("Getting daily report summary for date: {}", date);

        DailyReportDO dailyReport = getDailyReport(date);

        if (dailyReport == null) {
            return buildEmptyDailySummary(date);
        }

        return buildDailySummaryFromEntity(dailyReport);
    }

    @Override
    public IPage<Map<String, Object>> getDailyReportList(LocalDate startDate, LocalDate endDate, IPage<?> page) {
        log.info("Getting daily report list from {} to {}", startDate, endDate);

        // 从按日汇总表中分页查询
        IPage<DailyReportDO> dailyReportPage = dailyReportRepository.page(
                new Page<>(page.getCurrent(), page.getSize()),
                Wrappers.lambdaQuery(DailyReportDO.class)
                        .between(DailyReportDO::getReportDate, startDate, endDate)
                        .eq(DailyReportDO::getReportStatus, ReportStatus.COMPLETED)
                        .orderByDesc(DailyReportDO::getReportDate)
        );

        // 转换为响应格式
        List<Map<String, Object>> resultList = dailyReportPage.getRecords().stream()
                .map(this::buildDailySummaryFromEntity)
                .collect(Collectors.toList());

        // 构建分页结果
        IPage<Map<String, Object>> resultPage = new Page<>(page.getCurrent(), page.getSize());
        resultPage.setRecords(resultList);
        resultPage.setTotal(dailyReportPage.getTotal());
        resultPage.setPages(dailyReportPage.getPages());

        return resultPage;
    }

    @Override
    public Map<String, Object> getDailyReportDetail(LocalDate date) {
        log.info("Getting daily report detail for date: {}", date);

        DailyReportDO dailyReport = getDailyReport(date);

        if (dailyReport == null) {
            return buildEmptyDailyDetail(date);
        }

        return buildDailyDetailFromEntity(dailyReport);
    }

    @Override
    public Map<String, Object> getPassRateTrend(LocalDate endDate, int days) {
        log.info("Getting pass rate trend for {} days ending on {}", days, endDate);

        LocalDate startDate = endDate.minusDays(days - 1);

        // 查询日期范围内的按日报告
        List<DailyReportDO> dailyReports = dailyReportRepository.list(
                Wrappers.lambdaQuery(DailyReportDO.class)
                        .between(DailyReportDO::getReportDate, startDate, endDate)
                        .eq(DailyReportDO::getReportStatus, ReportStatus.COMPLETED)
                        .orderBy(true, true, DailyReportDO::getReportDate)
        );

        // 构建趋势数据
        Map<LocalDate, DailyReportDO> reportMap = dailyReports.stream()
                .collect(Collectors.toMap(DailyReportDO::getReportDate, r -> r));

        List<Map<String, Object>> trendData = new ArrayList<>();
        LocalDate currentDate = startDate;

        while (!currentDate.isAfter(endDate)) {
            DailyReportDO report = reportMap.get(currentDate);

            Map<String, Object> dayData = new HashMap<>();
            dayData.put("date", currentDate);

            if (report == null) {
                dayData.put("passRate", 0);
                dayData.put("total", 0);
                dayData.put("success", 0);
            } else {
                dayData.put("passRate", report.getOverallPassRate());
                dayData.put("total", report.getTotalChecks());
                dayData.put("success", report.getSuccessChecks());
            }

            trendData.add(dayData);
            currentDate = currentDate.plusDays(1);
        }

        Map<String, Object> result = new HashMap<>();
        result.put("trendData", trendData);
        result.put("period", days + "天");
        result.put("startDate", startDate);
        result.put("endDate", endDate);

        return result;
    }

    @Override
    public DailyReportDO getDailyReport(LocalDate date) {
        return dailyReportRepository.getOne(
                Wrappers.lambdaQuery(DailyReportDO.class)
                        .eq(DailyReportDO::getReportDate, date)
        );
    }

    @Override
    public boolean isDailyReportAvailable(LocalDate date) {
        DailyReportDO report = getDailyReport(date);
        return report != null && ReportStatus.COMPLETED.equals(report.getReportStatus());
    }

    @Override
    public Map<String, Object> exportDailyReport(LocalDate date, String format) {
        log.info("Exporting daily report for date: {} in format: {}", date, format);

        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", "导出功能暂未实现");

        // TODO: 实现导出功能
        return result;
    }

    @Override
    public Map<String, Object> exportDailyReportRange(LocalDate startDate, LocalDate endDate, String format) {
        log.info("Exporting daily reports from {} to {} in format: {}", startDate, endDate, format);

        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", "导出功能暂未实现");

        // TODO: 实现导出功能
        return result;
    }

    /**
     * 构建空的日报告汇总
     */
    private Map<String, Object> buildEmptyDailySummary(LocalDate date) {
        Map<String, Object> summary = new HashMap<>();
        summary.put("reportDate", date);
        summary.put("reportId", "DAILY-" + date.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        summary.put("totalChecks", 0);
        summary.put("successCount", 0);
        summary.put("warningCount", 0);
        summary.put("failedCount", 0);
        summary.put("passRate", 0);
        summary.put("generateTime", null);
        summary.put("hasData", false);
        summary.put("reportStatus", "NOT_GENERATED");
        return summary;
    }

    @Override
    public Map<String, Object> exportDailyReportRange(LocalDate startDate, LocalDate endDate, String format) {
        log.info("Exporting daily reports from {} to {} in format: {}", startDate, endDate, format);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", "导出功能暂未实现");
        
        // TODO: 实现导出功能
        return result;
    }

    /**
     * 构建空的日报告汇总
     */
    private Map<String, Object> buildEmptyDailySummary(LocalDate date) {
        Map<String, Object> summary = new HashMap<>();
        summary.put("reportDate", date);
        summary.put("reportId", "DAILY-" + date.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        summary.put("totalChecks", 0);
        summary.put("successCount", 0);
        summary.put("warningCount", 0);
        summary.put("failedCount", 0);
        summary.put("passRate", 0);
        summary.put("generateTime", LocalDateTime.now());
        summary.put("hasData", false);
        return summary;
    }

    /**
     * 从DailyReportDO构建日报告汇总
     */
    private Map<String, Object> buildDailySummaryFromEntity(DailyReportDO dailyReport) {
        Map<String, Object> summary = new HashMap<>();
        summary.put("reportDate", dailyReport.getReportDate());
        summary.put("reportId", dailyReport.getDailyReportId());
        summary.put("totalChecks", dailyReport.getTotalChecks());
        summary.put("successCount", dailyReport.getSuccessChecks());
        summary.put("warningCount", dailyReport.getWarningChecks());
        summary.put("failedCount", dailyReport.getFailedChecks());
        summary.put("passRate", dailyReport.getOverallPassRate());
        summary.put("generateTime", dailyReport.getGenerateEndTime());
        summary.put("hasData", true);
        summary.put("taskCount", dailyReport.getTaskCount());
        summary.put("reportStatus", dailyReport.getReportStatus().getDescription());

        // 添加执行概况
        Map<String, Object> executionSummary = new HashMap<>();
        executionSummary.put("totalTasks", dailyReport.getTaskCount());
        executionSummary.put("successTasks", dailyReport.getSuccessTasks());
        executionSummary.put("warningTasks", dailyReport.getWarningTasks());
        executionSummary.put("failedTasks", dailyReport.getFailedTasks());
        executionSummary.put("totalRules", dailyReport.getTotalRules());
        summary.put("executionSummary", executionSummary);

        // 添加执行结果分布
        Map<String, Object> executionDistribution = buildExecutionDistributionFromEntity(dailyReport);
        summary.put("executionDistribution", executionDistribution);

        return summary;
    }

    /**
     * 构建空的日报告详情
     */
    private Map<String, Object> buildEmptyDailyDetail(LocalDate date) {
        Map<String, Object> detail = new HashMap<>();
        detail.put("reportDate", date);
        detail.put("hasData", false);
        detail.put("exceptionSummary", new ArrayList<>());
        detail.put("exceptionDetails", new ArrayList<>());
        return detail;
    }

    /**
     * 从DailyReportDO构建日报告详情
     */
    private Map<String, Object> buildDailyDetailFromEntity(DailyReportDO dailyReport) {
        Map<String, Object> detail = new HashMap<>();
        detail.put("reportDate", dailyReport.getReportDate());
        detail.put("hasData", true);

        // 构建异常汇总
        List<Map<String, Object>> exceptionSummary = buildExceptionSummaryFromEntity(dailyReport);
        detail.put("exceptionSummary", exceptionSummary);

        // 构建异常详情（从content中获取）
        List<Map<String, Object>> exceptionDetails = buildExceptionDetailsFromEntity(dailyReport);
        detail.put("exceptionDetails", exceptionDetails);

        return detail;
    }

    /**
     * 从DailyReportDO构建执行结果分布
     */
    private Map<String, Object> buildExecutionDistributionFromEntity(DailyReportDO dailyReport) {
        List<Map<String, Object>> statusCounts = new ArrayList<>();

        Map<String, Object> passedStatus = new HashMap<>();
        passedStatus.put("status", "通过");
        passedStatus.put("statusCode", "passed");
        passedStatus.put("count", dailyReport.getSuccessChecks());
        passedStatus.put("percentage", dailyReport.getTotalChecks() > 0 ?
                (dailyReport.getSuccessChecks() * 100.0) / dailyReport.getTotalChecks() : 0.0);
        statusCounts.add(passedStatus);

        Map<String, Object> warningStatus = new HashMap<>();
        warningStatus.put("status", "告警");
        warningStatus.put("statusCode", "warning");
        warningStatus.put("count", dailyReport.getWarningChecks());
        warningStatus.put("percentage", dailyReport.getTotalChecks() > 0 ?
                (dailyReport.getWarningChecks() * 100.0) / dailyReport.getTotalChecks() : 0.0);
        statusCounts.add(warningStatus);

        Map<String, Object> failedStatus = new HashMap<>();
        failedStatus.put("status", "失败");
        failedStatus.put("statusCode", "failed");
        failedStatus.put("count", dailyReport.getFailedChecks());
        failedStatus.put("percentage", dailyReport.getTotalChecks() > 0 ?
                (dailyReport.getFailedChecks() * 100.0) / dailyReport.getTotalChecks() : 0.0);
        statusCounts.add(failedStatus);

        Map<String, Object> distribution = new HashMap<>();
        distribution.put("total", dailyReport.getTotalChecks());
        distribution.put("successRate", dailyReport.getOverallPassRate());
        distribution.put("statusCounts", statusCounts);

        return distribution;
    }

    /**
     * 从DailyReportDO构建异常汇总
     */
    private List<Map<String, Object>> buildExceptionSummaryFromEntity(DailyReportDO dailyReport) {
        List<Map<String, Object>> exceptionSummary = new ArrayList<>();

        if (dailyReport.getCriticalExceptions() > 0) {
            Map<String, Object> critical = new HashMap<>();
            critical.put("level", "critical");
            critical.put("levelName", "高危");
            critical.put("count", dailyReport.getCriticalExceptions());
            critical.put("exceptionTypes", List.of("系统故障", "网络中断"));
            exceptionSummary.add(critical);
        }

        if (dailyReport.getWarningExceptions() > 0) {
            Map<String, Object> warning = new HashMap<>();
            warning.put("level", "warning");
            warning.put("levelName", "中危");
            warning.put("count", dailyReport.getWarningExceptions());
            warning.put("exceptionTypes", List.of("性能异常", "配置错误"));
            exceptionSummary.add(warning);
        }

        if (dailyReport.getInfoExceptions() > 0) {
            Map<String, Object> info = new HashMap<>();
            info.put("level", "info");
            info.put("levelName", "低危");
            info.put("count", dailyReport.getInfoExceptions());
            info.put("exceptionTypes", List.of("轻微告警"));
            exceptionSummary.add(info);
        }

        return exceptionSummary;
    }

    /**
     * 从DailyReportDO构建异常详情
     */
    private List<Map<String, Object>> buildExceptionDetailsFromEntity(DailyReportDO dailyReport) {
        List<Map<String, Object>> exceptionDetails = new ArrayList<>();

        // 从content中获取异常详情
        if (dailyReport.getContent() != null && dailyReport.getContent().getExceptionSummaries() != null) {
            for (DailyReportContent.ExceptionSummary exceptionSummary : dailyReport.getContent().getExceptionSummaries()) {
                if (exceptionSummary.getExceptionTypes() != null) {
                    for (DailyReportContent.ExceptionSummary.ExceptionTypeCount typeCount : exceptionSummary.getExceptionTypes()) {
                        Map<String, Object> detail = new HashMap<>();
                        detail.put("ruleId", "UNKNOWN");
                        detail.put("ruleName", typeCount.getExceptionType());
                        detail.put("description", typeCount.getTypicalDescription());
                        detail.put("resourceName", "多个资源");
                        detail.put("level", exceptionSummary.getLevel());
                        detail.put("occurTime", dailyReport.getGenerateEndTime());
                        detail.put("suggestion", "请检查相关配置和资源状态");
                        exceptionDetails.add(detail);
                    }
                }
            }
        }

        return exceptionDetails;
    }

}
