package com.cmpay.hacp.inspection.application.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmpay.hacp.inspection.application.service.DailyInspectionReportService;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.ReportContent;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.ReportDO;
import com.cmpay.hacp.inspection.infrastructure.repository.ReportRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 按日巡检报告服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DailyInspectionReportServiceImpl implements DailyInspectionReportService {

    private final ReportRepository reportRepository;

    @Override
    public Map<String, Object> getDailyReportSummary(LocalDate date) {
        log.info("Getting daily report summary for date: {}", date);
        
        // 获取指定日期的所有按次报告
        List<ReportDO> reports = getPerInspectionReportsByDate(date);
        
        if (reports.isEmpty()) {
            return buildEmptyDailySummary(date);
        }
        
        // 汇总统计数据
        return buildDailySummary(date, reports);
    }

    @Override
    public IPage<Map<String, Object>> getDailyReportList(LocalDate startDate, LocalDate endDate, IPage<?> page) {
        log.info("Getting daily report list from {} to {}", startDate, endDate);
        
        // 创建结果分页对象
        IPage<Map<String, Object>> resultPage = new Page<>(page.getCurrent(), page.getSize());
        List<Map<String, Object>> dailyReports = new ArrayList<>();
        
        // 遍历日期范围，为每一天生成汇总数据
        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            List<ReportDO> dayReports = getPerInspectionReportsByDate(currentDate);
            if (!dayReports.isEmpty()) {
                Map<String, Object> dailySummary = buildDailySummary(currentDate, dayReports);
                dailyReports.add(dailySummary);
            }
            currentDate = currentDate.plusDays(1);
        }
        
        // 手动分页
        int start = (int) ((page.getCurrent() - 1) * page.getSize());
        int end = Math.min(start + (int) page.getSize(), dailyReports.size());
        
        if (start < dailyReports.size()) {
            resultPage.setRecords(dailyReports.subList(start, end));
        } else {
            resultPage.setRecords(new ArrayList<>());
        }
        
        resultPage.setTotal(dailyReports.size());
        return resultPage;
    }

    @Override
    public Map<String, Object> getDailyReportDetail(LocalDate date) {
        log.info("Getting daily report detail for date: {}", date);
        
        List<ReportDO> reports = getPerInspectionReportsByDate(date);
        
        if (reports.isEmpty()) {
            return buildEmptyDailyDetail(date);
        }
        
        return buildDailyDetail(date, reports);
    }

    @Override
    public Map<String, Object> getPassRateTrend(LocalDate endDate, int days) {
        log.info("Getting pass rate trend for {} days ending on {}", days, endDate);
        
        List<Map<String, Object>> trendData = new ArrayList<>();
        LocalDate startDate = endDate.minusDays(days - 1);
        
        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            List<ReportDO> dayReports = getPerInspectionReportsByDate(currentDate);
            
            Map<String, Object> dayData = new HashMap<>();
            dayData.put("date", currentDate);
            
            if (dayReports.isEmpty()) {
                dayData.put("passRate", 0);
                dayData.put("total", 0);
                dayData.put("success", 0);
            } else {
                int total = calculateTotalChecks(dayReports);
                int success = calculateSuccessCount(dayReports);
                int passRate = total > 0 ? (success * 100) / total : 0;
                
                dayData.put("passRate", passRate);
                dayData.put("total", total);
                dayData.put("success", success);
            }
            
            trendData.add(dayData);
            currentDate = currentDate.plusDays(1);
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("trendData", trendData);
        result.put("period", days + "天");
        result.put("startDate", startDate);
        result.put("endDate", endDate);
        
        return result;
    }

    @Override
    public boolean generateDailyReport(LocalDate date) {
        log.info("Generating daily report for date: {}", date);
        
        try {
            List<ReportDO> reports = getPerInspectionReportsByDate(date);
            if (reports.isEmpty()) {
                log.warn("No per-inspection reports found for date: {}", date);
                return false;
            }
            
            // 这里可以实现将按日汇总数据持久化的逻辑
            // 目前暂时只是汇总计算，不做持久化
            Map<String, Object> dailySummary = buildDailySummary(date, reports);
            log.info("Daily report generated successfully for date: {}, summary: {}", date, dailySummary);
            
            return true;
        } catch (Exception e) {
            log.error("Failed to generate daily report for date: {}", date, e);
            return false;
        }
    }

    @Override
    public List<ReportDO> getPerInspectionReportsByDate(LocalDate date) {
        LocalDateTime startOfDay = date.atStartOfDay();
        LocalDateTime endOfDay = date.atTime(23, 59, 59);
        
        return reportRepository.list(
                Wrappers.lambdaQuery(ReportDO.class)
                        .between(ReportDO::getStartTime, startOfDay, endOfDay)
                        .orderBy(true, true, ReportDO::getStartTime)
        );
    }

    @Override
    public Map<String, Object> exportDailyReport(LocalDate date, String format) {
        log.info("Exporting daily report for date: {} in format: {}", date, format);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", "导出功能暂未实现");
        
        // TODO: 实现导出功能
        return result;
    }

    @Override
    public Map<String, Object> exportDailyReportRange(LocalDate startDate, LocalDate endDate, String format) {
        log.info("Exporting daily reports from {} to {} in format: {}", startDate, endDate, format);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", "导出功能暂未实现");
        
        // TODO: 实现导出功能
        return result;
    }

    /**
     * 构建空的日报告汇总
     */
    private Map<String, Object> buildEmptyDailySummary(LocalDate date) {
        Map<String, Object> summary = new HashMap<>();
        summary.put("reportDate", date);
        summary.put("reportId", "DAILY-" + date.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        summary.put("totalChecks", 0);
        summary.put("successCount", 0);
        summary.put("warningCount", 0);
        summary.put("failedCount", 0);
        summary.put("passRate", 0);
        summary.put("generateTime", LocalDateTime.now());
        summary.put("hasData", false);
        return summary;
    }

    /**
     * 构建日报告汇总
     */
    private Map<String, Object> buildDailySummary(LocalDate date, List<ReportDO> reports) {
        int totalChecks = calculateTotalChecks(reports);
        int successCount = calculateSuccessCount(reports);
        int warningCount = calculateWarningCount(reports);
        int failedCount = calculateFailedCount(reports);
        int passRate = totalChecks > 0 ? (successCount * 100) / totalChecks : 0;
        
        Map<String, Object> summary = new HashMap<>();
        summary.put("reportDate", date);
        summary.put("reportId", "DAILY-" + date.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        summary.put("totalChecks", totalChecks);
        summary.put("successCount", successCount);
        summary.put("warningCount", warningCount);
        summary.put("failedCount", failedCount);
        summary.put("passRate", passRate);
        summary.put("generateTime", LocalDateTime.now());
        summary.put("hasData", true);
        summary.put("taskCount", reports.size());
        
        // 添加执行概况
        Map<String, Object> executionSummary = buildExecutionSummary(reports);
        summary.put("executionSummary", executionSummary);
        
        // 添加执行结果分布
        Map<String, Object> executionDistribution = buildExecutionDistribution(reports);
        summary.put("executionDistribution", executionDistribution);
        
        return summary;
    }

    /**
     * 构建空的日报告详情
     */
    private Map<String, Object> buildEmptyDailyDetail(LocalDate date) {
        Map<String, Object> detail = new HashMap<>();
        detail.put("reportDate", date);
        detail.put("hasData", false);
        detail.put("exceptionSummary", new ArrayList<>());
        detail.put("exceptionDetails", new ArrayList<>());
        return detail;
    }

    /**
     * 构建日报告详情
     */
    private Map<String, Object> buildDailyDetail(LocalDate date, List<ReportDO> reports) {
        Map<String, Object> detail = new HashMap<>();
        detail.put("reportDate", date);
        detail.put("hasData", true);
        
        // 构建异常汇总
        List<Map<String, Object>> exceptionSummary = buildExceptionSummary(reports);
        detail.put("exceptionSummary", exceptionSummary);
        
        // 构建异常详情
        List<Map<String, Object>> exceptionDetails = buildExceptionDetails(reports);
        detail.put("exceptionDetails", exceptionDetails);
        
        return detail;
    }

    /**
     * 计算总检查数
     */
    private int calculateTotalChecks(List<ReportDO> reports) {
        return reports.stream()
                .mapToInt(report -> {
                    if (report.getContent() != null && report.getContent().getExecutionDistribution() != null) {
                        return report.getContent().getExecutionDistribution().getTotal();
                    }
                    return 0;
                })
                .sum();
    }

    /**
     * 计算成功数
     */
    private int calculateSuccessCount(List<ReportDO> reports) {
        return reports.stream()
                .mapToInt(report -> {
                    if (report.getContent() != null && report.getContent().getExecutionDistribution() != null) {
                        return report.getContent().getExecutionDistribution().getStatusCounts().stream()
                                .filter(status -> "passed".equals(status.getStatusCode()))
                                .mapToInt(status -> status.getCount())
                                .sum();
                    }
                    return 0;
                })
                .sum();
    }

    /**
     * 计算告警数
     */
    private int calculateWarningCount(List<ReportDO> reports) {
        return reports.stream()
                .mapToInt(report -> {
                    if (report.getContent() != null && report.getContent().getExecutionDistribution() != null) {
                        return report.getContent().getExecutionDistribution().getStatusCounts().stream()
                                .filter(status -> "warning".equals(status.getStatusCode()))
                                .mapToInt(status -> status.getCount())
                                .sum();
                    }
                    return 0;
                })
                .sum();
    }

    /**
     * 计算失败数
     */
    private int calculateFailedCount(List<ReportDO> reports) {
        return reports.stream()
                .mapToInt(report -> {
                    if (report.getContent() != null && report.getContent().getExecutionDistribution() != null) {
                        return report.getContent().getExecutionDistribution().getStatusCounts().stream()
                                .filter(status -> "failed".equals(status.getStatusCode()))
                                .mapToInt(status -> status.getCount())
                                .sum();
                    }
                    return 0;
                })
                .sum();
    }
