<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dispatch.dao.IDispatchPushHistoryDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.dispatch.entity.DispatchPushHistoryDO" >
        <id column="push_history_id" property="pushHistoryId" jdbcType="INTEGER" />
        <result column="dispatch_config_id" property="dispatchConfigId" jdbcType="INTEGER" />
        <result column="push_times" property="pushTimes" jdbcType="TINYINT" />
        <result column="dispatch_node_id" property="dispatchNodeId" jdbcType="INTEGER" />
        <result column="dispatch_node_name" property="dispatchNodeName" jdbcType="VARCHAR" />
        <result column="zone_id" property="zoneId" jdbcType="BIGINT" />
        <result column="ip_port" property="ipPort" jdbcType="VARCHAR" />
        <result column="push_start_time" property="pushStartTime" jdbcType="TIMESTAMP" />
        <result column="push_end_time" property="pushEndTime" jdbcType="TIMESTAMP" />
        <result column="config_desc" property="configDesc" jdbcType="VARCHAR" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="push_status" property="pushStatus" jdbcType="TINYINT" />
        <result column="msg_cd" property="msgCd" jdbcType="VARCHAR" />
        <result column="msg_info" property="msgInfo" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        push_history_id, dispatch_config_id, push_times, dispatch_node_id, dispatch_node_name, 
        zone_id, ip_port, push_start_time, push_end_time, config_desc, operator_id, operator_name, 
        push_status, msg_cd, msg_info
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
        select 
        <include refid="Base_Column_List" />
        from dispatch_push_history
        where push_history_id = #{pushHistoryId,jdbcType=INTEGER}
    </select>

    <delete id="delete" parameterType="java.lang.Integer" >
        delete from dispatch_push_history
        where push_history_id = #{pushHistoryId,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.dispatch.entity.DispatchPushHistoryDO" >
        insert into dispatch_push_history
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="pushHistoryId != null" >
                push_history_id,
            </if>
            <if test="dispatchConfigId != null" >
                dispatch_config_id,
            </if>
            <if test="pushTimes != null" >
                push_times,
            </if>
            <if test="dispatchNodeId != null" >
                dispatch_node_id,
            </if>
            <if test="dispatchNodeName != null" >
                dispatch_node_name,
            </if>
            <if test="zoneId != null" >
                zone_id,
            </if>
            <if test="ipPort != null" >
                ip_port,
            </if>
            <if test="pushStartTime != null" >
                push_start_time,
            </if>
            <if test="pushEndTime != null" >
                push_end_time,
            </if>
            <if test="configDesc != null" >
                config_desc,
            </if>
            <if test="operatorId != null" >
                operator_id,
            </if>
            <if test="operatorName != null" >
                operator_name,
            </if>
            <if test="pushStatus != null" >
                push_status,
            </if>
            <if test="msgCd != null" >
                msg_cd,
            </if>
            <if test="msgInfo != null" >
                msg_info,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="pushHistoryId != null" >
                #{pushHistoryId,jdbcType=INTEGER},
            </if>
            <if test="dispatchConfigId != null" >
                #{dispatchConfigId,jdbcType=INTEGER},
            </if>
            <if test="pushTimes != null" >
                #{pushTimes,jdbcType=TINYINT},
            </if>
            <if test="dispatchNodeId != null" >
                #{dispatchNodeId,jdbcType=INTEGER},
            </if>
            <if test="dispatchNodeName != null" >
                #{dispatchNodeName,jdbcType=VARCHAR},
            </if>
            <if test="zoneId != null" >
                #{zoneId,jdbcType=BIGINT},
            </if>
            <if test="ipPort != null" >
                #{ipPort,jdbcType=VARCHAR},
            </if>
            <if test="pushStartTime != null" >
                #{pushStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="pushEndTime != null" >
                #{pushEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="configDesc != null" >
                #{configDesc,jdbcType=VARCHAR},
            </if>
            <if test="operatorId != null" >
                #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="pushStatus != null" >
                #{pushStatus,jdbcType=TINYINT},
            </if>
            <if test="msgCd != null" >
                #{msgCd,jdbcType=VARCHAR},
            </if>
            <if test="msgInfo != null" >
                #{msgInfo,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.dispatch.entity.DispatchPushHistoryDO" >
        update dispatch_push_history
        <set >
            <if test="dispatchConfigId != null" >
                dispatch_config_id = #{dispatchConfigId,jdbcType=INTEGER},
            </if>
            <if test="pushTimes != null" >
                push_times = #{pushTimes,jdbcType=TINYINT},
            </if>
            <if test="dispatchNodeId != null" >
                dispatch_node_id = #{dispatchNodeId,jdbcType=INTEGER},
            </if>
            <if test="dispatchNodeName != null" >
                dispatch_node_name = #{dispatchNodeName,jdbcType=VARCHAR},
            </if>
            <if test="zoneId != null" >
                zone_id = #{zoneId,jdbcType=BIGINT},
            </if>
            <if test="ipPort != null" >
                ip_port = #{ipPort,jdbcType=VARCHAR},
            </if>
            <if test="pushStartTime != null" >
                push_start_time = #{pushStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="pushEndTime != null" >
                push_end_time = #{pushEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="configDesc != null" >
                config_desc = #{configDesc,jdbcType=VARCHAR},
            </if>
            <if test="operatorId != null" >
                operator_id = #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                operator_name = #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="pushStatus != null" >
                push_status = #{pushStatus,jdbcType=TINYINT},
            </if>
            <if test="msgCd != null" >
                msg_cd = #{msgCd,jdbcType=VARCHAR},
            </if>
            <if test="msgInfo != null" >
                msg_info = #{msgInfo,jdbcType=VARCHAR},
            </if>
        </set>
        where push_history_id = #{pushHistoryId,jdbcType=INTEGER}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.dispatch.entity.DispatchPushHistoryDO" >
        select 
        <include refid="Base_Column_List" />
        from dispatch_push_history
        <where >
            <if test="pushHistoryId != null" >
                and push_history_id = #{pushHistoryId,jdbcType=INTEGER}
            </if>
            <if test="dispatchConfigId != null" >
                and dispatch_config_id = #{dispatchConfigId,jdbcType=INTEGER}
            </if>
            <if test="pushTimes != null" >
                and push_times = #{pushTimes,jdbcType=TINYINT}
            </if>
            <if test="dispatchNodeId != null" >
                and dispatch_node_id = #{dispatchNodeId,jdbcType=INTEGER}
            </if>
            <if test="dispatchNodeName != null" >
                and dispatch_node_name = #{dispatchNodeName,jdbcType=VARCHAR}
            </if>
            <if test="zoneId != null" >
                and zone_id = #{zoneId,jdbcType=BIGINT}
            </if>
            <if test="ipPort != null" >
                and ip_port = #{ipPort,jdbcType=VARCHAR}
            </if>
            <if test="pushStartTime != null" >
                and push_start_time = #{pushStartTime,jdbcType=TIMESTAMP}
            </if>
            <if test="pushEndTime != null" >
                and push_end_time = #{pushEndTime,jdbcType=TIMESTAMP}
            </if>
            <if test="configDesc != null" >
                and config_desc = #{configDesc,jdbcType=VARCHAR}
            </if>
            <if test="operatorId != null" >
                and operator_id = #{operatorId,jdbcType=VARCHAR}
            </if>
            <if test="operatorName != null" >
                and operator_name = #{operatorName,jdbcType=VARCHAR}
            </if>
            <if test="pushStatus != null" >
                and push_status = #{pushStatus,jdbcType=TINYINT}
            </if>
            <if test="msgCd != null" >
                and msg_cd = #{msgCd,jdbcType=VARCHAR}
            </if>
            <if test="msgInfo != null" >
                and msg_info = #{msgInfo,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>