<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dispatch.dao.IDispatchNodeExtDao" >

    <resultMap id="QueryResultMap" type="com.cmpay.hacp.dispatch.entity.DispatchNodeQueryDO" >
        <id column="dispatch_node_id" property="dispatchNodeId" jdbcType="INTEGER" />
        <result column="dispatch_node_name" property="dispatchNodeName" jdbcType="VARCHAR" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="zone_id" property="zoneId" jdbcType="BIGINT" />
        <result column="ip_port" property="ipPort" jdbcType="VARCHAR" />
        <result column="gray_dispatch_config_id" property="grayDispatchConfigId" jdbcType="INTEGER" />
        <result column="dispatch_config_id" property="dispatchConfigId" jdbcType="INTEGER" />
        <result column="running_dispatch_config_id" property="runningDispatchConfigId" jdbcType="INTEGER" />
        <result column="gray_dispatch_version" property="grayDispatchVersion" jdbcType="INTEGER" />
        <result column="dispatch_version" property="dispatchVersion" jdbcType="VARCHAR" />
        <result column="running_dispatch_version" property="runningDispatchVersion" jdbcType="VARCHAR" />
        <result column="config_status" property="configStatus" jdbcType="TINYINT" />
        <result column="status" property="status" jdbcType="TINYINT" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="push_times" property="pushTimes" jdbcType="TINYINT" />
        <result column="push_time" property="pushTime" jdbcType="TIMESTAMP" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Query_Column_List" >
        dispatch_node_id, dispatch_node_name, workspace_id, zone_id, ip_port,
        gray_dispatch_config_id, dispatch_config_id, running_dispatch_config_id,
        gray_dispatch_version, dispatch_version, running_dispatch_version,
        IF(push_time IS NULL, false, true) AS config_status,
        status, operator_id, operator_name, push_time, create_time,
        update_time
    </sql>

    <resultMap id="ZoneDispatchResultMap" type="com.cmpay.hacp.dispatch.bo.DashboardDispatchBO" >
        <result column="zone_name" property="zoneName" jdbcType="VARCHAR" />
        <result column="dispatch_count" property="dispatchCount" jdbcType="INTEGER" />
        <result column="dispatch_version" property="dispatchVersion" jdbcType="VARCHAR" />
        <result column="running_dispatch_version" property="runningDispatchVersion" jdbcType="VARCHAR" />
    </resultMap>

    <select id="likeFind" resultMap="QueryResultMap" parameterType="com.cmpay.hacp.dispatch.entity.DispatchNodeQueryDO" >
        select 
        <include refid="Query_Column_List" />
        from dispatch_node
        <where >
            <if test="dispatchNodeId != null" >
                and dispatch_node_id = #{dispatchNodeId,jdbcType=INTEGER}
            </if>
            <if test="dispatchNodeName != null" >
                and dispatch_node_name = #{dispatchNodeName,jdbcType=VARCHAR}
            </if>
            <if test="workspaceId != null" >
                and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="zoneId != null" >
                and zone_id = #{zoneId,jdbcType=VARCHAR}
            </if>
            <if test="ipPort != null" >
                and ip_port = #{ipPort,jdbcType=VARCHAR}
            </if>
            <if test="grayDispatchVersion != null" >
                and gray_dispatch_version = #{grayDispatchVersion,jdbcType=VARCHAR}
            </if>
            <if test="dispatchVersion != null" >
                and dispatch_version = #{dispatchVersion,jdbcType=VARCHAR}
            </if>
            <if test="runningDispatchVersion != null" >
                and running_dispatch_version = #{runningDispatchVersion,jdbcType=VARCHAR}
            </if>
            <if test="configStatus == true" >
                and push_time is not null
            </if>
            <if test="configStatus == false" >
                and push_time is null
            </if>
            <if test="status != null" >
                and status = #{status,jdbcType=TINYINT}
            </if>
            <if test="operatorId != null" >
                and operator_id = #{operatorId,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null" >
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null" >
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </select>

    <select id="randGetNode" resultMap="QueryResultMap" parameterType="com.cmpay.hacp.dispatch.entity.DispatchNodeQueryDO" >
        select
        <include refid="Query_Column_List" />
        from dispatch_node
        <where >
            <if test="dispatchNodeId != null" >
                and dispatch_node_id = #{dispatchNodeId,jdbcType=INTEGER}
            </if>
            <if test="dispatchNodeName != null" >
                and dispatch_node_name = #{dispatchNodeName,jdbcType=VARCHAR}
            </if>
            <if test="workspaceId != null" >
                and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="zoneId != null" >
                and zone_id = #{zoneId,jdbcType=VARCHAR}
            </if>
            <if test="ipPort != null" >
                and ip_port = #{ipPort,jdbcType=VARCHAR}
            </if>
            <if test="grayDispatchVersion != null" >
                and gray_dispatch_version = #{grayDispatchVersion,jdbcType=VARCHAR}
            </if>
            <if test="dispatchVersion != null" >
                and dispatch_version = #{dispatchVersion,jdbcType=VARCHAR}
            </if>
            <if test="runningDispatchVersion != null" >
                and running_dispatch_version = #{runningDispatchVersion,jdbcType=VARCHAR}
            </if>
            <if test="configStatus == true" >
                and push_time is not null
            </if>
            <if test="configStatus == false" >
                and push_time is null
            </if>
            <if test="status != null" >
                and status = #{status,jdbcType=TINYINT}
            </if>
            <if test="operatorId != null" >
                and operator_id = #{operatorId,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null" >
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null" >
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            ORDER BY RAND() LIMIT 1
        </where>
    </select>

    <select id="countWorkspaceNodePushStatus" resultType="java.lang.Integer" parameterType="com.cmpay.hacp.dispatch.entity.DispatchNodeQueryDO" >
        select count(dispatch_node_id)
        from dispatch_node
        <where >
            (dispatch_config_id != running_dispatch_config_id or running_dispatch_config_id is null)
            <if test="workspaceId != null" >
                and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="dispatchConfigId != null" >
                and dispatch_config_id = #{dispatchConfigId,jdbcType=INTEGER}
            </if>
            <if test="runningDispatchConfigId != null" >
                and running_dispatch_id = #{runningDispatchConfigId,jdbcType=INTEGER}
            </if>
            <if test="status != null" >
                and status = #{status,jdbcType=TINYINT}
            </if>
        </where>
    </select>
    <select id="getZoneDispatchList" resultMap="ZoneDispatchResultMap">
        select any_value(zone_name) as zone_name,any_value(dispatch_version) as dispatch_version,any_value(running_dispatch_version) as running_dispatch_version,count(any_value(n.dispatch_node_id)) as dispatch_count
        from  dispatch_node n
        left join dispatch_zone z on z.id=n.zone_id
        where z.workspace_id=#{workspaceId,jdbcType=VARCHAR}
        GROUP BY z.id,dispatch_version
    </select>

    <select id="getRunningVersionList" resultType="java.lang.String">
        select
        running_dispatch_version
        from dispatch_node
        where
        workspace_id = #{workspaceId,jdbcType=VARCHAR}
        and running_dispatch_version is not null
        and status = #{status,jdbcType=TINYINT}
    </select>
</mapper>