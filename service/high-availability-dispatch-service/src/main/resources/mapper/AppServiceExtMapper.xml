<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dispatch.dao.IAppServiceExtDao" >

    <resultMap id="ServiceResultMap" type="com.cmpay.hacp.dispatch.entity.AppServiceDO" >
        <id column="app_service_id" property="appServiceId" jdbcType="INTEGER" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="app_service_name" property="appServiceName" jdbcType="VARCHAR" />
        <result column="app_service_cn" property="appServiceCn" jdbcType="VARCHAR" />
        <result column="app_service_desc" property="appServiceDesc" jdbcType="VARCHAR" />
        <result column="listen" property="listen" jdbcType="VARCHAR" />
        <result column="domain_names" property="domainNames" jdbcType="VARCHAR" />
        <result column="extra_configs" property="extraConfigs" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="TINYINT" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Service_Column_List" >
        app_service_id, workspace_id, app_service_cn, app_service_name, app_service_desc,
        listen, domain_names, extra_configs, status, operator_id, operator_name, create_time,
        update_time
    </sql>

    <select id="likeFind" resultMap="ServiceResultMap" parameterType="com.cmpay.hacp.dispatch.entity.AppServiceDO" >
        select 
        <include refid="Service_Column_List" />
        from app_service
        <where >
            <if test="appServiceId != null" >
                and app_service_id = #{appServiceId,jdbcType=INTEGER}
            </if>
            <if test="workspaceId != null" >
                and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="appServiceName != null" >
                and app_service_name like concat('%',#{appServiceName,jdbcType=VARCHAR},'%')
            </if>
            <if test="appServiceDesc != null" >
                and app_service_desc = #{appServiceDesc,jdbcType=VARCHAR}
            </if>
            <if test="listen != null" >
                and listen = #{listen,jdbcType=VARCHAR}
            </if>
            <if test="domainNames != null" >
                and domain_names like concat('%',#{domainNames,jdbcType=VARCHAR},'%')
            </if>
            <if test="extraConfigs != null" >
                and extra_configs = #{extraConfigs,jdbcType=VARCHAR}
            </if>
            <if test="status != null" >
                and status = #{status,jdbcType=TINYINT}
            </if>
            <if test="operatorId != null" >
                and operator_id = #{operatorId,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null" >
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null" >
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </select>

    <select id="existFind" resultType="com.cmpay.hacp.dispatch.entity.AppServiceDO">
        select
        <include refid="Service_Column_List" />
        from app_service
        <where>
            workspace_id = #{workspaceId,jdbcType=VARCHAR}
            and app_service_name = #{appServiceName,jdbcType=VARCHAR}
            <if test="appServiceId != null " >
                and app_service_id != #{appServiceId,jdbcType=INTEGER}
            </if>
        </where>
    </select>
</mapper>