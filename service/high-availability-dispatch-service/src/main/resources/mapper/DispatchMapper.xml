<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dispatch.dao.IDispatchDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.dispatch.entity.DispatchDO" >
        <id column="dispatch_id" property="dispatchId" jdbcType="INTEGER" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="dispatch_cn" property="dispatchCn" jdbcType="VARCHAR" />
        <result column="dispatch_name" property="dispatchName" jdbcType="VARCHAR" />
        <result column="dispatch_desc" property="dispatchDesc" jdbcType="VARCHAR" />
        <result column="api_location_id" property="apiLocationId" jdbcType="VARCHAR" />
        <result column="api_tag" property="apiTag" jdbcType="VARCHAR" />
        <result column="api_rule_ids" property="apiRuleIds" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="TINYINT" />
        <result column="is_emergency" property="isEmergency" jdbcType="TINYINT" />
        <result column="priority_level" property="priorityLevel" jdbcType="TINYINT" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        dispatch_id, workspace_id, dispatch_cn, dispatch_name, dispatch_desc, api_location_id, 
        api_tag, api_rule_ids, status, is_emergency, priority_level, operator_id, operator_name, 
        create_time, update_time
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
        select 
        <include refid="Base_Column_List" />
        from dispatch
        where dispatch_id = #{dispatchId,jdbcType=INTEGER}
    </select>

    <delete id="delete" parameterType="java.lang.Integer" >
        delete from dispatch
        where dispatch_id = #{dispatchId,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.dispatch.entity.DispatchDO" useGeneratedKeys="true" keyProperty="dispatchId" >
        insert into dispatch
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="workspaceId != null" >
                workspace_id,
            </if>
            <if test="dispatchCn != null" >
                dispatch_cn,
            </if>
            <if test="dispatchName != null" >
                dispatch_name,
            </if>
            <if test="dispatchDesc != null" >
                dispatch_desc,
            </if>
            <if test="apiLocationId != null" >
                api_location_id,
            </if>
            <if test="apiTag != null" >
                api_tag,
            </if>
            <if test="apiRuleIds != null" >
                api_rule_ids,
            </if>
            <if test="status != null" >
                status,
            </if>
            <if test="isEmergency != null" >
                is_emergency,
            </if>
            <if test="priorityLevel != null" >
                priority_level,
            </if>
            <if test="operatorId != null" >
                operator_id,
            </if>
            <if test="operatorName != null" >
                operator_name,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateTime != null" >
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="workspaceId != null" >
                #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="dispatchCn != null" >
                #{dispatchCn,jdbcType=VARCHAR},
            </if>
            <if test="dispatchName != null" >
                #{dispatchName,jdbcType=VARCHAR},
            </if>
            <if test="dispatchDesc != null" >
                #{dispatchDesc,jdbcType=VARCHAR},
            </if>
            <if test="apiLocationId != null" >
                #{apiLocationId,jdbcType=VARCHAR},
            </if>
            <if test="apiTag != null" >
                #{apiTag,jdbcType=VARCHAR},
            </if>
            <if test="apiRuleIds != null" >
                #{apiRuleIds,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                #{status,jdbcType=TINYINT},
            </if>
            <if test="isEmergency != null" >
                #{isEmergency,jdbcType=TINYINT},
            </if>
            <if test="priorityLevel != null" >
                #{priorityLevel,jdbcType=TINYINT},
            </if>
            <if test="operatorId != null" >
                #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.dispatch.entity.DispatchDO" >
        update dispatch
        <set >
            <if test="workspaceId != null" >
                workspace_id = #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="dispatchCn != null" >
                dispatch_cn = #{dispatchCn,jdbcType=VARCHAR},
            </if>
            <if test="dispatchName != null" >
                dispatch_name = #{dispatchName,jdbcType=VARCHAR},
            </if>
            <if test="dispatchDesc != null" >
                dispatch_desc = #{dispatchDesc,jdbcType=VARCHAR},
            </if>
            <if test="apiLocationId != null" >
                api_location_id = #{apiLocationId,jdbcType=VARCHAR},
            </if>
            <if test="apiTag != null" >
                api_tag = #{apiTag,jdbcType=VARCHAR},
            </if>
            <if test="apiRuleIds != null" >
                api_rule_ids = #{apiRuleIds,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="isEmergency != null" >
                is_emergency = #{isEmergency,jdbcType=TINYINT},
            </if>
            <if test="priorityLevel != null" >
                priority_level = #{priorityLevel,jdbcType=TINYINT},
            </if>
            <if test="operatorId != null" >
                operator_id = #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                operator_name = #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where dispatch_id = #{dispatchId,jdbcType=INTEGER}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.dispatch.entity.DispatchDO" >
        select 
        <include refid="Base_Column_List" />
        from dispatch
        <where >
            <if test="dispatchId != null" >
                and dispatch_id = #{dispatchId,jdbcType=INTEGER}
            </if>
            <if test="workspaceId != null" >
                and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="dispatchCn != null" >
                and dispatch_cn = #{dispatchCn,jdbcType=VARCHAR}
            </if>
            <if test="dispatchName != null" >
                and dispatch_name = #{dispatchName,jdbcType=VARCHAR}
            </if>
            <if test="dispatchDesc != null" >
                and dispatch_desc = #{dispatchDesc,jdbcType=VARCHAR}
            </if>
            <if test="apiLocationId != null" >
                and api_location_id = #{apiLocationId,jdbcType=VARCHAR}
            </if>
            <if test="apiTag != null" >
                and api_tag = #{apiTag,jdbcType=VARCHAR}
            </if>
            <if test="apiRuleIds != null" >
                and api_rule_ids = #{apiRuleIds,jdbcType=VARCHAR}
            </if>
            <if test="status != null" >
                and status = #{status,jdbcType=TINYINT}
            </if>
            <if test="isEmergency != null" >
                and is_emergency = #{isEmergency,jdbcType=TINYINT}
            </if>
            <if test="priorityLevel != null" >
                and priority_level = #{priorityLevel,jdbcType=TINYINT}
            </if>
            <if test="operatorId != null" >
                and operator_id = #{operatorId,jdbcType=VARCHAR}
            </if>
            <if test="operatorName != null" >
                and operator_name = #{operatorName,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null" >
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null" >
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </select>
</mapper>