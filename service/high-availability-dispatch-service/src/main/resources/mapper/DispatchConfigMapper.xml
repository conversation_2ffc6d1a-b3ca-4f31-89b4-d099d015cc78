<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dispatch.dao.IDispatchConfigDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.dispatch.entity.DispatchConfigDO" >
        <id column="dispatch_config_id" property="dispatchConfigId" jdbcType="INTEGER" />
        <result column="dispatch_version" property="dispatchVersion" jdbcType="VARCHAR" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="is_current_version" property="isCurrentVersion" jdbcType="TINYINT" />
        <result column="push_times" property="pushTimes" jdbcType="TINYINT" />
        <result column="push_start_time" property="pushStartTime" jdbcType="TIMESTAMP" />
        <result column="push_end_time" property="pushEndTime" jdbcType="TIMESTAMP" />
        <result column="config_desc" property="configDesc" jdbcType="VARCHAR" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="TINYINT" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="config_hex" property="configHex" jdbcType="VARCHAR" />
    </resultMap>

    <resultMap id="ResultMapWithBLOBs" type="com.cmpay.hacp.dispatch.entity.DispatchConfigDO" extends="BaseResultMap" >
        <result column="app_instance_groups" property="appInstanceGroups" jdbcType="LONGVARCHAR" />
        <result column="app_services" property="appServices" jdbcType="LONGVARCHAR" />
        <result column="app_api_locations" property="appApiLocations" jdbcType="LONGVARCHAR" />
        <result column="app_rules" property="appRules" jdbcType="LONGVARCHAR" />
        <result column="dispatches" property="dispatches" jdbcType="LONGVARCHAR" />
        <result column="api_tag_map" property="apiTagMap" jdbcType="LONGVARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        dispatch_config_id, dispatch_version, workspace_id, is_current_version, push_times, 
        push_start_time, push_end_time, config_desc, operator_id, operator_name, status, 
        create_time, update_time, config_hex
    </sql>

    <sql id="Blob_Column_List" >
        app_instance_groups, app_services, app_api_locations, app_rules, dispatches, api_tag_map
    </sql>

    <select id="get" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Integer" >
        select 
        <include refid="Base_Column_List" />
        ,
        <include refid="Blob_Column_List" />
        from dispatch_config
        where dispatch_config_id = #{dispatchConfigId,jdbcType=INTEGER}
    </select>

    <delete id="delete" parameterType="java.lang.Integer" >
        delete from dispatch_config
        where dispatch_config_id = #{dispatchConfigId,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.dispatch.entity.DispatchConfigDO" >
        insert into dispatch_config
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="dispatchConfigId != null" >
                dispatch_config_id,
            </if>
            <if test="dispatchVersion != null" >
                dispatch_version,
            </if>
            <if test="workspaceId != null" >
                workspace_id,
            </if>
            <if test="isCurrentVersion != null" >
                is_current_version,
            </if>
            <if test="pushTimes != null" >
                push_times,
            </if>
            <if test="pushStartTime != null" >
                push_start_time,
            </if>
            <if test="pushEndTime != null" >
                push_end_time,
            </if>
            <if test="configDesc != null" >
                config_desc,
            </if>
            <if test="operatorId != null" >
                operator_id,
            </if>
            <if test="operatorName != null" >
                operator_name,
            </if>
            <if test="status != null" >
                status,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateTime != null" >
                update_time,
            </if>
            <if test="configHex != null" >
                config_hex,
            </if>
            <if test="appInstanceGroups != null" >
                app_instance_groups,
            </if>
            <if test="appServices != null" >
                app_services,
            </if>
            <if test="appApiLocations != null" >
                app_api_locations,
            </if>
            <if test="appRules != null" >
                app_rules,
            </if>
            <if test="dispatches != null" >
                dispatches,
            </if>
            <if test="apiTagMap != null" >
                api_tag_map,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="dispatchConfigId != null" >
                #{dispatchConfigId,jdbcType=INTEGER},
            </if>
            <if test="dispatchVersion != null" >
                #{dispatchVersion,jdbcType=VARCHAR},
            </if>
            <if test="workspaceId != null" >
                #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="isCurrentVersion != null" >
                #{isCurrentVersion,jdbcType=TINYINT},
            </if>
            <if test="pushTimes != null" >
                #{pushTimes,jdbcType=TINYINT},
            </if>
            <if test="pushStartTime != null" >
                #{pushStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="pushEndTime != null" >
                #{pushEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="configDesc != null" >
                #{configDesc,jdbcType=VARCHAR},
            </if>
            <if test="operatorId != null" >
                #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                #{status,jdbcType=TINYINT},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="configHex != null" >
                #{configHex,jdbcType=VARCHAR},
            </if>
            <if test="appInstanceGroups != null" >
                #{appInstanceGroups,jdbcType=LONGVARCHAR},
            </if>
            <if test="appServices != null" >
                #{appServices,jdbcType=LONGVARCHAR},
            </if>
            <if test="appApiLocations != null" >
                #{appApiLocations,jdbcType=LONGVARCHAR},
            </if>
            <if test="appRules != null" >
                #{appRules,jdbcType=LONGVARCHAR},
            </if>
            <if test="dispatches != null" >
                #{dispatches,jdbcType=LONGVARCHAR},
            </if>
            <if test="apiTagMap != null" >
                #{apiTagMap,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.dispatch.entity.DispatchConfigDO" >
        update dispatch_config
        <set >
            <if test="dispatchVersion != null" >
                dispatch_version = #{dispatchVersion,jdbcType=VARCHAR},
            </if>
            <if test="workspaceId != null" >
                workspace_id = #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="isCurrentVersion != null" >
                is_current_version = #{isCurrentVersion,jdbcType=TINYINT},
            </if>
            <if test="pushTimes != null" >
                push_times = #{pushTimes,jdbcType=TINYINT},
            </if>
            <if test="pushStartTime != null" >
                push_start_time = #{pushStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="pushEndTime != null" >
                push_end_time = #{pushEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="configDesc != null" >
                config_desc = #{configDesc,jdbcType=VARCHAR},
            </if>
            <if test="operatorId != null" >
                operator_id = #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                operator_name = #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="configHex != null" >
                config_hex = #{configHex,jdbcType=VARCHAR},
            </if>
            <if test="appInstanceGroups != null" >
                app_instance_groups = #{appInstanceGroups,jdbcType=LONGVARCHAR},
            </if>
            <if test="appServices != null" >
                app_services = #{appServices,jdbcType=LONGVARCHAR},
            </if>
            <if test="appApiLocations != null" >
                app_api_locations = #{appApiLocations,jdbcType=LONGVARCHAR},
            </if>
            <if test="appRules != null" >
                app_rules = #{appRules,jdbcType=LONGVARCHAR},
            </if>
            <if test="dispatches != null" >
                dispatches = #{dispatches,jdbcType=LONGVARCHAR},
            </if>
            <if test="apiTagMap != null" >
                api_tag_map = #{apiTagMap,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where dispatch_config_id = #{dispatchConfigId,jdbcType=INTEGER}
    </update>

    <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.cmpay.hacp.dispatch.entity.DispatchConfigDO" >
        update dispatch_config
        set dispatch_version = #{dispatchVersion,jdbcType=VARCHAR},
            workspace_id = #{workspaceId,jdbcType=VARCHAR},
            is_current_version = #{isCurrentVersion,jdbcType=TINYINT},
            push_times = #{pushTimes,jdbcType=TINYINT},
            push_start_time = #{pushStartTime,jdbcType=TIMESTAMP},
            push_end_time = #{pushEndTime,jdbcType=TIMESTAMP},
            config_desc = #{configDesc,jdbcType=VARCHAR},
            operator_id = #{operatorId,jdbcType=VARCHAR},
            operator_name = #{operatorName,jdbcType=VARCHAR},
            status = #{status,jdbcType=TINYINT},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            config_hex = #{configHex,jdbcType=VARCHAR},
            app_instance_groups = #{appInstanceGroups,jdbcType=LONGVARCHAR},
            app_services = #{appServices,jdbcType=LONGVARCHAR},
            app_api_locations = #{appApiLocations,jdbcType=LONGVARCHAR},
            app_rules = #{appRules,jdbcType=LONGVARCHAR},
            dispatches = #{dispatches,jdbcType=LONGVARCHAR},
            api_tag_map = #{apiTagMap,jdbcType=LONGVARCHAR}
        where dispatch_config_id = #{dispatchConfigId,jdbcType=INTEGER}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.dispatch.entity.DispatchConfigDO" >
        select 
        <include refid="Base_Column_List" />
        from dispatch_config
        <where >
            <if test="dispatchConfigId != null" >
                and dispatch_config_id = #{dispatchConfigId,jdbcType=INTEGER}
            </if>
            <if test="dispatchVersion != null" >
                and dispatch_version = #{dispatchVersion,jdbcType=VARCHAR}
            </if>
            <if test="workspaceId != null" >
                and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="isCurrentVersion != null" >
                and is_current_version = #{isCurrentVersion,jdbcType=TINYINT}
            </if>
            <if test="pushTimes != null" >
                and push_times = #{pushTimes,jdbcType=TINYINT}
            </if>
            <if test="pushStartTime != null" >
                and push_start_time = #{pushStartTime,jdbcType=TIMESTAMP}
            </if>
            <if test="pushEndTime != null" >
                and push_end_time = #{pushEndTime,jdbcType=TIMESTAMP}
            </if>
            <if test="configDesc != null" >
                and config_desc = #{configDesc,jdbcType=VARCHAR}
            </if>
            <if test="operatorId != null" >
                and operator_id = #{operatorId,jdbcType=VARCHAR}
            </if>
            <if test="operatorName != null" >
                and operator_name = #{operatorName,jdbcType=VARCHAR}
            </if>
            <if test="status != null" >
                and status = #{status,jdbcType=TINYINT}
            </if>
            <if test="createTime != null" >
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null" >
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="configHex != null" >
                and config_hex = #{configHex,jdbcType=VARCHAR}
            </if>
            <if test="appInstanceGroups != null" >
                and app_instance_groups = #{appInstanceGroups,jdbcType=LONGVARCHAR}
            </if>
            <if test="appServices != null" >
                and app_services = #{appServices,jdbcType=LONGVARCHAR}
            </if>
            <if test="appApiLocations != null" >
                and app_api_locations = #{appApiLocations,jdbcType=LONGVARCHAR}
            </if>
            <if test="appRules != null" >
                and app_rules = #{appRules,jdbcType=LONGVARCHAR}
            </if>
            <if test="dispatches != null" >
                and dispatches = #{dispatches,jdbcType=LONGVARCHAR}
            </if>
            <if test="apiTagMap != null" >
                and api_tag_map = #{apiTagMap,jdbcType=LONGVARCHAR}
            </if>
        </where>
    </select>
</mapper>