<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dispatch.dao.IDispatchTestDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.dispatch.entity.DispatchTestDO" >
        <id column="dispatch_test_id" property="dispatchTestId" jdbcType="INTEGER" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="dispatch_id" property="dispatchId" jdbcType="INTEGER" />
        <result column="dispatch_test_name" property="dispatchTestName" jdbcType="VARCHAR" />
        <result column="scheme" property="scheme" jdbcType="VARCHAR" />
        <result column="address" property="address" jdbcType="VARCHAR" />
        <result column="port" property="port" jdbcType="INTEGER" />
        <result column="host" property="host" jdbcType="VARCHAR" />
        <result column="app_service_id" property="appServiceId" jdbcType="INTEGER" />
        <result column="method" property="method" jdbcType="VARCHAR" />
        <result column="urlPath" property="urlpath" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="TINYINT" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="dispatch_test_desc" property="dispatchTestDesc" jdbcType="VARCHAR" />
    </resultMap>

    <resultMap id="ResultMapWithBLOBs" type="com.cmpay.hacp.dispatch.entity.DispatchTestDO" extends="BaseResultMap" >
        <result column="queryParams" property="queryparams" jdbcType="LONGVARCHAR" />
        <result column="cookies" property="cookies" jdbcType="LONGVARCHAR" />
        <result column="requestHeaders" property="requestheaders" jdbcType="LONGVARCHAR" />
        <result column="requestBody" property="requestbody" jdbcType="LONGVARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        dispatch_test_id, workspace_id, dispatch_id, dispatch_test_name, scheme, address, 
        port, host, app_service_id, method, urlPath, status, operator_id, operator_name, 
        create_time, update_time, dispatch_test_desc
    </sql>

    <sql id="Blob_Column_List" >
        queryParams, cookies, requestHeaders, requestBody
    </sql>

    <select id="get" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Integer" >
        select 
        <include refid="Base_Column_List" />
        ,
        <include refid="Blob_Column_List" />
        from dispatch_test
        where dispatch_test_id = #{dispatchTestId,jdbcType=INTEGER}
    </select>

    <delete id="delete" parameterType="java.lang.Integer" >
        delete from dispatch_test
        where dispatch_test_id = #{dispatchTestId,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.dispatch.entity.DispatchTestDO" useGeneratedKeys="true" keyProperty="dispatchTestId" >
        insert into dispatch_test
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="workspaceId != null" >
                workspace_id,
            </if>
            <if test="dispatchId != null" >
                dispatch_id,
            </if>
            <if test="dispatchTestName != null" >
                dispatch_test_name,
            </if>
            <if test="scheme != null" >
                scheme,
            </if>
            <if test="address != null" >
                address,
            </if>
            <if test="port != null" >
                port,
            </if>
            <if test="host != null" >
                host,
            </if>
            <if test="appServiceId != null" >
                app_service_id,
            </if>
            <if test="method != null" >
                method,
            </if>
            <if test="urlpath != null" >
                urlPath,
            </if>
            <if test="status != null" >
                status,
            </if>
            <if test="operatorId != null" >
                operator_id,
            </if>
            <if test="operatorName != null" >
                operator_name,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateTime != null" >
                update_time,
            </if>
            <if test="dispatchTestDesc != null" >
                dispatch_test_desc,
            </if>
            <if test="queryparams != null" >
                queryParams,
            </if>
            <if test="cookies != null" >
                cookies,
            </if>
            <if test="requestheaders != null" >
                requestHeaders,
            </if>
            <if test="requestbody != null" >
                requestBody,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="workspaceId != null" >
                #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="dispatchId != null" >
                #{dispatchId,jdbcType=INTEGER},
            </if>
            <if test="dispatchTestName != null" >
                #{dispatchTestName,jdbcType=VARCHAR},
            </if>
            <if test="scheme != null" >
                #{scheme,jdbcType=VARCHAR},
            </if>
            <if test="address != null" >
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="port != null" >
                #{port,jdbcType=INTEGER},
            </if>
            <if test="host != null" >
                #{host,jdbcType=VARCHAR},
            </if>
            <if test="appServiceId != null" >
                #{appServiceId,jdbcType=INTEGER},
            </if>
            <if test="method != null" >
                #{method,jdbcType=VARCHAR},
            </if>
            <if test="urlpath != null" >
                #{urlpath,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                #{status,jdbcType=TINYINT},
            </if>
            <if test="operatorId != null" >
                #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="dispatchTestDesc != null" >
                #{dispatchTestDesc,jdbcType=VARCHAR},
            </if>
            <if test="queryparams != null" >
                #{queryparams,jdbcType=LONGVARCHAR},
            </if>
            <if test="cookies != null" >
                #{cookies,jdbcType=LONGVARCHAR},
            </if>
            <if test="requestheaders != null" >
                #{requestheaders,jdbcType=LONGVARCHAR},
            </if>
            <if test="requestbody != null" >
                #{requestbody,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.dispatch.entity.DispatchTestDO" >
        update dispatch_test
        <set >
            <if test="workspaceId != null" >
                workspace_id = #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="dispatchId != null" >
                dispatch_id = #{dispatchId,jdbcType=INTEGER},
            </if>
            <if test="dispatchTestName != null" >
                dispatch_test_name = #{dispatchTestName,jdbcType=VARCHAR},
            </if>
            <if test="scheme != null" >
                scheme = #{scheme,jdbcType=VARCHAR},
            </if>
            <if test="address != null" >
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="port != null" >
                port = #{port,jdbcType=INTEGER},
            </if>
            <if test="host != null" >
                host = #{host,jdbcType=VARCHAR},
            </if>
            <if test="appServiceId != null" >
                app_service_id = #{appServiceId,jdbcType=INTEGER},
            </if>
            <if test="method != null" >
                method = #{method,jdbcType=VARCHAR},
            </if>
            <if test="urlpath != null" >
                urlPath = #{urlpath,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="operatorId != null" >
                operator_id = #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                operator_name = #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="dispatchTestDesc != null" >
                dispatch_test_desc = #{dispatchTestDesc,jdbcType=VARCHAR},
            </if>
            <if test="queryparams != null" >
                queryParams = #{queryparams,jdbcType=LONGVARCHAR},
            </if>
            <if test="cookies != null" >
                cookies = #{cookies,jdbcType=LONGVARCHAR},
            </if>
            <if test="requestheaders != null" >
                requestHeaders = #{requestheaders,jdbcType=LONGVARCHAR},
            </if>
            <if test="requestbody != null" >
                requestBody = #{requestbody,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where dispatch_test_id = #{dispatchTestId,jdbcType=INTEGER}
    </update>

    <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.cmpay.hacp.dispatch.entity.DispatchTestDO" >
        update dispatch_test
        set workspace_id = #{workspaceId,jdbcType=VARCHAR},
            dispatch_id = #{dispatchId,jdbcType=INTEGER},
            dispatch_test_name = #{dispatchTestName,jdbcType=VARCHAR},
            scheme = #{scheme,jdbcType=VARCHAR},
            address = #{address,jdbcType=VARCHAR},
            port = #{port,jdbcType=INTEGER},
            host = #{host,jdbcType=VARCHAR},
            app_service_id = #{appServiceId,jdbcType=INTEGER},
            method = #{method,jdbcType=VARCHAR},
            urlPath = #{urlpath,jdbcType=VARCHAR},
            status = #{status,jdbcType=TINYINT},
            operator_id = #{operatorId,jdbcType=VARCHAR},
            operator_name = #{operatorName,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            dispatch_test_desc = #{dispatchTestDesc,jdbcType=VARCHAR},
            queryParams = #{queryparams,jdbcType=LONGVARCHAR},
            cookies = #{cookies,jdbcType=LONGVARCHAR},
            requestHeaders = #{requestheaders,jdbcType=LONGVARCHAR},
            requestBody = #{requestbody,jdbcType=LONGVARCHAR}
        where dispatch_test_id = #{dispatchTestId,jdbcType=INTEGER}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.dispatch.entity.DispatchTestDO" >
        select 
        <include refid="Base_Column_List" />
        from dispatch_test
        <where >
            <if test="dispatchTestId != null" >
                and dispatch_test_id = #{dispatchTestId,jdbcType=INTEGER}
            </if>
            <if test="workspaceId != null" >
                and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="dispatchId != null" >
                and dispatch_id = #{dispatchId,jdbcType=INTEGER}
            </if>
            <if test="dispatchTestName != null" >
                and dispatch_test_name = #{dispatchTestName,jdbcType=VARCHAR}
            </if>
            <if test="scheme != null" >
                and scheme = #{scheme,jdbcType=VARCHAR}
            </if>
            <if test="address != null" >
                and address = #{address,jdbcType=VARCHAR}
            </if>
            <if test="port != null" >
                and port = #{port,jdbcType=INTEGER}
            </if>
            <if test="host != null" >
                and host = #{host,jdbcType=VARCHAR}
            </if>
            <if test="appServiceId != null" >
                and app_service_id = #{appServiceId,jdbcType=INTEGER}
            </if>
            <if test="method != null" >
                and method = #{method,jdbcType=VARCHAR}
            </if>
            <if test="urlpath != null" >
                and urlPath = #{urlpath,jdbcType=VARCHAR}
            </if>
            <if test="status != null" >
                and status = #{status,jdbcType=TINYINT}
            </if>
            <if test="operatorId != null" >
                and operator_id = #{operatorId,jdbcType=VARCHAR}
            </if>
            <if test="operatorName != null" >
                and operator_name = #{operatorName,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null" >
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null" >
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="dispatchTestDesc != null" >
                and dispatch_test_desc = #{dispatchTestDesc,jdbcType=VARCHAR}
            </if>
            <if test="queryparams != null" >
                and queryParams = #{queryparams,jdbcType=LONGVARCHAR}
            </if>
            <if test="cookies != null" >
                and cookies = #{cookies,jdbcType=LONGVARCHAR}
            </if>
            <if test="requestheaders != null" >
                and requestHeaders = #{requestheaders,jdbcType=LONGVARCHAR}
            </if>
            <if test="requestbody != null" >
                and requestBody = #{requestbody,jdbcType=LONGVARCHAR}
            </if>
        </where>
    </select>
</mapper>