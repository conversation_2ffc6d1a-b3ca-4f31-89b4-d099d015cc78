<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dispatch.dao.IRuleExpressionExtDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.dispatch.entity.RuleExpressionDO" >
        <id column="rule_expression_id" property="ruleExpressionId" jdbcType="INTEGER" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="dispatch_rule_id" property="dispatchRuleId" jdbcType="INTEGER" />
        <result column="dispatch_rule_type" property="dispatchRuleType" jdbcType="VARCHAR" />
        <result column="priority_level" property="priorityLevel" jdbcType="TINYINT" />
        <result column="param_extractor" property="paramExtractor" jdbcType="VARCHAR" />
        <result column="calc_operator" property="calcOperator" jdbcType="VARCHAR" />
        <result column="result_value" property="resultValue" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="TINYINT" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <resultMap id="ResultMapWithBLOBs" type="com.cmpay.hacp.dispatch.entity.RuleExpressionDO" extends="BaseResultMap" >
        <result column="zone_weights" property="zoneWeights" jdbcType="LONGVARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        rule_expression_id, workspace_id, dispatch_rule_id, dispatch_rule_type, priority_level, 
        param_extractor, calc_operator, result_value, status, operator_id, operator_name, 
        create_time, update_time
    </sql>

    <sql id="Blob_Column_List" >
        zone_weights
    </sql>

    <delete id="deleteByRuleId"  parameterType="java.lang.Integer" >
        delete from rule_expression
        where dispatch_rule_id = #{dispatchRuleId,jdbcType=INTEGER}
    </delete>

    <select id="likeFind" resultMap="ResultMapWithBLOBs" parameterType="com.cmpay.hacp.dispatch.entity.RuleExpressionDO" >
        select
        <include refid="Base_Column_List" />
        ,
        <include refid="Blob_Column_List" />
        from rule_expression
        <where >
            <if test="ruleExpressionId != null" >
                and rule_expression_id = #{ruleExpressionId,jdbcType=INTEGER}
            </if>
            <if test="workspaceId != null" >
                and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="dispatchRuleId != null" >
                and dispatch_rule_id = #{dispatchRuleId,jdbcType=INTEGER}
            </if>
            <if test="dispatchRuleType != null" >
                and dispatch_rule_type = #{dispatchRuleType,jdbcType=VARCHAR}
            </if>
            <if test="priorityLevel != null" >
                and priority_level = #{priorityLevel,jdbcType=TINYINT}
            </if>
            <if test="paramExtractor != null" >
                and param_extractor = #{paramExtractor,jdbcType=VARCHAR}
            </if>
            <if test="calcOperator != null" >
                and calc_operator = #{calcOperator,jdbcType=VARCHAR}
            </if>
            <if test="resultValue != null" >
                and result_value = #{resultValue,jdbcType=VARCHAR}
            </if>
            <if test="status != null" >
                and status = #{status,jdbcType=TINYINT}
            </if>
            <if test="operatorId != null" >
                and operator_id = #{operatorId,jdbcType=VARCHAR}
            </if>
            <if test="operatorName != null" >
                and operator_name = #{operatorName,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null" >
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null" >
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="zoneWeights != null" >
                and zone_weights like concat('%',#{zoneWeights,jdbcType=LONGVARCHAR},'%')
            </if>
        </where>
    </select>
</mapper>