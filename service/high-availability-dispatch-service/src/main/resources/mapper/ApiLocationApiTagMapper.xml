<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dispatch.dao.IApiLocationApiTagDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.dispatch.entity.ApiLocationApiTagDO" >
        <id column="api_location_id" property="apiLocationId" jdbcType="INTEGER" />
        <id column="api_tag_id" property="apiTagId" jdbcType="INTEGER" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="TINYINT" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        api_location_id, api_tag_id, workspace_id, status, operator_id, operator_name, create_time, 
        update_time
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.dispatch.entity.ApiLocationApiTagDOKey" >
        select 
        <include refid="Base_Column_List" />
        from api_location_api_tag
        where api_location_id = #{apiLocationId,jdbcType=INTEGER}
          and api_tag_id = #{apiTagId,jdbcType=INTEGER}
    </select>

    <delete id="delete" parameterType="com.cmpay.hacp.dispatch.entity.ApiLocationApiTagDOKey" >
        delete from api_location_api_tag
        where api_location_id = #{apiLocationId,jdbcType=INTEGER}
          and api_tag_id = #{apiTagId,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.dispatch.entity.ApiLocationApiTagDO" >
        insert into api_location_api_tag
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="apiLocationId != null" >
                api_location_id,
            </if>
            <if test="apiTagId != null" >
                api_tag_id,
            </if>
            <if test="workspaceId != null" >
                workspace_id,
            </if>
            <if test="status != null" >
                status,
            </if>
            <if test="operatorId != null" >
                operator_id,
            </if>
            <if test="operatorName != null" >
                operator_name,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateTime != null" >
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="apiLocationId != null" >
                #{apiLocationId,jdbcType=INTEGER},
            </if>
            <if test="apiTagId != null" >
                #{apiTagId,jdbcType=INTEGER},
            </if>
            <if test="workspaceId != null" >
                #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                #{status,jdbcType=TINYINT},
            </if>
            <if test="operatorId != null" >
                #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.dispatch.entity.ApiLocationApiTagDO" >
        update api_location_api_tag
        <set >
            <if test="workspaceId != null" >
                workspace_id = #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="operatorId != null" >
                operator_id = #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                operator_name = #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where api_location_id = #{apiLocationId,jdbcType=INTEGER}
          and api_tag_id = #{apiTagId,jdbcType=INTEGER}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.dispatch.entity.ApiLocationApiTagDO" >
        select 
        <include refid="Base_Column_List" />
        from api_location_api_tag
        <where >
            <if test="apiLocationId != null" >
                and api_location_id = #{apiLocationId,jdbcType=INTEGER}
            </if>
            <if test="apiTagId != null" >
                and api_tag_id = #{apiTagId,jdbcType=INTEGER}
            </if>
            <if test="workspaceId != null" >
                and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="status != null" >
                and status = #{status,jdbcType=TINYINT}
            </if>
            <if test="operatorId != null" >
                and operator_id = #{operatorId,jdbcType=VARCHAR}
            </if>
            <if test="operatorName != null" >
                and operator_name = #{operatorName,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null" >
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null" >
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </select>
</mapper>