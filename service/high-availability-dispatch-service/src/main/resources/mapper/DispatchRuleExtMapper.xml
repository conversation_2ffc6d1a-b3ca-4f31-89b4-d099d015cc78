<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dispatch.dao.IDispatchRuleExtDao" >

    <resultMap id="RuleResultMap" type="com.cmpay.hacp.dispatch.entity.DispatchRuleDO" >
        <id column="dispatch_rule_id" property="dispatchRuleId" jdbcType="INTEGER" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="dispatch_rule_name" property="dispatchRuleName" jdbcType="VARCHAR" />
        <result column="dispatch_rule_cn" property="dispatchRuleCn" jdbcType="VARCHAR" />
        <result column="dispatch_rule_desc" property="dispatchRuleDesc" jdbcType="VARCHAR" />
        <result column="dispatch_rule_type" property="dispatchRuleType" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="TINYINT" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Rule_Column_List" >
        dispatch_rule_id, workspace_id, dispatch_rule_cn, dispatch_rule_name, dispatch_rule_desc,
        dispatch_rule_type, status, operator_id, operator_name, create_time, update_time
    </sql>

    <select id="likeFind" resultMap="RuleResultMap" parameterType="com.cmpay.hacp.dispatch.entity.DispatchRuleDO" >
        select 
        <include refid="Rule_Column_List" />
        from dispatch_rule
        <where >
            <if test="dispatchRuleId != null" >
                and dispatch_rule_id = #{dispatchRuleId,jdbcType=INTEGER}
            </if>
            <if test="workspaceId != null" >
                and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="dispatchRuleName != null" >
                and dispatch_rule_name like concat('%',#{dispatchRuleName,jdbcType=VARCHAR},'%')
            </if>
            <if test="dispatchRuleCn != null" >
                and dispatch_rule_cn like concat('%',#{dispatchRuleCn,jdbcType=VARCHAR},'%')
            </if>
            <if test="dispatchRuleDesc != null" >
                and dispatch_rule_desc = #{dispatchRuleDesc,jdbcType=VARCHAR}
            </if>
            <if test="dispatchRuleType != null" >
                and dispatch_rule_type = #{dispatchRuleType,jdbcType=VARCHAR}
            </if>
            <if test="status != null" >
                and status = #{status,jdbcType=TINYINT}
            </if>
            <if test="operatorId != null" >
                and operator_id = #{operatorId,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null" >
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null" >
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </select>
    <select id="existFind" resultType="com.cmpay.hacp.dispatch.entity.DispatchRuleDO">
        select
        <include refid="Rule_Column_List" />
        from dispatch_rule
        <where>
            workspace_id = #{workspaceId,jdbcType=VARCHAR}
            and dispatch_rule_name = #{dispatchRuleName,jdbcType=VARCHAR}
            <if test="dispatchRuleId != null " >
                and dispatch_rule_id != #{dispatchRuleId,jdbcType=INTEGER}
            </if>
        </where>
    </select>
</mapper>