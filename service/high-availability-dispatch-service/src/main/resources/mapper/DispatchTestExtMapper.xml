<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dispatch.dao.IDispatchTestExtDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.dispatch.entity.DispatchTestDO" >
        <id column="dispatch_test_id" property="dispatchTestId" jdbcType="INTEGER" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="dispatch_id" property="dispatchId" jdbcType="INTEGER" />
        <result column="dispatch_test_name" property="dispatchTestName" jdbcType="VARCHAR" />
        <result column="scheme" property="scheme" jdbcType="VARCHAR" />
        <result column="address" property="address" jdbcType="VARCHAR" />
        <result column="port" property="port" jdbcType="INTEGER" />
        <result column="host" property="host" jdbcType="VARCHAR" />
        <result column="app_service_id" property="appServiceId" jdbcType="INTEGER" />
        <result column="method" property="method" jdbcType="VARCHAR" />
        <result column="urlPath" property="urlpath" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="TINYINT" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <resultMap id="ResultMapWithBLOBs" type="com.cmpay.hacp.dispatch.entity.DispatchTestDO" extends="BaseResultMap" >
        <result column="queryParams" property="queryparams" jdbcType="LONGVARCHAR" />
        <result column="cookies" property="cookies" jdbcType="LONGVARCHAR" />
        <result column="requestHeaders" property="requestheaders" jdbcType="LONGVARCHAR" />
        <result column="requestBody" property="requestbody" jdbcType="LONGVARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        dispatch_test_id, workspace_id, dispatch_id, dispatch_test_name, scheme, address, 
        port, host, app_service_id, method, urlPath, status, operator_id, operator_name, 
        create_time, update_time
    </sql>

    <sql id="Blob_Column_List" >
        queryParams, cookies, requestHeaders, requestBody
    </sql>

    <select id="likeFind" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.dispatch.entity.DispatchTestDO" >
        select 
        <include refid="Base_Column_List" />
        from dispatch_test
        <where >
            <if test="dispatchTestId != null" >
                and dispatch_test_id = #{dispatchTestId,jdbcType=INTEGER}
            </if>
            <if test="workspaceId != null" >
                and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="dispatchId != null" >
                and dispatch_id = #{dispatchId,jdbcType=INTEGER}
            </if>
            <if test="dispatchTestName != null" >
                and dispatch_test_name like concat('%',#{dispatchTestName,jdbcType=VARCHAR},'%')
            </if>
            <if test="scheme != null" >
                and scheme = #{scheme,jdbcType=VARCHAR}
            </if>
            <if test="address != null" >
                and address = #{address,jdbcType=VARCHAR}
            </if>
            <if test="port != null" >
                and port = #{port,jdbcType=INTEGER}
            </if>
            <if test="host != null" >
                and host = #{host,jdbcType=VARCHAR}
            </if>
            <if test="appServiceId != null" >
                and app_service_id = #{appServiceId,jdbcType=INTEGER}
            </if>
            <if test="method != null" >
                and method = #{method,jdbcType=VARCHAR}
            </if>
            <if test="urlpath != null" >
                and urlPath = #{urlpath,jdbcType=VARCHAR}
            </if>
            <if test="status != null" >
                and status = #{status,jdbcType=TINYINT}
            </if>
            <if test="operatorId != null" >
                and operator_id = #{operatorId,jdbcType=VARCHAR}
            </if>
            <if test="operatorName != null" >
                and operator_name = #{operatorName,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null" >
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null" >
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="queryparams != null" >
                and queryParams = #{queryparams,jdbcType=LONGVARCHAR}
            </if>
            <if test="cookies != null" >
                and cookies = #{cookies,jdbcType=LONGVARCHAR}
            </if>
            <if test="requestheaders != null" >
                and requestHeaders = #{requestheaders,jdbcType=LONGVARCHAR}
            </if>
            <if test="requestbody != null" >
                and requestBody = #{requestbody,jdbcType=LONGVARCHAR}
            </if>
        </where>
    </select>
</mapper>