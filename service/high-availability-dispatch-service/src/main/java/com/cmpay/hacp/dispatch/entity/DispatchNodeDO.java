/*
 * @ClassName DispatchNodeDO
 * @Description 
 * @version 1.0
 * @Date 2024-08-16 10:59:34
 */
package com.cmpay.hacp.dispatch.entity;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;
import java.time.LocalDateTime;

@DataObject
public class DispatchNodeDO extends BaseDO {
    /**
     * @Fields dispatchNodeId 调度节点ID
     */
    private Integer dispatchNodeId;
    /**
     * @Fields dispatchNodeName 调度节点名称
     */
    private String dispatchNodeName;
    /**
     * @Fields workspaceId 项目空间ID
     */
    private String workspaceId;
    /**
     * @Fields zoneId 机房ID
     */
    private Long zoneId;
    /**
     * @Fields ipPort 代理机房IP端口
     */
    private String ipPort;
    /**
     * @Fields grayDispatchConfigId 灰度配置调度版本ID
     */
    private Integer grayDispatchConfigId;
    /**
     * @Fields dispatchConfigId 全局配置调度版本ID
     */
    private Integer dispatchConfigId;
    /**
     * @Fields runningDispatchConfigId 运行的调度版本ID
     */
    private Integer runningDispatchConfigId;
    /**
     * @Fields grayDispatchVersion 灰度配置调度版本ID
     */
    private String grayDispatchVersion;
    /**
     * @Fields dispatchVersion 全局配置调度版本
     */
    private String dispatchVersion;
    /**
     * @Fields runningDispatchVersion 运行的调度版本
     */
    private String runningDispatchVersion;
    /**
     * @Fields status 0:禁用  1:启用
     */
    private Byte status;
    /**
     * @Fields operatorId 操作员
     */
    private String operatorId;
    /**
     * @Fields operatorName 操作员名称
     */
    private String operatorName;
    /**
     * @Fields pushTimes 推送次数
     */
    private Byte pushTimes;
    /**
     * @Fields pushTime 推送时间
     */
    private LocalDateTime pushTime;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields updateTime 修改时间
     */
    private LocalDateTime updateTime;

    public Integer getDispatchNodeId() {
        return dispatchNodeId;
    }

    public void setDispatchNodeId(Integer dispatchNodeId) {
        this.dispatchNodeId = dispatchNodeId;
    }

    public String getDispatchNodeName() {
        return dispatchNodeName;
    }

    public void setDispatchNodeName(String dispatchNodeName) {
        this.dispatchNodeName = dispatchNodeName;
    }

    public String getWorkspaceId() {
        return workspaceId;
    }

    public void setWorkspaceId(String workspaceId) {
        this.workspaceId = workspaceId;
    }

    public Long getZoneId() {
        return zoneId;
    }

    public void setZoneId(Long zoneId) {
        this.zoneId = zoneId;
    }

    public String getIpPort() {
        return ipPort;
    }

    public void setIpPort(String ipPort) {
        this.ipPort = ipPort;
    }

    public Integer getGrayDispatchConfigId() {
        return grayDispatchConfigId;
    }

    public void setGrayDispatchConfigId(Integer grayDispatchConfigId) {
        this.grayDispatchConfigId = grayDispatchConfigId;
    }

    public Integer getDispatchConfigId() {
        return dispatchConfigId;
    }

    public void setDispatchConfigId(Integer dispatchConfigId) {
        this.dispatchConfigId = dispatchConfigId;
    }

    public Integer getRunningDispatchConfigId() {
        return runningDispatchConfigId;
    }

    public void setRunningDispatchConfigId(Integer runningDispatchConfigId) {
        this.runningDispatchConfigId = runningDispatchConfigId;
    }

    public String getGrayDispatchVersion() {
        return grayDispatchVersion;
    }

    public void setGrayDispatchVersion(String grayDispatchVersion) {
        this.grayDispatchVersion = grayDispatchVersion;
    }

    public String getDispatchVersion() {
        return dispatchVersion;
    }

    public void setDispatchVersion(String dispatchVersion) {
        this.dispatchVersion = dispatchVersion;
    }

    public String getRunningDispatchVersion() {
        return runningDispatchVersion;
    }

    public void setRunningDispatchVersion(String runningDispatchVersion) {
        this.runningDispatchVersion = runningDispatchVersion;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public Byte getPushTimes() {
        return pushTimes;
    }

    public void setPushTimes(Byte pushTimes) {
        this.pushTimes = pushTimes;
    }

    public LocalDateTime getPushTime() {
        return pushTime;
    }

    public void setPushTime(LocalDateTime pushTime) {
        this.pushTime = pushTime;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
}