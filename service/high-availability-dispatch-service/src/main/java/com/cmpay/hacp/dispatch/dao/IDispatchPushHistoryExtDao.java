/*
 * @ClassName IDispatchPushHistoryDao
 * @Description 
 * @version 1.0
 * @Date 2024-07-02 13:59:52
 */
package com.cmpay.hacp.dispatch.dao;

import com.cmpay.hacp.dispatch.entity.DispatchPushHistoryDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface IDispatchPushHistoryExtDao extends IDispatchPushHistoryDao {

    List<DispatchPushHistoryDO> getConfigPublishHistory(DispatchPushHistoryDO dispatchPushHistoryDO);
    List<DispatchPushHistoryDO> likeFind(DispatchPushHistoryDO dispatchPushHistoryDO);
    List<DispatchPushHistoryDO> skipLockedUnPushedNodeByConfigId(DispatchPushHistoryDO dispatchPushHistoryDO);
    int countWorkspaceHistoryPushStatus(DispatchPushHistoryDO dispatchPushHistoryDO);


}