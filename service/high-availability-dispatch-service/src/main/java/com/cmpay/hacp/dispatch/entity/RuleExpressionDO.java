/*
 * @ClassName RuleExpressionDO
 * @Description 
 * @version 1.0
 * @Date 2024-08-16 10:59:34
 */
package com.cmpay.hacp.dispatch.entity;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;
import java.time.LocalDateTime;

@DataObject
public class RuleExpressionDO extends BaseDO {
    /**
     * @Fields ruleExpressionId 调度规则表达式ID
     */
    private Integer ruleExpressionId;
    /**
     * @Fields workspaceId 项目空间ID
     */
    private String workspaceId;
    /**
     * @Fields dispatchRuleId 调度规则ID
     */
    private Integer dispatchRuleId;
    /**
     * @Fields dispatchRuleType 分流类型
     */
    private String dispatchRuleType;
    /**
     * @Fields priorityLevel 规则优先级
     */
    private Byte priorityLevel;
    /**
     * @Fields paramExtractor 参数名/取值表达式
     */
    private String paramExtractor;
    /**
     * @Fields calcOperator 计算操作符
     */
    private String calcOperator;
    /**
     * @Fields resultValue 参数值
     */
    private String resultValue;
    /**
     * @Fields status 0:禁用  1:启用
     */
    private Byte status;
    /**
     * @Fields operatorId 操作员
     */
    private String operatorId;
    /**
     * @Fields operatorName 操作员名称
     */
    private String operatorName;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields updateTime 修改时间
     */
    private LocalDateTime updateTime;
    /**
     * @Fields zoneWeights 分机房分流权重（机房标识：权重）
     */
    private String zoneWeights;

    public Integer getRuleExpressionId() {
        return ruleExpressionId;
    }

    public void setRuleExpressionId(Integer ruleExpressionId) {
        this.ruleExpressionId = ruleExpressionId;
    }

    public String getWorkspaceId() {
        return workspaceId;
    }

    public void setWorkspaceId(String workspaceId) {
        this.workspaceId = workspaceId;
    }

    public Integer getDispatchRuleId() {
        return dispatchRuleId;
    }

    public void setDispatchRuleId(Integer dispatchRuleId) {
        this.dispatchRuleId = dispatchRuleId;
    }

    public String getDispatchRuleType() {
        return dispatchRuleType;
    }

    public void setDispatchRuleType(String dispatchRuleType) {
        this.dispatchRuleType = dispatchRuleType;
    }

    public Byte getPriorityLevel() {
        return priorityLevel;
    }

    public void setPriorityLevel(Byte priorityLevel) {
        this.priorityLevel = priorityLevel;
    }

    public String getParamExtractor() {
        return paramExtractor;
    }

    public void setParamExtractor(String paramExtractor) {
        this.paramExtractor = paramExtractor;
    }

    public String getCalcOperator() {
        return calcOperator;
    }

    public void setCalcOperator(String calcOperator) {
        this.calcOperator = calcOperator;
    }

    public String getResultValue() {
        return resultValue;
    }

    public void setResultValue(String resultValue) {
        this.resultValue = resultValue;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getZoneWeights() {
        return zoneWeights;
    }

    public void setZoneWeights(String zoneWeights) {
        this.zoneWeights = zoneWeights;
    }
}