package com.cmpay.hacp.dispatch.service.impl;

import com.cmpay.hacp.dispatch.bo.DispatchConfigBO;
import com.cmpay.hacp.dispatch.bo.DispatchPushHistoryBO;
import com.cmpay.hacp.dispatch.client.AgentNodeFeignClient;
import com.cmpay.hacp.dispatch.client.FeignClientCache;
import com.cmpay.hacp.dispatch.dao.IDispatchConfigExtDao;
import com.cmpay.hacp.dispatch.dao.IDispatchNodeExtDao;
import com.cmpay.hacp.dispatch.dao.IDispatchPushHistoryExtDao;
import com.cmpay.hacp.dispatch.entity.DispatchConfigDO;
import com.cmpay.hacp.dispatch.entity.DispatchNodeDO;
import com.cmpay.hacp.dispatch.entity.DispatchNodeQueryDO;
import com.cmpay.hacp.dispatch.entity.DispatchPushHistoryDO;
import com.cmpay.hacp.dispatch.service.DispatchNodeService;
import com.cmpay.hacp.dispatch.service.DispatchPushService;
import com.cmpay.hacp.dispatch.service.DispatchRuleService;
import com.cmpay.hacp.dispatch.service.DispatchZoneService;
import com.cmpay.hacp.enums.ByteStatusEnum;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.hafr.agent.dto.ConfigStatusRequest;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.data.DefaultDTO;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class DispatchPushServiceImpl implements DispatchPushService {
    @Autowired
    private DispatchNodeService dispatchNodeService;
    @Autowired
    private IDispatchConfigExtDao dispatchConfigDao;
    @Autowired
    private IDispatchNodeExtDao dispatchNodeDao;
    @Autowired
    private IDispatchPushHistoryExtDao dispatchPushHistoryDao;
    @Autowired
    private DispatchZoneService dispatchZoneService;
    @Autowired
    private DispatchRuleService dispatchRuleService;

    @Async
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void pushConfigToNodeList(DispatchConfigBO dispatchConfigBO) {
        if(StringUtils.isBlank(dispatchConfigBO.getWorkspaceId())) {
            return;
        }
        DispatchPushHistoryDO findUnPushedPushNode = new DispatchPushHistoryDO();
        findUnPushedPushNode.setDispatchConfigId(dispatchConfigBO.getDispatchConfigId());
        findUnPushedPushNode.setPushTimes(dispatchConfigBO.getPushTimes());
        List<DispatchPushHistoryDO> skipLockedUnPushedNodeList = dispatchPushHistoryDao.skipLockedUnPushedNodeByConfigId(findUnPushedPushNode);
        Map<Long,String> zoneMap = dispatchZoneService.getSimpleMap(dispatchConfigBO.getWorkspaceId());
        dispatchConfigBO.setZoneMap(zoneMap);
        if(skipLockedUnPushedNodeList != null && !skipLockedUnPushedNodeList.isEmpty()) {
            for (DispatchPushHistoryDO unPushedNode : skipLockedUnPushedNodeList) {

                DefaultRspDTO<NoBody> pushStatus = null;
                try {
                    pushStatus = pushToAgentNode(dispatchConfigBO, BeanConvertUtil.convert(unPushedNode, DispatchPushHistoryBO.class));
                    unPushedNode.setMsgCd(pushStatus.getMsgCd());
                    unPushedNode.setMsgInfo(pushStatus.getMsgInfo());
                    if(JudgeUtils.isSuccess(pushStatus)) {
                        unPushedNode.setPushEndTime(LocalDateTime.now());
                        unPushedNode.setPushStatus(ByteStatusEnum.ENABLE.getValue());
                    }
                } catch (Exception e) {
                    unPushedNode.setMsgCd(StringUtils.substring(e.getClass().getSimpleName(), 0, 64));
                    unPushedNode.setMsgInfo(StringUtils.substring(e.getMessage(), 0, 64));
                }
                dispatchPushHistoryDao.update(unPushedNode);

                if(pushStatus != null && JudgeUtils.isSuccess(pushStatus)) {
                    DispatchNodeDO dispatchNodeDO = dispatchNodeDao.get(unPushedNode.getDispatchNodeId());
                    if(dispatchNodeDO != null) {
                        dispatchNodeDO.setRunningDispatchConfigId(unPushedNode.getDispatchConfigId());
                        dispatchNodeDO.setRunningDispatchVersion(dispatchNodeDO.getDispatchVersion());
                        dispatchNodeDO.setPushTime(LocalDateTime.now());
                        dispatchNodeDao.update(dispatchNodeDO);
                    }

                }
            }
        }
        checkAndFinishPushNode(dispatchConfigBO);
    }

    private DefaultRspDTO<NoBody> pushToAgentNode(DispatchConfigBO dispatchConfigBO, DispatchPushHistoryBO unPushedNode) {
        dispatchConfigBO.convertJsonToList();
        DefaultDTO req = new DefaultDTO<>();

        try{
            DefaultRspDTO<NoBody> noBodyDefaultRspDTO = null;
            AgentNodeFeignClient nodeClient = FeignClientCache.getClient(AgentNodeFeignClient.class, unPushedNode.getNodeCallUrl());

            switch (unPushedNode.getPushStatus()) {
                case 0:
                    req.setBody(dispatchConfigBO.buildConfigUpdateRequest(unPushedNode));
                    noBodyDefaultRspDTO = nodeClient.configUpdate(req);
                    break;
                case 2:
                    ConfigStatusRequest configStatusRequest = new ConfigStatusRequest();
                    configStatusRequest.setApiMeta(dispatchConfigBO.convertToAgentApiMeta());
                    configStatusRequest.setOperation(ConfigStatusRequest.StatusOperation.DELETE);
                    req.setBody(configStatusRequest);
                    noBodyDefaultRspDTO = nodeClient.configStatusChange(req);
                    break;
            }

            if(noBodyDefaultRspDTO != null && JudgeUtils.isNotSuccess(noBodyDefaultRspDTO)) {
                log.info("call agent node exception, nodeId:{}, ip port:{}, configId:{}, configVersion:{}, req:{}, response:{}",
                        unPushedNode.getDispatchNodeId(), unPushedNode.getIpPort(), unPushedNode.getDispatchConfigId(), dispatchConfigBO.getDispatchVersion(), req, noBodyDefaultRspDTO);
            }
            return noBodyDefaultRspDTO;
        } catch (Exception e) {
            log.info("call agent node exception, nodeId:{}, ip port:{}, configId:{}, configVersion:{}, e:{}, em:{}",
                    unPushedNode.getDispatchNodeId(), unPushedNode.getIpPort(), unPushedNode.getDispatchConfigId(), dispatchConfigBO.getDispatchVersion(), e.getClass().getSimpleName(), e.getMessage());
            throw e;
        }
    }


    @Async
    public void checkAndFinishPushNode(DispatchConfigBO dispatchConfigBO) {

        DispatchConfigDO findConfig = dispatchConfigDao.get(dispatchConfigBO.getDispatchConfigId());

        DispatchPushHistoryDO dispatchPushHistoryDO = new DispatchPushHistoryDO();
        dispatchPushHistoryDO.setDispatchConfigId(dispatchConfigBO.getDispatchConfigId());
        dispatchPushHistoryDO.setPushTimes(dispatchConfigBO.getPushTimes());
        int notSyncHistoryCount = dispatchPushHistoryDao.countWorkspaceHistoryPushStatus(dispatchPushHistoryDO);
        if(notSyncHistoryCount > 0){
            log.info("config推送未完成 workspaceId:{}, 版本号:{}, 推送批次:{}, 还有{}个节点没同步完成", dispatchConfigBO.getWorkspaceId(), dispatchConfigBO.getDispatchVersion(), dispatchConfigBO.getPushTimes(), notSyncHistoryCount);
            return;
        }

        DispatchNodeQueryDO dispatchNodeDO = new DispatchNodeQueryDO();
        dispatchNodeDO.setWorkspaceId(dispatchConfigBO.getWorkspaceId());
        dispatchNodeDO.setStatus(ByteStatusEnum.ENABLE.getValue());
        int notSyncNodeCount = dispatchNodeDao.countWorkspaceNodePushStatus(dispatchNodeDO);
        if(notSyncNodeCount > 0){
            log.info("node同步未完成 workspaceId:{}, 版本号:{}, 推送批次:{}, 还有{}个节点没同步完成", dispatchConfigBO.getWorkspaceId(), dispatchConfigBO.getDispatchVersion(), dispatchConfigBO.getPushTimes(), notSyncNodeCount);
            return;
        }
        findConfig.setPushEndTime(LocalDateTime.now());
        dispatchConfigDao.update(findConfig);
        if(findConfig.getIsCurrentVersion() < 1) {
            rePushNewCurrentConfigVersion(dispatchConfigBO);
        }

    }

    private void rePushNewCurrentConfigVersion(DispatchConfigBO dispatchConfigBO) {
        log.warn("当前推送版本已经不是最新版本, 版本ID{}, 版本号{}, 推送批次:{}", dispatchConfigBO.getDispatchConfigId(), dispatchConfigBO.getDispatchVersion(), dispatchConfigBO.getPushTimes());
        DispatchConfigDO currentVersion = new DispatchConfigBO();
        currentVersion.setWorkspaceId(dispatchConfigBO.getWorkspaceId());
        currentVersion.setIsCurrentVersion(Byte.valueOf((byte)1));
        List<DispatchConfigDO> dispatchConfigDOS = dispatchConfigDao.find(currentVersion);
        if(dispatchConfigDOS == null || dispatchConfigDOS.isEmpty()) {
            log.warn("无可用版本, 版本ID{}, 版本号{}, 推送批次:{}", dispatchConfigBO.getDispatchConfigId(), dispatchConfigBO.getDispatchVersion(), dispatchConfigBO.getPushTimes());
        } else {
            currentVersion = dispatchConfigDOS.get(0);
            log.warn("当前推送版本版本ID{}, 版本号{}, 推送批次:{}, 最新推送版本版本ID{}, 版本号{}, 推送批次:{}",
                    dispatchConfigBO.getDispatchConfigId(), dispatchConfigBO.getDispatchVersion(), dispatchConfigBO.getPushTimes(),
                    currentVersion.getDispatchConfigId(), currentVersion.getDispatchVersion(), currentVersion.getPushTimes());
        }


    }

    @Override
    public void pushConfigToNodeByWorkspace(){
        DispatchConfigDO currentVersion = new DispatchConfigBO();
        currentVersion.setIsCurrentVersion(Byte.valueOf((byte)1));
        List<DispatchConfigDO> dispatchConfigDOS = dispatchConfigDao.find(currentVersion);
        if(dispatchConfigDOS != null && !dispatchConfigDOS.isEmpty()) {
            for (DispatchConfigDO dispatchConfigDO : dispatchConfigDOS) {
                pushConfigToNodeList(BeanConvertUtil.convert(dispatchConfigDao.get(dispatchConfigDO.getDispatchConfigId()), DispatchConfigBO.class));
            }
        }
    }

}
