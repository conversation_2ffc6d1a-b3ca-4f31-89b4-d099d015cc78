/*
 * @ClassName DispatchDO
 * @Description 
 * @version 1.0
 * @Date 2024-05-28 09:42:23
 */
package com.cmpay.hacp.dispatch.bo;

import com.cmpay.hacp.capable.TenantCapable;
import com.cmpay.hacp.dispatch.entity.DispatchTestDO;
import com.cmpay.hacp.dispatch.util.JsonSerializationUtils;
import com.cmpay.hafr.agent.util.ConfigCheckUtils;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
public class DispatchTestBO extends DispatchTestDO implements TenantCapable {

    private Map<String, String> cookieMap;
    private Map<String, String> requestheaderMap;
    private Map<String, String> queryParamsMap;

    private String appServiceName;

    public void check(){

        if (!ConfigCheckUtils.isValidParamName(this.getDispatchTestName())) {
            BusinessException.throwBusinessException("HFA00031");
        }

    }
    public void convertToJson(){

        if(JudgeUtils.isNotEmpty(this.cookieMap)) {
            this.setCookies(JsonSerializationUtils.toString(this.cookieMap));
        }
        if(JudgeUtils.isNotEmpty(this.requestheaderMap)) {
            this.setRequestheaders(JsonSerializationUtils.toString(this.requestheaderMap));
        }
        if(JudgeUtils.isNotEmpty(this.queryParamsMap)) {
            this.setQueryparams(JsonSerializationUtils.toString(this.queryParamsMap));
        }
    }
    public Map<String, String> getCookieMap(){

        if(StringUtils.isNotBlank(this.getCookies())) {
            return JsonSerializationUtils.toStringMap(this.getCookies());
        }
        return null;
    }

    public Map<String, String> getRequestheaderMap(){

        if(StringUtils.isNotBlank(this.getRequestheaders())) {
            return JsonSerializationUtils.toStringMap(this.getRequestheaders());
        }
        return null;
    }

    public Map<String, String> getQueryParamsMap(){

        if(StringUtils.isNotBlank(this.getQueryparams())) {
            return JsonSerializationUtils.toStringMap(this.getQueryparams());
        }
        return null;
    }
}