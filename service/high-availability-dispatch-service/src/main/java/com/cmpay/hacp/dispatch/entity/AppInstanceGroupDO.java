/*
 * @ClassName AppInstanceGroupDO
 * @Description 
 * @version 1.0
 * @Date 2024-08-16 10:59:34
 */
package com.cmpay.hacp.dispatch.entity;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;
import java.time.LocalDateTime;

@DataObject
public class AppInstanceGroupDO extends BaseDO {
    /**
     * @Fields instanceGroupId 业务服务组ID
     */
    private Integer instanceGroupId;
    /**
     * @Fields workspaceId 项目空间ID
     */
    private String workspaceId;
    /**
     * @Fields instanceGroupCn 
     */
    private String instanceGroupCn;
    /**
     * @Fields instanceGroupName 业务服务组名称
     */
    private String instanceGroupName;
    /**
     * @Fields instanceGroupDesc 业务服务组描述
     */
    private String instanceGroupDesc;
    /**
     * @Fields advancedSwitch 0:禁用  1:启用
     */
    private Byte advancedSwitch;
    /**
     * @Fields status 0:禁用  1:启用
     */
    private Byte status;
    /**
     * @Fields operatorId 操作员
     */
    private String operatorId;
    /**
     * @Fields operatorName 操作员名称
     */
    private String operatorName;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields updateTime 修改时间
     */
    private LocalDateTime updateTime;

    public Integer getInstanceGroupId() {
        return instanceGroupId;
    }

    public void setInstanceGroupId(Integer instanceGroupId) {
        this.instanceGroupId = instanceGroupId;
    }

    public String getWorkspaceId() {
        return workspaceId;
    }

    public void setWorkspaceId(String workspaceId) {
        this.workspaceId = workspaceId;
    }

    public String getInstanceGroupCn() {
        return instanceGroupCn;
    }

    public void setInstanceGroupCn(String instanceGroupCn) {
        this.instanceGroupCn = instanceGroupCn;
    }

    public String getInstanceGroupName() {
        return instanceGroupName;
    }

    public void setInstanceGroupName(String instanceGroupName) {
        this.instanceGroupName = instanceGroupName;
    }

    public String getInstanceGroupDesc() {
        return instanceGroupDesc;
    }

    public void setInstanceGroupDesc(String instanceGroupDesc) {
        this.instanceGroupDesc = instanceGroupDesc;
    }

    public Byte getAdvancedSwitch() {
        return advancedSwitch;
    }

    public void setAdvancedSwitch(Byte advancedSwitch) {
        this.advancedSwitch = advancedSwitch;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
}