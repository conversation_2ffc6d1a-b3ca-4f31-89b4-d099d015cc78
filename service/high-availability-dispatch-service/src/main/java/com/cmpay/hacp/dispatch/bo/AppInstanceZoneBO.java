package com.cmpay.hacp.dispatch.bo;

import com.cmpay.hacp.capable.TenantCapable;
import com.cmpay.hacp.dispatch.entity.AppInstanceZoneDO;
import com.cmpay.hafr.agent.domain.config.InstanceHealthCheck;
import com.cmpay.hafr.agent.domain.config.InstanceServer;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Data
@EqualsAndHashCode(callSuper=true)
public class AppInstanceZoneBO extends AppInstanceZoneDO implements TenantCapable {

    private List<Integer> deleteMachines;
    private List<AppInstanceMachineBO> servers;
    private String zoneLabel;

    public List<InstanceServer> convertToAgentInstanceServer(){
        if(JudgeUtils.isNotEmpty(this.servers)) {
            List<InstanceServer> list = new ArrayList<>();
            for (AppInstanceMachineBO bo : this.servers) {
                InstanceServer dto = new InstanceServer();
                dto.setAddress(bo.getAddress());
                Optional.ofNullable(bo.getWeight()).ifPresent(x -> dto.setWeight(x.intValue()));
                Optional.ofNullable(bo.getIsBackup()).ifPresent(x -> dto.setIsBackup(bo.getIsBackup() == 1));
                list.add(dto);
            }
            return list;
        }
        return null;
    }
    public InstanceHealthCheck convertToInstanceHealthCheck(){
        if(this.getCheckSwitch() == null || this.getCheckSwitch() < 1) {
            return null;
        }
        InstanceHealthCheck instanceHealthCheck = new InstanceHealthCheck();
        Optional.ofNullable(this.getCheckInterval()).ifPresent(x -> instanceHealthCheck.setInterval(x.intValue()));
        Optional.ofNullable(this.getCheckFall()).ifPresent(x -> instanceHealthCheck.setFall(x.intValue()));
        Optional.ofNullable(this.getCheckRise()).ifPresent(x -> instanceHealthCheck.setRise(x.intValue()));
        Optional.ofNullable(this.getCheckTimeout()).ifPresent(x -> instanceHealthCheck.setTimeout(x.intValue()));
        Optional.ofNullable(this.getCheckPort()).ifPresent(x -> instanceHealthCheck.setPort(x.intValue()));
        Optional.ofNullable(this.getCheckHttpSend()).ifPresent(x -> instanceHealthCheck.setHttpSend(x));
        Optional.ofNullable(this.getCheckHttpExpectAlive())
                .map(x -> Arrays.stream(x.split(","))
                .map(i -> Integer.parseInt(i)).collect(Collectors.toList()))
                .ifPresent(httpExpectAlive -> instanceHealthCheck.setHttpExpectAlive(httpExpectAlive));
        return instanceHealthCheck;
    }
}

