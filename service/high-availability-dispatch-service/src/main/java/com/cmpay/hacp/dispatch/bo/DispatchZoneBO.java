/*
 * @ClassName DispatchZoneDO
 * @Description 
 * @version 1.0
 * @Date 2024-05-15 10:07:29
 */
package com.cmpay.hacp.dispatch.bo;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.hacp.capable.TenantCapable;
import com.cmpay.hacp.enums.DispatchMsgEnum;
import com.cmpay.hafr.agent.util.ConfigCheckUtils;
import com.cmpay.lemon.common.exception.BusinessException;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DispatchZoneBO extends BaseDO implements TenantCapable {
    /**
     * @Fields id 机房id
     */
    private Long id;
    /**
     * @Fields workspaceId 项目id
     */
    private String workspaceId;
    /**
     * @Fields zoneName 机房名称
     */
    private String zoneName;
    /**
     * @Fields zoneLabel 机房标识
     */
    private String zoneLabel;
    /**
     * @Fields operatorId 操作员
     */
    private String operatorId;
    /**
     * @Fields operatorName 操作员名称
     */
    private String operatorName;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields updateTime 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * @Fields remarks 备注信息
     */
    private String remarks;
    /**
     * @Fields status 删除标记
     */
    private String status;

    public void check(){
        if(!ConfigCheckUtils.isValidParamName(this.getZoneLabel())){
            BusinessException.throwBusinessException(DispatchMsgEnum.ZONE_LABEL_ILLEGAL);
        }
    }
}