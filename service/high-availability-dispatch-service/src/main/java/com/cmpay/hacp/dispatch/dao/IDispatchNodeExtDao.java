/*
 * @ClassName IDispatchNodeDao
 * @Description 
 * @version 1.0
 * @Date 2024-06-19 09:29:43
 */
package com.cmpay.hacp.dispatch.dao;

import com.cmpay.hacp.dispatch.bo.DashboardDispatchBO;
import com.cmpay.hacp.dispatch.entity.DispatchNodeDO;
import com.cmpay.hacp.dispatch.entity.DispatchNodeQueryDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface IDispatchNodeExtDao extends IDispatchNodeDao {

    List<DispatchNodeQueryDO> likeFind(DispatchNodeQueryDO dispatchNode);
    DispatchNodeQueryDO randGetNode(DispatchNodeQueryDO dispatchNode);

    int countWorkspaceNodePushStatus(DispatchNodeQueryDO dispatchNode);

    List<DashboardDispatchBO> getZoneDispatchList(String workspaceId);

    List<String> getRunningVersionList(DispatchNodeDO entity);
}