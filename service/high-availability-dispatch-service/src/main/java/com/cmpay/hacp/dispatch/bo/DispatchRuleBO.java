/*
 * @ClassName DispatchDO
 * @Description 
 * @version 1.0
 * @Date 2024-05-28 09:42:23
 */
package com.cmpay.hacp.dispatch.bo;

import com.cmpay.hacp.capable.TenantCapable;
import com.cmpay.hacp.dispatch.entity.DispatchRuleDO;
import com.cmpay.hafr.agent.domain.config.FlowExpression;
import com.cmpay.hafr.agent.util.ConfigCheckUtils;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class DispatchRuleBO extends DispatchRuleDO implements TenantCapable {

    private List<RuleExpressionBO> ruleExpressions;

    public void check(){
        if (StringUtils.isNotBlank(this.getDispatchRuleName())
                && !ConfigCheckUtils.isValidParamName(this.getDispatchRuleName())) {
            BusinessException.throwBusinessException("DPZN1013");
        }
        List<RuleExpressionBO> expressions = this.getRuleExpressions();
        if (expressions == null || expressions.isEmpty()) {
            BusinessException.throwBusinessException("HFA00066");
        }

        expressions.forEach((expression) -> {
            if (expression.getDispatchRuleType() == null) {
                BusinessException.throwBusinessException("HFA00065");
            }

            if (!StringUtils.equalsAny(expression.getDispatchRuleType(), new CharSequence[]{"URL_PATH", "BODY_TEXT"})) {
                if (StringUtils.isBlank(expression.getParamExtractor())) {
                    BusinessException.throwBusinessException("HFA00067");
                }
            } else if (StringUtils.isBlank(expression.getParamExtractor())) {
                expression.setParamExtractor("");
            }

            if (expression.getCalcOperator() == null) {
                BusinessException.throwBusinessException("HFA00068");
            }

            if (expression.getZoneWeightList() == null || expression.getZoneWeightList().isEmpty()) {
                BusinessException.throwBusinessException("HFA00070");
            }

            expression.getZoneWeightList().forEach(typeValue -> {
                if (StringUtils.isBlank(typeValue.getType())) {
                    BusinessException.throwBusinessException("HFA00071");
                }

                if (typeValue.getValue() == null || Integer.parseInt(typeValue.getValue()) < 0) {
                    BusinessException.throwBusinessException("HFA00072");
                }

            });
        });
    }

    public List<FlowExpression> convertToAgentFlowExpression(){

        if(JudgeUtils.isNotEmpty(this.ruleExpressions)) {
            List<FlowExpression> list = new ArrayList<>();
            for (RuleExpressionBO bo : this.ruleExpressions) {
                FlowExpression dto = new FlowExpression();
                dto.setKeyExtractor(bo.getParamExtractor());
                dto.setOperator(bo.getCalcOperator());
                dto.setValue(bo.getResultValue());
                dto.setKeySource(bo.getDispatchRuleType());
                dto.setZoneWeights(bo.convertZoneWeightsToMap());
                list.add(dto);
            }
            return list;
        }
        return null;
    }

}