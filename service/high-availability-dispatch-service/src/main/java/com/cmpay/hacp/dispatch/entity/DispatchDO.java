/*
 * @ClassName DispatchDO
 * @Description 
 * @version 1.0
 * @Date 2024-08-16 10:59:34
 */
package com.cmpay.hacp.dispatch.entity;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;
import java.time.LocalDateTime;

@DataObject
public class DispatchDO extends BaseDO {
    /**
     * @Fields dispatchId 调度ID
     */
    private Integer dispatchId;
    /**
     * @Fields workspaceId 项目空间ID
     */
    private String workspaceId;
    /**
     * @Fields dispatchCn 调度中文
     */
    private String dispatchCn;
    /**
     * @Fields dispatchName 调度名称
     */
    private String dispatchName;
    /**
     * @Fields dispatchDesc 调度描叙
     */
    private String dispatchDesc;
    /**
     * @Fields apiLocationId 接口ID
     */
    private String apiLocationId;
    /**
     * @Fields apiTag 接口标签
     */
    private String apiTag;
    /**
     * @Fields apiRuleIds 流量规则列表
     */
    private String apiRuleIds;
    /**
     * @Fields status 0:禁用  1:启用
     */
    private Byte status;
    /**
     * @Fields isEmergency 0:常规调度  1:应急调度
     */
    private Byte isEmergency;
    /**
     * @Fields priorityLevel 调度优先级
     */
    private Byte priorityLevel;
    /**
     * @Fields operatorId 操作员
     */
    private String operatorId;
    /**
     * @Fields operatorName 操作员名称
     */
    private String operatorName;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields updateTime 修改时间
     */
    private LocalDateTime updateTime;

    public Integer getDispatchId() {
        return dispatchId;
    }

    public void setDispatchId(Integer dispatchId) {
        this.dispatchId = dispatchId;
    }

    public String getWorkspaceId() {
        return workspaceId;
    }

    public void setWorkspaceId(String workspaceId) {
        this.workspaceId = workspaceId;
    }

    public String getDispatchCn() {
        return dispatchCn;
    }

    public void setDispatchCn(String dispatchCn) {
        this.dispatchCn = dispatchCn;
    }

    public String getDispatchName() {
        return dispatchName;
    }

    public void setDispatchName(String dispatchName) {
        this.dispatchName = dispatchName;
    }

    public String getDispatchDesc() {
        return dispatchDesc;
    }

    public void setDispatchDesc(String dispatchDesc) {
        this.dispatchDesc = dispatchDesc;
    }

    public String getApiLocationId() {
        return apiLocationId;
    }

    public void setApiLocationId(String apiLocationId) {
        this.apiLocationId = apiLocationId;
    }

    public String getApiTag() {
        return apiTag;
    }

    public void setApiTag(String apiTag) {
        this.apiTag = apiTag;
    }

    public String getApiRuleIds() {
        return apiRuleIds;
    }

    public void setApiRuleIds(String apiRuleIds) {
        this.apiRuleIds = apiRuleIds;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public Byte getIsEmergency() {
        return isEmergency;
    }

    public void setIsEmergency(Byte isEmergency) {
        this.isEmergency = isEmergency;
    }

    public Byte getPriorityLevel() {
        return priorityLevel;
    }

    public void setPriorityLevel(Byte priorityLevel) {
        this.priorityLevel = priorityLevel;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
}