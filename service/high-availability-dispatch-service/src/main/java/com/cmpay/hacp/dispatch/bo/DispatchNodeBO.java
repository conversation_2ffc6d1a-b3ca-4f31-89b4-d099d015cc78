/*
 * @ClassName DispatchDO
 * @Description 
 * @version 1.0
 * @Date 2024-05-28 09:42:23
 */
package com.cmpay.hacp.dispatch.bo;

import com.cmpay.hacp.capable.TenantCapable;
import com.cmpay.hacp.dispatch.entity.DispatchNodeQueryDO;
import com.cmpay.hacp.dispatch.util.DispatchConfigCheckUtils;
import com.cmpay.lemon.common.exception.BusinessException;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

@Data
@EqualsAndHashCode(callSuper = true)
public class DispatchNodeBO extends DispatchNodeQueryDO implements TenantCapable {

    public void check(){
        if(DispatchConfigCheckUtils.isInValidProtocolIpDomainPort(this.getIpPort())){
            BusinessException.throwBusinessException("DPZN1012");
        }
    }

    public String getNodeCallUrl(){
        if(!StringUtils.startsWith(this.getIpPort(), "http")) {

            return "http://" + this.getIpPort();
        }
        return this.getIpPort();
    }
}