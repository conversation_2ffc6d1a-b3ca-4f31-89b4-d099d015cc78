package com.cmpay.hacp.dispatch.bo;

import com.cmpay.hacp.capable.TenantCapable;
import com.cmpay.hacp.dispatch.entity.AppServiceDO;
import com.cmpay.hafr.agent.CommonConstants;
import com.cmpay.hafr.agent.util.ConfigCheckUtils;
import com.cmpay.hafr.agent.util.NginxListenParser;
import com.cmpay.lemon.common.exception.BusinessException;
import org.apache.commons.lang3.StringUtils;

public class AppServiceBO extends AppServiceDO implements TenantCapable {

    public void check(){

        if (StringUtils.isNotBlank(this.getAppServiceName())
                && !ConfigCheckUtils.isValidParamName(this.getAppServiceName())) {
            BusinessException.throwBusinessException("HFA00031");
        }

        if (StringUtils.isBlank(this.getListen())) {
            BusinessException.throwBusinessException("HFA00032");
        }

        NginxListenParser.ListenInfo listenInfo = NginxListenParser.parse(this.getListen());
        if (listenInfo == null) {
            BusinessException.throwBusinessException("HFA00035");
        }
        if (CommonConstants.CONFIG_SERVICE_RESERVED_PORTS.contains(listenInfo.getPort())) {
            BusinessException.throwBusinessException("HFA00036");
        }

        if (StringUtils.isBlank(this.getDomainNames())) {
            BusinessException.throwBusinessException("HFA00033");
        }
        for (String domainName : this.getDomainNames().split(",")) {
            if (StringUtils.isBlank(domainName)) {
                BusinessException.throwBusinessException("HFA00034");
            }
        }
    }
}
