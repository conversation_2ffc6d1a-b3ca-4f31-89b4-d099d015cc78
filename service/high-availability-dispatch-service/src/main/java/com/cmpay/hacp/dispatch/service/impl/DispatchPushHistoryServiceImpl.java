package com.cmpay.hacp.dispatch.service.impl;

import com.cmpay.hacp.dispatch.bo.DispatchPushHistoryBO;
import com.cmpay.hacp.dispatch.dao.IDispatchConfigExtDao;
import com.cmpay.hacp.dispatch.dao.IDispatchPushHistoryExtDao;
import com.cmpay.hacp.dispatch.entity.DispatchConfigDO;
import com.cmpay.hacp.dispatch.entity.DispatchPushHistoryDO;
import com.cmpay.hacp.dispatch.service.DispatchPushHistoryService;
import com.cmpay.hacp.enums.DispatchMsgEnum;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.framework.page.PageInfo;
import com.cmpay.lemon.framework.utils.PageUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DispatchPushHistoryServiceImpl implements DispatchPushHistoryService {

    @Autowired
    private IDispatchPushHistoryExtDao dispatchPushHistoryDao;

    @Autowired
    private IDispatchConfigExtDao dispatchConfigDao;
    @Override
    public PageInfo<DispatchPushHistoryBO> getDispatchPushHistoryList(int pageNum, int pageSize, DispatchPushHistoryBO dispatchConfigBO) {
        if(dispatchConfigBO.getPushTimes() == null || dispatchConfigBO.getPushTimes() == 0) {
            DispatchConfigDO dispatchConfigDO = dispatchConfigDao.get(dispatchConfigBO.getDispatchConfigId());
            dispatchConfigBO.setPushTimes(dispatchConfigDO.getPushTimes());
        }
        return PageUtils.pageQueryWithCount(pageNum, pageSize,
                () -> BeanConvertUtil.convertList(dispatchPushHistoryDao.likeFind(dispatchConfigBO), DispatchPushHistoryBO.class));
    }

    @Override
    public DispatchPushHistoryBO addDispatchPushHistory(DispatchPushHistoryBO dispatchConfigBO) {
        dispatchPushHistoryDao.insert(dispatchConfigBO);
        dispatchConfigBO.setPushStartTime(null);
        dispatchConfigBO.setPushEndTime(null);
        List<DispatchPushHistoryDO> dispatchPushHistoryDOS = dispatchPushHistoryDao.find(dispatchConfigBO);
        return BeanConvertUtil.convert(dispatchPushHistoryDOS.get(0), DispatchPushHistoryBO.class);
    }

    @Override
    public void updateDispatchPushHistory(DispatchPushHistoryBO dispatchConfigBO) {
        dispatchPushHistoryDao.update(dispatchConfigBO);
    }

    @Override
    public List<DispatchPushHistoryBO> getConfigPublishHistory(DispatchPushHistoryBO dispatchPushHistoryBO) {
        if(dispatchPushHistoryBO.getDispatchConfigId() == null || dispatchPushHistoryBO.getDispatchConfigId() < 1){
            BusinessException.throwBusinessException(DispatchMsgEnum.PUBLISH_VERSION_NOT_EXIST);
        }
        return BeanConvertUtil.convertList(dispatchPushHistoryDao.getConfigPublishHistory(dispatchPushHistoryBO), DispatchPushHistoryBO.class);
    }
}
