package com.cmpay.hacp.dispatch.service;

import com.cmpay.hacp.dispatch.bo.AppInstanceGroupBO;
import com.cmpay.hacp.dispatch.bo.AppInstanceMachineBO;
import com.cmpay.lemon.framework.page.PageInfo;

import java.util.List;

public interface AppInstanceGroupService {

    void addAppInstanceGroup(AppInstanceGroupBO appInstanceGroupBO);
    void deleteAppInstanceGroup(AppInstanceGroupBO appInstanceGroupBO);
    void updateAppInstanceGroup(AppInstanceGroupBO appInstanceGroupBO);
    List<AppInstanceGroupBO> getAppInstanceGroupList(AppInstanceGroupBO appInstanceGroupBO);
    AppInstanceGroupBO getAppInstanceGroup(AppInstanceGroupBO appInstanceGroupBO);

    List<AppInstanceGroupBO> generateAppInstanceGroups(String workspaceId);

    PageInfo<AppInstanceMachineBO> getInstanceGroupMachineList(int pageNum, int pageSize, AppInstanceMachineBO appInstanceMachineBO);

    List<AppInstanceMachineBO> getInstanceGroupAllList(String workspaceId);
}
