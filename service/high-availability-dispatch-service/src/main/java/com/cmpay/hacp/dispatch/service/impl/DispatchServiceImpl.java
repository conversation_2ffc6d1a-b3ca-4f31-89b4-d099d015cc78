package com.cmpay.hacp.dispatch.service.impl;

import com.cmpay.hacp.dispatch.bo.DispatchBO;
import com.cmpay.hacp.dispatch.dao.IDispatchExtDao;
import com.cmpay.hacp.dispatch.entity.DispatchDO;
import com.cmpay.hacp.dispatch.service.DispatchService;
import com.cmpay.hacp.enums.ByteStatusEnum;
import com.cmpay.hacp.enums.DispatchMsgEnum;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.page.PageInfo;
import com.cmpay.lemon.framework.utils.PageUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class DispatchServiceImpl implements DispatchService {

    @Autowired
    private IDispatchExtDao dispatchDao;

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void addDispatch(DispatchBO dispatchBO) {
        dispatchBO.check();
        dispatchBO.convertToString();
        DispatchBO findName = new DispatchBO();
        findName.setWorkspaceId(dispatchBO.getWorkspaceId());
        findName.setDispatchName(dispatchBO.getDispatchName());
        if(StringUtils.isNotBlank(dispatchBO.getDispatchName())
                && JudgeUtils.isNotEmpty(dispatchDao.find(findName))){
            BusinessException.throwBusinessException(DispatchMsgEnum.DISPATCH_NAME_EXISTS);
        }
        dispatchDao.insert(dispatchBO);
        if (StringUtils.isBlank(dispatchBO.getDispatchName())){
            dispatchBO.setDispatchName("D"+dispatchBO.getDispatchId());
            dispatchDao.update(dispatchBO);
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void deleteDispatch(DispatchBO dispatchBO) {
        dispatchDao.delete(dispatchBO.getDispatchId());
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void updateDispatch(DispatchBO dispatchBO) {
        if(StringUtils.isBlank(dispatchBO.getDispatchDesc())) {
            BusinessException.throwBusinessException(DispatchMsgEnum.DISPATCH_DESC_NOT_BLANK);
        }
        dispatchBO.convertToString();
        dispatchDao.update(dispatchBO);
    }

    public boolean existBind(DispatchBO dispatchBO){
        PageInfo<DispatchBO> dispatchBOPage = findDispatchList(1, 2, dispatchBO);
        return dispatchBOPage.hasContent();
    }

    @Override
    public PageInfo<DispatchBO> findDispatchList(int pageNum, int pageSize, DispatchBO dispatchBO) {
        return PageUtils.pageQueryWithCount(pageNum, pageSize,
                () -> BeanConvertUtil.convertList(dispatchDao.find(dispatchBO), DispatchBO.class));
    }

    @Override
    public PageInfo<DispatchBO> getDispatchList(int pageNum, int pageSize, DispatchBO dispatchBO) {
        return PageUtils.pageQueryWithCount(pageNum, pageSize,
                () -> BeanConvertUtil.convertList(dispatchDao.likeFind(dispatchBO), DispatchBO.class));
    }

    @Override
    public List<DispatchBO> getDispatchList(String workspaceId) {
        DispatchBO dispatchBO = new DispatchBO();
        dispatchBO.setWorkspaceId(workspaceId);
        return BeanConvertUtil.convertList(dispatchDao.likeFind(dispatchBO), DispatchBO.class);
    }

    @Override
    public DispatchBO getInfo(Integer dispatchId, String workspaceId) {
        DispatchDO entity = new DispatchDO();
        entity.setDispatchId(dispatchId);
        entity.setWorkspaceId(workspaceId);
        List<DispatchDO> dispatchDOS = dispatchDao.find(entity);
        if(JudgeUtils.isEmpty(dispatchDOS)){
            BusinessException.throwBusinessException(MsgEnum.DB_SELECT_FAILED);
        }
        DispatchBO result =BeanConvertUtil.convert( dispatchDOS.get(0), DispatchBO.class);
        result.convertToList();
        return result;
    }

    @Override
    public void disable(DispatchBO dispatchBO) {
        dispatchBO.setStatus(ByteStatusEnum.DISABLE.getValue());
        dispatchDao.update(dispatchBO);
    }

    @Override
    public void enable(DispatchBO dispatchBO) {
        dispatchBO.setStatus(ByteStatusEnum.ENABLE.getValue());
        dispatchDao.update(dispatchBO);
    }
}
