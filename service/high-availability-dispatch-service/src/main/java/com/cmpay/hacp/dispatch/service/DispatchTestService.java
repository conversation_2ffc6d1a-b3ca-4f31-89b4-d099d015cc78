package com.cmpay.hacp.dispatch.service;

import com.cmpay.hacp.dispatch.bo.DispatchTestBO;
import com.cmpay.hafr.agent.dto.TraceResult;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.cmpay.lemon.framework.page.PageInfo;

public interface DispatchTestService {

    void addDispatchTest(DispatchTestBO dispatchNodeBO);
    void updateDispatchTest(DispatchTestBO dispatchNodeBO);
    void deleteDispatchTest(DispatchTestBO dispatchNodeBO);
    DispatchTestBO getDispatchTest(DispatchTestBO dispatchNodeBO);
    PageInfo<DispatchTestBO> getDispatchTestList(int pageNum, int pageSize, DispatchTestBO dispatchNodeBO);
    DefaultRspDTO<TraceResult> call(DispatchTestBO dispatchNodeBO);

}