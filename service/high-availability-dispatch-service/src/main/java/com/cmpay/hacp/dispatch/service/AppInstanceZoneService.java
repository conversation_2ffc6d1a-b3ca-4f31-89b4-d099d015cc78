package com.cmpay.hacp.dispatch.service;

import com.cmpay.hacp.dispatch.bo.AppInstanceZoneBO;

import java.util.List;

public interface AppInstanceZoneService {

    void addAppInstanceZone(AppInstanceZoneBO appInstanceZoneBO);

    void deleteAppInstanceZone(AppInstanceZoneBO appInstanceZoneBO);

    void updateAppInstanceZone(AppInstanceZoneBO appInstanceZoneBO);

    List<AppInstanceZoneBO> getAppInstanceZoneList(AppInstanceZoneBO appInstanceZoneBO);

}
