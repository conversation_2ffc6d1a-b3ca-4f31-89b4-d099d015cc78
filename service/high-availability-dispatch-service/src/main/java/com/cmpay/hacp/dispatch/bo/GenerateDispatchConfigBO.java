package com.cmpay.hacp.dispatch.bo;

import com.cmpay.hacp.capable.TenantCapable;
import com.cmpay.hacp.enums.DispatchMsgEnum;
import com.cmpay.lemon.common.exception.BusinessException;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class GenerateDispatchConfigBO implements TenantCapable {

    private String workspaceId;

    private String configDesc;
    /**
     * @Fields operatorId 操作员
     */
    private String operatorId;
    /**
     * @Fields operatorName 操作员名称
     */
    private String operatorName;

    public void checkBlank(){
        if(StringUtils.isBlank(workspaceId)) {

            BusinessException.throwBusinessException(DispatchMsgEnum.WORKSPACE_IS_NULL);
        }
        if(StringUtils.isBlank(configDesc)) {
            BusinessException.throwBusinessException(DispatchMsgEnum.DISPATCH_DESC_NOT_BLANK);
        }
        if(StringUtils.isBlank(operatorId)) {
            BusinessException.throwBusinessException(DispatchMsgEnum.OPERATOR_ID_IS_NULL);

        }
        if(StringUtils.isBlank(operatorName)) {
            BusinessException.throwBusinessException(DispatchMsgEnum.OPERATOR_NAME_IS_NULL);
        }

    }
}
