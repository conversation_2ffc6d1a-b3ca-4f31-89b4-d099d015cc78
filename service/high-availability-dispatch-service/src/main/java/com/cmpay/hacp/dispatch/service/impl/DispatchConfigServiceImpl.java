package com.cmpay.hacp.dispatch.service.impl;

import com.cmpay.hacp.dispatch.bo.GenerateDispatchConfigBO;
import com.cmpay.hacp.dispatch.bo.*;
import com.cmpay.hacp.dispatch.dao.*;
import com.cmpay.hacp.dispatch.entity.*;
import com.cmpay.hacp.dispatch.service.*;
import com.cmpay.hacp.enums.ByteStatusEnum;
import com.cmpay.hacp.enums.DispatchMsgEnum;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.hafr.agent.domain.report.Dictionary;
import com.cmpay.hafr.agent.dto.ConfigUpdateRequest;
import com.cmpay.hafr.agent.dto.DictionaryReport;
import com.cmpay.hafr.agent.util.ConfigCheckUtils;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.page.PageInfo;
import com.cmpay.lemon.framework.utils.PageUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DispatchConfigServiceImpl implements DispatchConfigService {

    @Autowired
    private DispatchPushService dispatchPushService;
    @Autowired
    private DispatchNodeService dispatchNodeService;
    @Autowired
    private DispatchRuleService dispatchRuleService;
    @Autowired
    private IDispatchConfigExtDao dispatchConfigDao;
    @Autowired
    private IDispatchExtDao dispatchDao;
    @Autowired
    private IApiLocationApiTagExtDao apiLocationApiTagExtDao;

    @Autowired
    private AppInstanceGroupService appInstanceGroupService;
    @Autowired
    private IAppServiceExtDao appServiceDao;
    @Autowired
    private IDispatchRuleExtDao dispatchRuleDao;
    @Autowired
    private IRuleExpressionExtDao ruleExpressionDao;
    @Autowired
    private IApiLocationExtDao apiLocationDao;
    @Autowired
    private IDispatchNodeExtDao dispatchNodeDao;
    @Autowired
    private IDispatchPushHistoryExtDao dispatchPushHistoryDao;
    @Autowired
    private DispatchZoneService dispatchZoneService;

    @Override
    public void disableDispatchConfig(DispatchConfigBO dispatchConfigBO) {
        DispatchConfigBO disableDispatchConfig = new DispatchConfigBO();
        disableDispatchConfig.setDispatchConfigId(disableDispatchConfig.getDispatchConfigId());
        DispatchConfigDO dispatchConfigDO = dispatchConfigDao.get(disableDispatchConfig.getDispatchConfigId());
        if(dispatchConfigDO == null){
            BusinessException.throwBusinessException(DispatchMsgEnum.PUBLISH_VERSION_NOT_EXIST);
        }
        if(dispatchConfigDO != null && dispatchConfigDO.getIsCurrentVersion() > 0) {
            BusinessException.throwBusinessException(DispatchMsgEnum.PUBLISH_VERSION_CANNOT_DELETE);
        }
        dispatchConfigDO.setStatus(ByteStatusEnum.DISABLE.getValue());
        dispatchConfigDao.update(dispatchConfigDO);
    }

    @Override
    public void deleteDispatchConfig(DispatchConfigBO dispatchConfigBO) {
        DispatchConfigDO findDispatchConfigDO = dispatchConfigDao.get(dispatchConfigBO.getDispatchConfigId());
        if(findDispatchConfigDO == null){
            BusinessException.throwBusinessException(DispatchMsgEnum.PUBLISH_VERSION_NOT_EXIST);
        }
        if(findDispatchConfigDO.getIsCurrentVersion() > 0) {
            BusinessException.throwBusinessException(DispatchMsgEnum.PUBLISH_VERSION_CANNOT_DELETE);
        }
        if(findDispatchConfigDO.getPushTimes() > 0) {
            BusinessException.throwBusinessException(DispatchMsgEnum.PUBLISH_VERSION_CANNOT_DELETE);
        }
        dispatchConfigDao.delete(findDispatchConfigDO.getDispatchConfigId());
    }

    @Override
    public DispatchConfigBO getPushDataDropDown(String workspaceId) {
        List<String> versions = dispatchNodeService.getRunningVersionList(workspaceId);
        List<DispatchConfigBO> runningDispatchConfig =dispatchConfigDao.getRunningDispatchConfig(workspaceId,versions);

        DispatchConfigBO dispatchConfigBO = new DispatchConfigBO();

        dispatchConfigBO.setSortedDispatches(new ArrayList<>());
        dispatchConfigBO.setApiLocationList(new ArrayList<>());
        HashMap<Integer,DispatchBO> dispatchMap = new HashMap<>();
        HashMap<Integer,ApiLocationBO> apiLocationMap = new HashMap<>();
        runningDispatchConfig.stream()
                .peek(DispatchConfigBO::convertJsonToList)
                .forEach(m->{
                    dispatchMap.putAll(m.getSortedDispatches()
                            .stream()
                            .collect(Collectors.toMap(DispatchBO::getDispatchId, Function.identity())));

                    apiLocationMap.putAll(m.getApiLocationList()
                            .stream()
                            .collect(Collectors.toMap(ApiLocationBO::getApiLocationId, Function.identity())));
                });
        dispatchConfigBO.setSortedDispatches(new ArrayList<>(dispatchMap.values()));
        dispatchConfigBO.setApiLocationList(new ArrayList<>(apiLocationMap.values()));
        return dispatchConfigBO;
    }


    private List<ApiLocationBO> closeDispatches(List<ApiLocationBO> apiLocationBOS, List<DispatchBO> sortedDispatches, Set<String> closeDispatches) {

        if(closeDispatches == null || closeDispatches.isEmpty()) {
            return apiLocationBOS;
        }
        for (DispatchBO sortedDispatch : sortedDispatches) {
            if(closeDispatches.contains(sortedDispatch.getDispatchName())
                    && sortedDispatch.getStatus() == ByteStatusEnum.ENABLE.getValue()){
                sortedDispatch.setStatus(ByteStatusEnum.DISABLE.getValue());
            }
        }
        boolean bool = bindApilocationWithDispatches(sortedDispatches, apiLocationBOS);

        return apiLocationBOS;
    }

    @Override
    public boolean openDispatches(GenerateDispatchConfigBO preConfig, Set<String> openEmergencyDispatches) {
        return openOrCloseDispatches(preConfig, openEmergencyDispatches, null);
    }

    @Override
    public boolean closeDispatches(GenerateDispatchConfigBO preConfig, Set<String> closeEmergencyDispatches) {
        return openOrCloseDispatches(preConfig, null, closeEmergencyDispatches);
    }
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean openOrCloseDispatches(GenerateDispatchConfigBO preConfig, Set<String> openEmergencyDispatches, Set<String> closeEmergencyDispatches) {
        preConfig.checkBlank();
        if(JudgeUtils.isEmpty(openEmergencyDispatches) && JudgeUtils.isEmpty(closeEmergencyDispatches)) {
            BusinessException.throwBusinessException(DispatchMsgEnum.OPEN_CLOSE_DISPATCH_EMPTY);
        }
        DispatchConfigBO currentDispatchConfig = this.getCurrentDispatchConfig(preConfig.getWorkspaceId());
        currentDispatchConfig.convertJsonToList();
        List<DispatchBO> sortedDispatches = currentDispatchConfig.getSortedDispatches();
        boolean isChanged = false;
        if(sortedDispatches != null) {

            for (DispatchBO sortedDispatch : sortedDispatches) {
                DispatchBO updateDispatch = new DispatchBO();
                updateDispatch.setDispatchId(sortedDispatch.getDispatchId());
                if(openEmergencyDispatches != null
                        && openEmergencyDispatches.contains(String.valueOf(sortedDispatch.getDispatchId()))
                        && sortedDispatch.getStatus() == ByteStatusEnum.DISABLE.getValue()) {
                    isChanged = true;
                    sortedDispatch.setStatus(ByteStatusEnum.ENABLE.getValue());
                    updateDispatch.setStatus(ByteStatusEnum.ENABLE.getValue());
                    dispatchDao.update(updateDispatch);
                }

                if(closeEmergencyDispatches != null
                        && closeEmergencyDispatches.contains(String.valueOf(sortedDispatch.getDispatchId()))
                        && sortedDispatch.getStatus() == ByteStatusEnum.ENABLE.getValue()) {
                    isChanged = true;
                    sortedDispatch.setStatus(ByteStatusEnum.DISABLE.getValue());
                    updateDispatch.setStatus(ByteStatusEnum.DISABLE.getValue());
                    dispatchDao.update(updateDispatch);
                }
            }
        }

        if(!isChanged) {
            //BusinessException.throwBusinessException(DispatchMsgEnum.DISPATCH_IS_STATUS_RIGHT);
        }
        bindApilocationWithDispatches(sortedDispatches, currentDispatchConfig.getApiLocationList());
        currentDispatchConfig.publishGenerateDispatchConfig(preConfig);
        DispatchConfigBO dispatchConfigBO = newAndGetDispatchConfigBO(currentDispatchConfig);
        push(dispatchConfigBO);
        return true;
    }

    @Override
    public PageInfo<DispatchConfigBO> getDispatchConfigList(int pageNum, int pageSize, DispatchConfigBO dispatchConfigBO) {
        if(dispatchConfigBO.getStatus() == null) {
            dispatchConfigBO.setStatus(ByteStatusEnum.ENABLE.getValue());
        }
        return PageUtils.pageQueryWithCount(pageNum, pageSize,
                () -> dispatchConfigDao.likeFind(dispatchConfigBO));
    }

    @Override
    public DispatchConfigBO getDispatchConfig(DispatchConfigBO dispatchConfigBO) {
        if(dispatchConfigBO == null
                || dispatchConfigBO.getDispatchConfigId() == null
                || dispatchConfigBO.getDispatchConfigId() < 1) {
            DispatchConfigBO currentDispatchConfig = getCurrentDispatchConfig(dispatchConfigBO.getWorkspaceId());
            if(currentDispatchConfig != null) {
                dispatchConfigBO.setDispatchConfigId(currentDispatchConfig.getDispatchConfigId());
            }
        }
        return BeanConvertUtil.convert(dispatchConfigDao.get(dispatchConfigBO.getDispatchConfigId()), DispatchConfigBO.class);
    }

    @Override
    public DispatchConfigBO getDispatchGraph(DispatchConfigBO dispatchConfigBO) {
        if(dispatchConfigBO.getDispatchConfigId() == null
                && StringUtils.isNotBlank(dispatchConfigBO.getDispatchVersion())) {
            DispatchConfigBO findBO = new DispatchConfigBO();
            findBO.setDispatchVersion(dispatchConfigBO.getDispatchVersion());
            PageInfo<DispatchConfigBO> pageInfo = this.getDispatchConfigList(0, 1, findBO);
            if (pageInfo != null && pageInfo.hasContent()) {
                dispatchConfigBO.setDispatchConfigId(pageInfo.getList().get(0).getDispatchConfigId());
            }
        }
        DispatchConfigBO dispatchConfig = getDispatchConfig(dispatchConfigBO);
        if(dispatchConfig == null) {
            return null;
        }
        dispatchConfig.convertJsonToList();

        Map<String, DispatchRuleBO> rulesMap = new HashMap<>();
        List<DispatchRuleBO> dispatchRuleList = dispatchConfig.getDispatchRuleList();
        DictionaryReport ruleDictionary = dispatchRuleService.getRuleDictionary(dispatchConfigBO.getWorkspaceId());
        Map<String, String> flowKeySourceMap = ruleDictionary.getOrDefault("flowKeySource", new ArrayList<>()).stream().collect(Collectors.toMap(Dictionary::getKey, Dictionary::getDesc));
        Map<String, String> expressionOperatorMap = ruleDictionary.getOrDefault("expressionOperator", new ArrayList<>()).stream().collect(Collectors.toMap(Dictionary::getKey, Dictionary::getDesc));
        if(dispatchRuleList != null) {
            for (DispatchRuleBO dispatchRuleBO : dispatchRuleList) {
                rulesMap.put(dispatchRuleBO.getDispatchRuleId().intValue() + "", dispatchRuleBO);
                List<RuleExpressionBO> ruleExpressions = dispatchRuleBO.getRuleExpressions();
                if(ruleExpressions != null) {
                    ruleExpressions.stream().forEach(f->{
                        f.setDispatchRuleTypeName(flowKeySourceMap.getOrDefault(f.getDispatchRuleType(),null));
                        f.setCalcOperatorName(expressionOperatorMap.getOrDefault(f.getCalcOperator(),null));
                    });
                }
            }
        }




        List<DispatchBO> sortedDispatches = dispatchConfig.getSortedDispatches();
        for (DispatchBO sortedDispatch : sortedDispatches) {
            String apiRuleIds = sortedDispatch.getApiRuleIds();
            String[] ruleIds = apiRuleIds.split(",");
            List<DispatchRuleBO> ruleList = new ArrayList<>(ruleIds.length);
            for (String ruleId : ruleIds) {
                DispatchRuleBO dispatchRuleBO = rulesMap.get(ruleId);
                if(dispatchRuleBO != null){
                    ruleList.add(dispatchRuleBO);
                }
            }
            sortedDispatch.setRuleList(ruleList);

        }
        return dispatchConfig;
    }

    private DispatchConfigBO getCurrentDispatchConfig(String workspaceId) {
        DispatchConfigBO currentDispatchConfig = new DispatchConfigBO();
        currentDispatchConfig.setWorkspaceId(workspaceId);
        currentDispatchConfig.setIsCurrentVersion((byte)1);
        List<DispatchConfigDO> dispatchConfigDOS = dispatchConfigDao.find(currentDispatchConfig);
        if(JudgeUtils.isEmpty(dispatchConfigDOS)) {
            return null;
        }
        if(dispatchConfigDOS.size() > 0) {
            return BeanConvertUtil.convert(dispatchConfigDao.get(dispatchConfigDOS.get(0).getDispatchConfigId()), DispatchConfigBO.class);
        }
        return null;
    }

    @Override
    public DispatchConfigBO preGenerateDispatchConfig(GenerateDispatchConfigBO preConfig) {

        String workspaceId = preConfig.getWorkspaceId();
        if(StringUtils.isBlank(workspaceId)) {
            BusinessException.throwBusinessException(DispatchMsgEnum.WORKSPACE_IS_NULL);
        }
        AppServiceDO appServiceDO = new AppServiceDO();
        appServiceDO.setWorkspaceId(workspaceId);
        appServiceDO.setStatus(ByteStatusEnum.ENABLE.getValue());
        List<AppServiceBO> appServices = BeanConvertUtil.convertList(appServiceDao.find(appServiceDO), AppServiceBO.class);

        List<AppInstanceGroupBO> appInstanceGroups = appInstanceGroupService.generateAppInstanceGroups(workspaceId);

        List<DispatchRuleBO> dispatchRuleList = generateAppRules(workspaceId);

        Map<String, Set<String>> apiTagMaps = generateApiTagMaps(workspaceId);

        List<DispatchBO> sortedDispatches = generateSortedDispatchesWithTag(workspaceId, apiTagMaps);

        List<ApiLocationBO> apiLocationList = generateApilocationWithDispatches(workspaceId, sortedDispatches);

        DispatchConfigBO generateDispatchConfig = new DispatchConfigBO();
        Map<Long,String> zoneMap = dispatchZoneService.getSimpleMap(workspaceId);
        generateDispatchConfig.setZoneMap(zoneMap);
        generateDispatchConfig.setAppServiceList(appServices);
        generateDispatchConfig.setAppInstanceGroupList(appInstanceGroups);
        generateDispatchConfig.setDispatchRuleList(dispatchRuleList);
        generateDispatchConfig.setApiTagMaps(apiTagMaps);
        generateDispatchConfig.setSortedDispatches(sortedDispatches);
        generateDispatchConfig.setApiLocationList(apiLocationList);

        generateDispatchConfig.preGenerateDispatchConfig(preConfig);
        return newAndGetDispatchConfigBO(generateDispatchConfig);
    }

    private DispatchConfigBO newAndGetDispatchConfigBO(DispatchConfigBO generateDispatchConfig) {
        ConfigUpdateRequest configUpdateRequest = generateDispatchConfig.buildConfigUpdateRequest();
        ConfigCheckUtils.checkAgentConfig(configUpdateRequest);
        configUpdateRequest.setVersion(null);
        String configMd5Hex = DigestUtils.md5Hex(configUpdateRequest.toString());
        generateDispatchConfig.setConfigHex(configMd5Hex);
        DispatchConfigBO findBO = new DispatchConfigBO();
        findBO.setWorkspaceId(generateDispatchConfig.getWorkspaceId());
        findBO.setConfigHex(configMd5Hex);
        List<DispatchConfigDO> hexList = dispatchConfigDao.find(findBO);
        if(JudgeUtils.isNotEmpty(hexList)) {
            generateDispatchConfig.setDispatchConfigId(hexList.get(0).getDispatchConfigId());
            generateDispatchConfig.setDispatchVersion(hexList.get(0).getDispatchVersion());
            return generateDispatchConfig;
        }

        dispatchConfigDao.insert(generateDispatchConfig);
        findBO.setDispatchVersion(generateDispatchConfig.getDispatchVersion());
        List<DispatchConfigDO> newList = dispatchConfigDao.find(findBO);
        DispatchConfigDO dispatchConfigDO = newList.get(0);
        generateDispatchConfig.setDispatchConfigId(dispatchConfigDO.getDispatchConfigId());
        return generateDispatchConfig;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public DispatchConfigBO publish(DispatchConfigBO dispatchConfigBO) {

        DispatchConfigBO publishDispatchConfig = getDispatchConfig(dispatchConfigBO);
        if(publishDispatchConfig == null
                || publishDispatchConfig.getDispatchConfigId() == null
                || publishDispatchConfig.getDispatchConfigId() < 1) {
            BusinessException.throwBusinessException(DispatchMsgEnum.PUBLISH_VERSION_NOT_EXIST);
        }

        if(StringUtils.isBlank(dispatchConfigBO.getConfigDesc())){
            BusinessException.throwBusinessException(DispatchMsgEnum.PUBLISH_VERSION_DESC_NOT_BLANK);
        }
        publishDispatchConfig.setConfigDesc(dispatchConfigBO.getConfigDesc());
        publishDispatchConfig.setPushEndTime(null);
        publishDispatchConfig.setStatus(ByteStatusEnum.ENABLE.getValue());
        dispatchConfigDao.pushUpdate(publishDispatchConfig);

        return publishDispatchConfig;
    }



    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public DispatchConfigBO publishAndPush(DispatchConfigBO dispatchConfigBO) {
        DispatchConfigBO publish = publish(dispatchConfigBO);
        push(publish);
        return publish;
    }

    private DispatchConfigBO push(DispatchConfigBO publish) {

        if(publish.getIsCurrentVersion() < 1) {
            DispatchConfigBO currentDispatchConfig = getCurrentDispatchConfig(publish.getWorkspaceId());
            if(currentDispatchConfig != null) {
                currentDispatchConfig.setIsCurrentVersion(ByteStatusEnum.DISABLE.getValue());
                dispatchConfigDao.update(currentDispatchConfig);
            }
            publish.setIsCurrentVersion(ByteStatusEnum.ENABLE.getValue());
            publish.setStatus(ByteStatusEnum.ENABLE.getValue());
            Integer pushTimes = publish.getPushTimes().intValue() + 1;
            publish.setPushTimes(pushTimes.byteValue());
            publish.setPushStartTime(LocalDateTime.now());
            publish.setPushEndTime(null);
            dispatchConfigDao.pushUpdate(publish);
        }
        pushConfigToAllNode(publish);
        return publish;
    }

    private void pushConfigToAllNode(DispatchConfigBO dispatchConfigBO) {
        if(StringUtils.isBlank(dispatchConfigBO.getWorkspaceId())) {
            return;
        }
        DispatchNodeBO dispatchNodeBO = new DispatchNodeBO();
        dispatchNodeBO.setWorkspaceId(dispatchConfigBO.getWorkspaceId());
        dispatchNodeBO.setStatus(ByteStatusEnum.ENABLE.getValue());
        int pageNum = 1, pageSize = 500;
        PageInfo<DispatchNodeBO> pageInfo = null;
        do {
            pageInfo = dispatchNodeService.getDispatchNodePage(pageNum, pageSize, dispatchNodeBO);
            if(pageInfo.hasContent()) {
                for (DispatchNodeDO pushDO : pageInfo.getList()) {
                    DispatchPushHistoryDO findPushHistory = new DispatchPushHistoryDO();
                    findPushHistory.setPushTimes(dispatchConfigBO.getPushTimes());
                    findPushHistory.setDispatchConfigId(dispatchConfigBO.getDispatchConfigId());
                    findPushHistory.setDispatchNodeId(pushDO.getDispatchNodeId());
                    List<DispatchPushHistoryDO> dispatchPushHistoryDOS = dispatchPushHistoryDao.find(findPushHistory);
                    if(dispatchPushHistoryDOS != null && !dispatchPushHistoryDOS.isEmpty()) {
                        findPushHistory = dispatchPushHistoryDOS.get(0);
                        findPushHistory.setIpPort(pushDO.getIpPort());
                        findPushHistory.setConfigDesc(dispatchConfigBO.getConfigDesc());
                        findPushHistory.setPushStartTime(LocalDateTime.now());
                        findPushHistory.setPushStatus(ByteStatusEnum.DISABLE.getValue());
                        dispatchPushHistoryDao.update(findPushHistory);
                    } else {
                        DispatchPushHistoryBO dispatchPushHistoryBO = BeanConvertUtil.convert(pushDO, DispatchPushHistoryBO.class);
                        dispatchPushHistoryBO.setDispatchConfigId(dispatchConfigBO.getDispatchConfigId());
                        dispatchPushHistoryBO.setPushTimes(dispatchConfigBO.getPushTimes());
                        dispatchPushHistoryBO.setConfigDesc(dispatchConfigBO.getConfigDesc());
                        dispatchPushHistoryBO.setPushStartTime(LocalDateTime.now());
                        dispatchPushHistoryBO.setPushStatus(ByteStatusEnum.DISABLE.getValue());
                        dispatchPushHistoryDao.insert(dispatchPushHistoryBO);
                    }
                    pushDO.setDispatchConfigId(dispatchConfigBO.getDispatchConfigId());
                    pushDO.setDispatchVersion(dispatchConfigBO.getDispatchVersion());
                    pushDO.setPushTimes(dispatchConfigBO.getPushTimes());
                    dispatchNodeDao.update(pushDO);

                }
            }
            dispatchPushService.pushConfigToNodeList(dispatchConfigBO);
            pageNum++;
        } while(!pageInfo.isIsLastPage());
    }

    @Override
    public List<ApiLocationBO> getApilocationFromDispatchConfig(DispatchConfigBO dispatchConfigBO) {
        return null;
    }

    @Override
    public PageInfo<DispatchBO> getDispatchFromDispatchConfigByApilocation(DispatchConfigBO dispatchConfigBO, ApiLocationBO apiLocationBO) {
        return null;
    }

    @Override
    public PageInfo<DispatchNodeBO> getPushHistoryByDispatchConfig(DispatchConfigBO dispatchConfigBO) {
        return null;
    }

    private List<ApiLocationBO> generateApilocationWithDispatches(String workspaceId, List<DispatchBO> sortedDispatches) {
        ApiLocationBO apiLocationBO = new ApiLocationBO();
        apiLocationBO.setWorkspaceId(workspaceId);
        apiLocationBO.setStatus(ByteStatusEnum.ENABLE.getValue());
        List<ApiLocationBO> apiLocationBOS = apiLocationDao.likeFind(apiLocationBO);

        bindApilocationWithDispatches(sortedDispatches, apiLocationBOS);

        return apiLocationBOS;
    }


    private static boolean bindApilocationWithDispatches(List<DispatchBO> sortedDispatches, List<ApiLocationBO> apiLocationBOS) {
        boolean isChanged = false;
        for (ApiLocationBO apiLocationBO : apiLocationBOS) {
            List<String> dispatches = new ArrayList<>();
            for (DispatchBO sortedDispatch : sortedDispatches) {
                if(ByteStatusEnum.ENABLE.getValue().equals(sortedDispatch.getStatus())
                        && sortedDispatch.getApiLocationIds().contains(String.valueOf(apiLocationBO.getApiLocationId()))){
                    dispatches.add(sortedDispatch.getDispatchName());
                }
            }

            if(Optional.ofNullable(apiLocationBO.getDispatches()).map(List::size).orElse(0) != dispatches.size()) {
                isChanged = true;
            }
            if(!dispatches.isEmpty()) {
                apiLocationBO.setDispatches(dispatches);
            } else {
                apiLocationBO.setDispatches(null);
            }
        }
        return isChanged;
    }


    private List<DispatchBO> generateSortedDispatchesWithTag(String workspaceId, Map<String, Set<String>> apiTagMaps) {
        DispatchDO dispatchDO = new DispatchBO();
        dispatchDO.setWorkspaceId(workspaceId);
        dispatchDO.setStatus(ByteStatusEnum.ENABLE.getValue());
        List<DispatchBO> sortedDispatches = BeanConvertUtil.convertList(dispatchDao.findSortedDispatches(dispatchDO), DispatchBO.class);
        List<DispatchRuleBO> ruleList = dispatchRuleService.getDispatchRuleList(workspaceId);
        Map<Integer, String> ruleMap = Optional.ofNullable(ruleList)
                .orElse(new ArrayList<>())
                .stream()
                .collect(Collectors.toMap(DispatchRuleBO::getDispatchRuleId, DispatchRuleBO::getDispatchRuleName));
        for (DispatchBO sortedDispatch : sortedDispatches) {
            sortedDispatch.convertToRulesList(ruleMap);
            sortedDispatch.convertApiLocationId(apiTagMaps);
        }
        return sortedDispatches;
    }

    private Map<String, Set<String>> generateApiTagMaps(String workspaceId) {
        ApiLocationApiTagDO apiLocationApiTagDO = new ApiLocationApiTagDO();
        apiLocationApiTagDO.setWorkspaceId(workspaceId);
        apiLocationApiTagDO.setStatus(ByteStatusEnum.ENABLE.getValue());
        List<ApiLocationApiTagBO> apiLocationApiTagBOS = BeanConvertUtil.convertList(apiLocationApiTagExtDao.find(apiLocationApiTagDO), ApiLocationApiTagBO.class);
        Map<String, Set<String>> apiTagMaps = new HashMap<>();
        for (ApiLocationApiTagBO apiLocationApiTagBO : apiLocationApiTagBOS) {
            Set<String> apiIds = apiTagMaps.getOrDefault(apiLocationApiTagBO.getApiTagId(), new HashSet<>());
            apiIds.add(apiLocationApiTagBO.getApiLocationId().toString());
            apiTagMaps.putIfAbsent(apiLocationApiTagBO.getApiTagId().toString(), apiIds);
        }
        return apiTagMaps;
    }

    private List<DispatchRuleBO> generateAppRules(String workspaceId) {
        DispatchRuleDO dispatchRuleDO = new DispatchRuleDO();
        dispatchRuleDO.setWorkspaceId(workspaceId);
        dispatchRuleDO.setStatus(ByteStatusEnum.ENABLE.getValue());
        List<DispatchRuleDO> dispatchRuleDOS = dispatchRuleDao.find(dispatchRuleDO);
        Map<Integer, DispatchRuleBO> ruleBOMap = new HashMap<>();
        for (DispatchRuleDO ruleDO : dispatchRuleDOS) {
            ruleBOMap.put(ruleDO.getDispatchRuleId(), BeanConvertUtil.convert(ruleDO, DispatchRuleBO.class));
        }

        RuleExpressionDO ruleExpressionDO = new RuleExpressionDO();
        ruleExpressionDO.setWorkspaceId(workspaceId);
        ruleExpressionDO.setStatus(ByteStatusEnum.ENABLE.getValue());
        List<RuleExpressionDO> ruleExpressionDOS = ruleExpressionDao.likeFind(ruleExpressionDO);
        for (RuleExpressionDO expressionDO : ruleExpressionDOS) {
            DispatchRuleBO dispatchRuleBO = ruleBOMap.get(expressionDO.getDispatchRuleId());
            if(dispatchRuleBO != null) {
                List<RuleExpressionBO> ruleExpressions = dispatchRuleBO.getRuleExpressions();
                if(ruleExpressions == null) {
                    ruleExpressions = new ArrayList<>();
                    dispatchRuleBO.setRuleExpressions(ruleExpressions);
                }
                RuleExpressionBO ruleExpressionBO = BeanConvertUtil.convert(expressionDO, RuleExpressionBO.class);
                ruleExpressionBO.convertZoneWeightsToList();
                ruleExpressions.add(ruleExpressionBO);
            }
        }
        return new ArrayList<>(ruleBOMap.values());
    }


}
