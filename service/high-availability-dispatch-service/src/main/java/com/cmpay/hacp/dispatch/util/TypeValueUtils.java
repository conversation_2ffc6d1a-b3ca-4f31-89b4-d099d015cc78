package com.cmpay.hacp.dispatch.util;

import com.cmpay.hacp.dispatch.bo.TypeValue;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Data
public class TypeValueUtils {
    private static final ObjectMapper objectMapper = new ObjectMapper();

    public static String toString(List<TypeValue> list) {
        if (list != null && !list.isEmpty()) {
            try {
                return objectMapper.writeValueAsString(list);
            } catch (JsonProcessingException e) {

            }
        }
        return null;
    }

    public static List<TypeValue> toList(String string) {
        if(StringUtils.isNotBlank(string)) {
            try {
                return objectMapper.readValue(string, new TypeReference<List<TypeValue>>() {});
            } catch (JsonProcessingException e) {

            }
        }
        return null;
    }
}
