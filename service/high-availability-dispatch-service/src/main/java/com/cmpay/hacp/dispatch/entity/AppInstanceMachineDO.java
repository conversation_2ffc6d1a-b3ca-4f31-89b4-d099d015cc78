/*
 * @ClassName AppInstanceMachineDO
 * @Description 
 * @version 1.0
 * @Date 2024-08-16 10:59:34
 */
package com.cmpay.hacp.dispatch.entity;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;
import java.time.LocalDateTime;

@DataObject
public class AppInstanceMachineDO extends BaseDO {
    /**
     * @Fields instanceMachineId 业务服务机器ID
     */
    private Integer instanceMachineId;
    /**
     * @Fields workspaceId 项目空间ID
     */
    private String workspaceId;
    /**
     * @Fields instanceGroupId 业务服务组ID
     */
    private Integer instanceGroupId;
    /**
     * @Fields zoneId 机房ID
     */
    private Long zoneId;
    /**
     * @Fields address 业务节点IP端口
     */
    private String address;
    /**
     * @Fields weight 权重
     */
    private Short weight;
    /**
     * @Fields isBackup 是否备份
     */
    private Short isBackup;
    /**
     * @Fields status 0:禁用  1:启用
     */
    private Byte status;
    /**
     * @Fields operatorId 操作员
     */
    private String operatorId;
    /**
     * @Fields operatorName 操作员名称
     */
    private String operatorName;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields updateTime 修改时间
     */
    private LocalDateTime updateTime;

    public Integer getInstanceMachineId() {
        return instanceMachineId;
    }

    public void setInstanceMachineId(Integer instanceMachineId) {
        this.instanceMachineId = instanceMachineId;
    }

    public String getWorkspaceId() {
        return workspaceId;
    }

    public void setWorkspaceId(String workspaceId) {
        this.workspaceId = workspaceId;
    }

    public Integer getInstanceGroupId() {
        return instanceGroupId;
    }

    public void setInstanceGroupId(Integer instanceGroupId) {
        this.instanceGroupId = instanceGroupId;
    }

    public Long getZoneId() {
        return zoneId;
    }

    public void setZoneId(Long zoneId) {
        this.zoneId = zoneId;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Short getWeight() {
        return weight;
    }

    public void setWeight(Short weight) {
        this.weight = weight;
    }

    public Short getIsBackup() {
        return isBackup;
    }

    public void setIsBackup(Short isBackup) {
        this.isBackup = isBackup;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
}