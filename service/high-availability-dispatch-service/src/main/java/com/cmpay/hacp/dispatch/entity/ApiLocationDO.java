/*
 * @ClassName ApiLocationDO
 * @Description 
 * @version 1.0
 * @Date 2024-08-16 10:59:34
 */
package com.cmpay.hacp.dispatch.entity;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;
import java.time.LocalDateTime;

@DataObject
public class ApiLocationDO extends BaseDO {
    /**
     * @Fields apiLocationId 接口ID
     */
    private Integer apiLocationId;
    /**
     * @Fields workspaceId 项目空间ID
     */
    private String workspaceId;
    /**
     * @Fields apiLocationCn 
     */
    private String apiLocationCn;
    /**
     * @Fields apiLocationName api location名称
     */
    private String apiLocationName;
    /**
     * @Fields apiDesc api描叙
     */
    private String apiDesc;
    /**
     * @Fields appService 业务服务
     */
    private Integer appService;
    /**
     * @Fields appInstanceGroup 业务节点组
     */
    private Integer appInstanceGroup;
    /**
     * @Fields status 0:禁用  1:启用
     */
    private Byte status;
    /**
     * @Fields operatorId 操作员
     */
    private String operatorId;
    /**
     * @Fields operatorName 操作员名称
     */
    private String operatorName;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields updateTime 修改时间
     */
    private LocalDateTime updateTime;
    /**
     * @Fields uriMatches URI匹配规则
     */
    private String uriMatches;

    public Integer getApiLocationId() {
        return apiLocationId;
    }

    public void setApiLocationId(Integer apiLocationId) {
        this.apiLocationId = apiLocationId;
    }

    public String getWorkspaceId() {
        return workspaceId;
    }

    public void setWorkspaceId(String workspaceId) {
        this.workspaceId = workspaceId;
    }

    public String getApiLocationCn() {
        return apiLocationCn;
    }

    public void setApiLocationCn(String apiLocationCn) {
        this.apiLocationCn = apiLocationCn;
    }

    public String getApiLocationName() {
        return apiLocationName;
    }

    public void setApiLocationName(String apiLocationName) {
        this.apiLocationName = apiLocationName;
    }

    public String getApiDesc() {
        return apiDesc;
    }

    public void setApiDesc(String apiDesc) {
        this.apiDesc = apiDesc;
    }

    public Integer getAppService() {
        return appService;
    }

    public void setAppService(Integer appService) {
        this.appService = appService;
    }

    public Integer getAppInstanceGroup() {
        return appInstanceGroup;
    }

    public void setAppInstanceGroup(Integer appInstanceGroup) {
        this.appInstanceGroup = appInstanceGroup;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getUriMatches() {
        return uriMatches;
    }

    public void setUriMatches(String uriMatches) {
        this.uriMatches = uriMatches;
    }
}