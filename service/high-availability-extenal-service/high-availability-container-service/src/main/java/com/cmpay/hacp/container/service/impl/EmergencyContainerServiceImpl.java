package com.cmpay.hacp.container.service.impl;

import com.alibaba.fastjson2.JSON;
import com.cmpay.hacp.constant.CommonConstant;
import com.cmpay.hacp.container.bo.EmergencyContainerBO;
import com.cmpay.hacp.container.dao.IEmergencyContainerExtDao;
import com.cmpay.hacp.container.entity.EmergencyContainerDO;
import com.cmpay.hacp.container.service.EmergencyContainerService;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.service.SystemCipherService;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.page.PageInfo;
import com.cmpay.lemon.framework.utils.PageUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024/09/02 10:23
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EmergencyContainerServiceImpl implements EmergencyContainerService {

    private final IEmergencyContainerExtDao emergencyContainerDao;

    private final SystemCipherService systemCipherService;

    @Override
    public void add(EmergencyContainerBO bo) {
        if (JudgeUtils.isBlank(bo.getPassword()) ||CommonConstant.ENCRYPTED_DISPLAY.equals(bo.getPassword())) {
            BusinessException.throwBusinessException(MsgEnum.INCOMPLETE_PARAM);
        }
        if(JudgeUtils.isNotNull(bo.getPassword())){
            bo.setPassword(systemCipherService.otherModuleEncryptPassword(bo.getUuid(), bo.getPassword()));
        }
        emergencyContainerDao.insert(bo);
    }

    @Override
    public void update(EmergencyContainerBO bo) {

        EmergencyContainerBO entity = BeanUtils.copyPropertiesReturnDest(new EmergencyContainerBO(),bo);
        entity.setUsername(null);
        entity.setPassword(null);
        EmergencyContainerBO detailInfo = this.getDetailInfo(entity);

        if(JudgeUtils.isNull(detailInfo) && CommonConstant.ENCRYPTED_DISPLAY.equals(bo.getPassword())){
            BusinessException.throwBusinessException(MsgEnum.KUBESPHERE_PASSWORD_IS_ERROR);
        }

        if(JudgeUtils.isNull(detailInfo)){
            log.error("info is null");
            BusinessException.throwBusinessException(MsgEnum.DB_UPDATE_FAILED);
        }
        if (CommonConstant.ENCRYPTED_DISPLAY.equals(bo.getPassword())) {
            bo.setPassword(null);
        }
        if(JudgeUtils.isNotNull(bo.getPassword())){
            bo.setPassword(systemCipherService.otherModuleEncryptPassword(bo.getUuid(), bo.getPassword()));
        }
        bo.setContainerId(detailInfo.getContainerId());
        bo.setUpdateTime(LocalDateTime.now());
        emergencyContainerDao.update(bo);
    }

    @Override
    public void delete(EmergencyContainerBO bo) {
        emergencyContainerDao.deleteExt(bo);
    }

    @Override
    public EmergencyContainerBO getDetailInfo(EmergencyContainerBO bo) {
        EmergencyContainerDO detailInfo = emergencyContainerDao.getDetailInfo(bo);
        if(JudgeUtils.isNull(detailInfo)){
            return null;
        }
        EmergencyContainerBO convert = BeanConvertUtil.convert(detailInfo, EmergencyContainerBO.class);
        convert.setCloudClusterList(JSON.parseArray(detailInfo.getCloudClusters(), String.class));
        return convert;
    }

    @Override
    public EmergencyContainerBO getDecryptDetailInfo(EmergencyContainerBO bo) {
        EmergencyContainerBO detailInfo = this.getDetailInfo(bo);
        if(JudgeUtils.isNull(detailInfo)){
            return null;
        }
        detailInfo.setPassword(systemCipherService.otherModuleDecryptPassword(detailInfo.getPassword()));
        return detailInfo;
    }

    @Override
    public PageInfo<EmergencyContainerBO> getPage(int pageNum, int pageSize, EmergencyContainerBO bo) {
        return  PageUtils.pageQueryWithCount(pageNum, pageSize,
                () -> emergencyContainerDao.findExt(bo).stream().map(x->{
                    EmergencyContainerBO emergencyContainerBO = BeanUtils.copyPropertiesReturnDest(new EmergencyContainerBO(), x);
                    emergencyContainerBO.setCloudClusterList(JSON.parseArray(x.getCloudClusters(), String.class));
                    return emergencyContainerBO;
                }).collect(Collectors.toList()));
    }

}
