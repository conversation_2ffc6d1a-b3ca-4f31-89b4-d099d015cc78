package com.cmpay.hacp.container.service;

import com.cmpay.hacp.container.bo.EmergencyContainerBO;
import com.cmpay.lemon.framework.page.PageInfo;

/**
 * <AUTHOR>
 * @create 2024/09/02 10:23
 * @since 1.0.0
 */

public interface EmergencyContainerService {

    void add(EmergencyContainerBO bo);

    void update(EmergencyContainerBO bo);

    void delete(EmergencyContainerBO bo);

    EmergencyContainerBO getDetailInfo(EmergencyContainerBO bo);

    EmergencyContainerBO getDecryptDetailInfo(EmergencyContainerBO bo);

    PageInfo<EmergencyContainerBO> getPage(int pageNum, int pageSize, EmergencyContainerBO bo);
}
