package com.cmpay.hacp.container.service.camunda;

import com.cmpay.hacp.bo.task.HacpEmergencyTaskBO;
import com.cmpay.hacp.bo.task.TaskParam;
import com.cmpay.hacp.container.bo.PodRestartParamBO;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.service.factory.AbstractTaskStrategyFactory;
import com.cmpay.hacp.service.factory.TaskStrategyType;
import com.cmpay.hacp.utils.CamundaUtil;
import com.cmpay.hacp.utils.JsonUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.BaseElement;
import org.camunda.bpm.model.bpmn.instance.ServiceTask;
import org.springframework.stereotype.Service;

import java.util.Collections;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/5/29 15:36
 */
@Slf4j
@Service(value = PodRestartImpl.BEAN_NAME)
public class PodRestartImpl extends AbstractTaskStrategyFactory {

    protected final static String BEAN_NAME = "podRestartImpl";
    public PodRestartImpl() {
        super(Collections.singletonList(new TaskStrategyType("5", "Pod重启", BEAN_NAME)));
    }

    @Override
    public boolean checkTaskParam(String json) {
        //将JSON转换成应急调度参数，检查参数是否正确
        PodRestartParamBO taskInitParamBO = (PodRestartParamBO) toTaskParam(json);
        if (taskInitParamBO == null) {
            return false;
        }
        return !JudgeUtils.isBlankAny(taskInitParamBO.getCluster()
                , taskInitParamBO.getNamespace());
    }

    @Override
    public TaskParam toTaskParam(String json) {
        //todo  详细字段转换待补充
        return JsonUtil.strToObject(json, PodRestartParamBO.class);
    }

    @Override
    public <T extends BaseElement> void bpmnTaskFieldHandle(T node, HacpEmergencyTaskBO taskInfo, BpmnModelInstance modelInstance) {
        //检查任务执行参数是否正确
        if (!checkTaskParam(taskInfo.getTaskParam())) {
            log.error("task param is error:{}",taskInfo.getTaskParam());
            BusinessException.throwBusinessException(MsgEnum.TASK_PARAM_IS_ERROR);
        }
        //设置设置服务任务执行调度
        if (node instanceof ServiceTask) {
            ((ServiceTask) node).setCamundaDelegateExpression("${doRestartPod}");
            ((ServiceTask) node).setName(taskInfo.getTaskName());
        }
    }

    @Override
    public boolean isNotDynamicParam(String taskParamJson) {
        PodRestartParamBO taskParam = JsonUtil.strToObject(taskParamJson, PodRestartParamBO.class);
        return taskParam.isAutoConfig();
    }

    @Override
    public boolean isNeedEncrypt() {
        return true;
    }

    @Override
    public String getBpmTaskType() {
        return CamundaUtil.CAMUNDA_SERVICE_TASK_ELEMENT;
    }

    @Override
    public String getActivityType() {
        return CamundaUtil.SERVICE_TASK_ELEMENT;
    }

}
