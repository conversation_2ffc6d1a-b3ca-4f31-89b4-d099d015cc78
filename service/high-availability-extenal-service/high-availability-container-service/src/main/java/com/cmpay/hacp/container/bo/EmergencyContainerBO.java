package com.cmpay.hacp.container.bo;

import com.cmpay.hacp.capable.TenantCapable;
import com.cmpay.hacp.container.entity.EmergencyContainerDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/09/02 10:25
 * @since 1.0.0
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class EmergencyContainerBO extends EmergencyContainerDO implements TenantCapable {

    private String uuid;

    private List<String> cloudClusterList;

}
