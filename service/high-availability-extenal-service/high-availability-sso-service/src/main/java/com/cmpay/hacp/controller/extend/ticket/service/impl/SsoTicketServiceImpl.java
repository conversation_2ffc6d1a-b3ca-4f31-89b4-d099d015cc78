package com.cmpay.hacp.controller.extend.ticket.service.impl;

import com.cmpay.hacp.controller.extend.ticket.bo.TicketUserInfoBO;
import com.cmpay.hacp.controller.extend.ticket.bo.UserDetailBO;
import com.cmpay.hacp.controller.extend.ticket.enums.MessageCodeEnum;
import com.cmpay.hacp.controller.extend.ticket.properties.LemonWebAdminSsoTicketProperties;
import com.cmpay.hacp.controller.extend.ticket.service.SsoTicketService;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.client.RestTemplate;

import java.util.List;

/**
 * <AUTHOR>
 */
public class SsoTicketServiceImpl implements SsoTicketService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SsoTicketServiceImpl.class);

    private static final String REG_EXP = "\r|\n";

    private static final String REPLACEMENT = "";

    private final LemonWebAdminSsoTicketProperties lemonWebAdminSsoTicketProperties;

    private final RestTemplate accountInfo4ARestTemplate;

    public SsoTicketServiceImpl(RestTemplate accountInfo4ARestTemplate, LemonWebAdminSsoTicketProperties lemonWebAdminSsoTicketProperties) {
        this.accountInfo4ARestTemplate = accountInfo4ARestTemplate;
        this.lemonWebAdminSsoTicketProperties = lemonWebAdminSsoTicketProperties;
    }

    @Override
    public TicketUserInfoBO serviceValidate(String ssoServer, String ticket, String service) {
        return this.getServiceValidate(ssoServer, ticket, service);
    }

    private TicketUserInfoBO getServiceValidate(String ssoServer, String ticket, String service) {
        String serviceValidateUrl = ssoServer.concat("?service=").concat(service).concat("&format=").concat(lemonWebAdminSsoTicketProperties.getFormat()).concat("&ticket=").concat(ticket);
        LOGGER.info("serviceValidateUrl: {}", serviceValidateUrl);
        String response = accountInfo4ARestTemplate.getForObject(serviceValidateUrl, String.class);
        LOGGER.info("response: {}", response);
        response = response.replaceAll(REG_EXP, REPLACEMENT);
        LOGGER.info("replace response: {}", response);
        return this.getTicketUserInfo(response);
    }

    private TicketUserInfoBO getTicketUserInfo(String response) {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        TicketUserInfoBO ticketUserInfo = null;
        try {
            ticketUserInfo = objectMapper.readValue(response, TicketUserInfoBO.class);
        } catch (JsonProcessingException e) {
            LOGGER.info("getTicketUserInfo error {}", e.getMessage());
            BusinessException.throwBusinessException(MessageCodeEnum.ACCESS_TOKEN_INVALID);
        }
        if (JudgeUtils.isNull(ticketUserInfo.getServiceResponse().getAuthenticationSuccess())) {
            LOGGER.info("getAuthenticationSuccess is null");
            BusinessException.throwBusinessException(MessageCodeEnum.ACCESS_TOKEN_INVALID);
        }
        if (JudgeUtils.isNull(ticketUserInfo.getServiceResponse().getAuthenticationSuccess().getAttributes())) {
            LOGGER.info("getAttributes is null");
            BusinessException.throwBusinessException(MessageCodeEnum.ACCESS_TOKEN_INVALID);
        }
        if (JudgeUtils.isEmpty(ticketUserInfo.getServiceResponse().getAuthenticationSuccess().getAttributes().getUserDetail())) {
            LOGGER.info("getUserDetail is null");
            BusinessException.throwBusinessException(MessageCodeEnum.ACCESS_TOKEN_INVALID);
        }
        List<String> userDetail = ticketUserInfo.getServiceResponse().getAuthenticationSuccess().getAttributes().getUserDetail();
        String userDetailStr = userDetail.get(0);
        UserDetailBO userDetailInfo = null;
        try {
            userDetailInfo = objectMapper.readValue(userDetailStr, UserDetailBO.class);
        } catch (JsonProcessingException e) {
            LOGGER.info("getUserDetail error {}", e.getMessage());
            BusinessException.throwBusinessException(MessageCodeEnum.ACCESS_TOKEN_INVALID);
        }
        ticketUserInfo.setUserDetail(userDetailInfo);
        return ticketUserInfo;
    }


}
