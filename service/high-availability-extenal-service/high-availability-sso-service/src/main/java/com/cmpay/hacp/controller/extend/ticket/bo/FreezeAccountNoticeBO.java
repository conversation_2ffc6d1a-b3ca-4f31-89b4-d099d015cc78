package com.cmpay.hacp.controller.extend.ticket.bo;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class FreezeAccountNoticeBO {

    private String systemName;

    private String fullName;

    private Long noLoginDay;

    private String expireTime;

    private String sendTime;

    private String application;

    private String active;

    /**
     * 转短信报文
     * @return
     */
    @Override
    public String toString() {
        return fullName + '|' + noLoginDay + '|' + systemName + '|' + application + '|' + active + '|' + expireTime + '|' + systemName;
    }
}
