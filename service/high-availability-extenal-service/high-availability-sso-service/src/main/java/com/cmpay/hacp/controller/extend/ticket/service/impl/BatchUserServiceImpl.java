package com.cmpay.hacp.controller.extend.ticket.service.impl;

import com.cmpay.hacp.bo.system.UserBO;
import com.cmpay.hacp.controller.extend.ticket.dto.AccountInfoFourADTO;
import com.cmpay.hacp.controller.extend.ticket.dto.AccountInfoFourAReqDTO;
import com.cmpay.hacp.controller.extend.ticket.dto.AccountInfoFourARspDTO;
import com.cmpay.hacp.controller.extend.ticket.properties.LemonBatchUserProperties;
import com.cmpay.hacp.controller.extend.ticket.service.BatchUserService;
import com.cmpay.hacp.enums.StatusConstans;
import com.cmpay.hacp.service.SystemAccessTokenService;
import com.cmpay.hacp.service.SystemCacheService;
import com.cmpay.hacp.service.SystemUserService;
import com.cmpay.hacp.utils.RandomUtil;
import com.cmpay.hacp.utils.crypto.AESEncryptorUtil;
import com.cmpay.hacp.utils.crypto.PasswordUtil;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class BatchUserServiceImpl implements BatchUserService {

    private static final String SUCCESS = "0";

    private final static String SYNC_USER_LOCK = "LEMON_WEB_ADMIN:SYNC_USER_LOCK";

    @Resource
    private SystemUserService systemUserService;

    @Resource(name = "hacpObjectMapper")
    private ObjectMapper lemonWebAdminObjectMapper;

    @Autowired
    private SystemCacheService systemCacheService;

    @Resource(name = "accountInfo4ARestTemplate")
    private RestTemplate restTemplate;

    @Autowired(required = false)
    private LemonBatchUserProperties lemonBatchUserProperties;

    @Resource
    private SystemAccessTokenService systemAccessTokenService;


    @Override
    public void queryAccountForSync(String startDate, String endDate, boolean isManualFlag) {

        try {
            String currentDateTimeStr = DateTimeUtils.getCurrentDateTimeStr();
            //分布式锁，如果没有这个key没有值,就给这个key设置值,并返回true
            if (systemCacheService.setIfAbsent(SYNC_USER_LOCK, currentDateTimeStr, 1, TimeUnit.HOURS)) {
                //1.转换请求DTO参数
                AccountInfoFourAReqDTO reqDTO = this.convertAccountApiParam(startDate, endDate, isManualFlag);
                //2.调用4A接口,获取用户数据
                String accountInfo = this.queryAccountInfoByApi(reqDTO);
                if (JudgeUtils.isBlank(accountInfo)) {
                    return;
                }
                //3.解密返回密文,转换为DTO对象
                String decryptResp = this.decryptResp(accountInfo);
                if (JudgeUtils.isBlank(decryptResp)) {
                    return;
                }
                //4.格式化
                AccountInfoFourARspDTO accountInfoFourARspDTO = lemonWebAdminObjectMapper.readValue(decryptResp, AccountInfoFourARspDTO.class);
                log.info("4A用户同步,解析返回密文结果:{}", accountInfoFourARspDTO);
                //4.处理返回参数
                this.handleRsp(accountInfoFourARspDTO);
            }
        } catch (Exception e) {
            log.info("4A用户同步出现异常,异常信息：{}", e.getMessage());
        } finally {
            log.info("delete queryAccountForSync lock");
            systemCacheService.delete(SYNC_USER_LOCK);
        }

    }

    /**
     * 解密返回密文
     *
     * @param encrypt
     * @return
     */
    private String decryptResp(String encrypt) {
        try {
            String decrypt = AESEncryptorUtil.aesDecrypt(lemonBatchUserProperties.getSyncPwdAesKey(), encrypt);
            return decrypt;
        } catch (Exception e) {
            log.error("decryptResp Exception {},encrypt {}", e.getMessage(), encrypt);
            return null;
        }
    }

    /**
     * 处理返回参数业务逻辑
     *
     * @param rspDTO
     */
    private void handleRsp(AccountInfoFourARspDTO rspDTO) {
        //接口查询状态判断
        if (!JudgeUtils.equalsIgnoreCase(SUCCESS, rspDTO.getResultCode())) {
            log.info("4A用户信息同步接口,查询返回失败");
            return;
        }
        if (JudgeUtils.isEmpty(rspDTO.getAccounts())) {
            log.info("4A用户信息同步接口,查询返回的用户列表为空");
            return;
        }
        for (AccountInfoFourADTO account : rspDTO.getAccounts()) {
            try {
                //根据用户名判断，eg: GY3603
                if (JudgeUtils.isEmpty(account.getPracctName())) {
                    continue;
                }

                // LocalDateTime expireTime = null;
                // if (account.getState().equals(2)) {
                //     expireTime = LocalDateTime.now();
                // }

                UserBO queryUserVO = new UserBO();
                queryUserVO.setUserName(account.getPracctName());
                UserBO userVO = systemUserService.getUserInfo(queryUserVO);
                //如果用户已经同步过,对用户进行更新操作
                if (JudgeUtils.isNotNull(userVO)) {
                    log.info("4A用户信息同步接口,该用户已同步,对该用户进行更新操作,用户名:{}", account.getPracctName());
                    UserBO user = new UserBO();
                    user.setUserId(userVO.getUserId());
                    user.setStatus(UserStatusConvertEnum.get(account.getState()).getValue());
                    user.setFullName(account.getRealName());
                    user.setEmail(account.getEmail());
                    user.setMobile(account.getMobile());
                    // user.setExpireTime(Optional.ofNullable(expireTime).orElse(userVO.getExpireTime()));
                    //执行更新操作
                    systemUserService.batchUpdate(user);
                } else {
                    UserBO user = new UserBO();
                    user.setStatus(UserStatusConvertEnum.get(account.getState()).getValue());
                    user.setUserName(account.getPracctName());
                    user.setFullName(account.getRealName());
                    user.setEmail(account.getEmail());
                    user.setMobile(account.getMobile());
                    user.setDeptId(lemonBatchUserProperties.getDefaultDeptId());
                    user.setDutyId(lemonBatchUserProperties.getDefaultDutyId());
                    //4A同步过来的用户默认赋值没有角色：N
                    user.setHasRole(StatusConstans.NOT_HAS_ROLE.getValue());
                    //生成随机密码
                    String randomPassWord = RandomUtil.getCharacterAndNumber(16);
                    String aesEncryptNewPassword = PasswordUtil.createPassWord(randomPassWord, systemAccessTokenService.getAesKey());
                    user.setPassword(aesEncryptNewPassword);
                    // user.setExpireTime(Optional.ofNullable(expireTime).orElse(LocalDateTime.now().plusDays(90)));
                    //入库存储
                    systemUserService.batchAdd(user);
                }
            } catch (Exception e) {
                log.info("遍历同步用户出现异常,跳过该次同步,异常信息：{},当前用户名:{}", e.getMessage(), account.getPracctName());
            }
        }

    }

    /**
     * 调用请求4A用户接口
     *
     * @param accountInfoFourAReqDTO
     * @return
     */
    private String queryAccountInfoByApi(AccountInfoFourAReqDTO accountInfoFourAReqDTO) {
        String rspEncrypt = null;
        try {
            String reqParam = lemonWebAdminObjectMapper.writeValueAsString(accountInfoFourAReqDTO);
            log.info("调用4A用户信息同步接口,请求信息：{}", reqParam);
            String reqParamEncrypt = AESEncryptorUtil.aesEncrypt(lemonBatchUserProperties.getSyncPwdAesKey(), reqParam);
            log.info("调用4A用户信息同步接口,加密请求信息：{}", reqParamEncrypt);
            log.info("调用4A用户信息同步接口,请求地址：{}", lemonBatchUserProperties.getUserForSyncUrl());
            rspEncrypt = restTemplate.postForObject(lemonBatchUserProperties.getUserForSyncUrl(), reqParamEncrypt, String.class);
            log.info("调用4A用户信息同步接口,返回信息：{}", rspEncrypt);
            return rspEncrypt;
        } catch (Exception e) {
            log.info("调用4A用户信息同步接口出现异常,异常信息:{}", e.getMessage());
        }
        return null;
    }

    /**
     * 参数转换
     *
     * @param startDate
     * @param endDate
     * @param isManualFlag
     * @return
     */
    private AccountInfoFourAReqDTO convertAccountApiParam(String startDate, String endDate, boolean isManualFlag) {
        AccountInfoFourAReqDTO reqDTO = new AccountInfoFourAReqDTO();
        //如果是手动操作同步用户,将入参的日期作为参数。
        if (isManualFlag) {
            reqDTO.setStartTime(startDate);
            reqDTO.setEndTime(endDate);
            return reqDTO;
        }
        //如果不是查询全量的用户,则根据配置的查询间隔时间（一天)拼接查询起始时间
        //获取今天的日期
        SimpleDateFormat format = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        Date today = new Date();
        Calendar c = Calendar.getInstance();
        c.setTime(today);
        c.add(Calendar.DAY_OF_MONTH, -Integer.valueOf(lemonBatchUserProperties.getInterval()));
        //获取查询间隔之前的日期
        Date startTime = c.getTime();
        String endTime = format.format(today);
        String start = format.format(startTime);
        reqDTO.setStartTime(start);
        reqDTO.setEndTime(endTime);
        return reqDTO;
    }

    enum UserStatusConvertEnum {
        //启用
        ENABLE(0, "启用", StatusConstans.ENABLE),
        //禁用
        DISABLE(1, "禁用", StatusConstans.DISABLE),
        //线下
        RESIGN(2, "离职", StatusConstans.DISABLE),
        ;

        private static final Map<Integer, StatusConstans> LOOKUP = new HashMap<>();

        static {
            for (UserStatusConvertEnum d : UserStatusConvertEnum.values()) {
                LOOKUP.put(d.getFlag(), d.getStatusConstans());
            }
        }

        private Integer flag;

        private String desc;

        private StatusConstans statusConstans;

        UserStatusConvertEnum(Integer flag, String desc, StatusConstans statusFlag) {
            this.flag = flag;
            this.desc = desc;
            this.statusConstans = statusFlag;
        }

        public static StatusConstans get(Integer flag) {
            return LOOKUP.get(flag);
        }

        public Integer getFlag() {
            return flag;
        }

        public String getDesc() {
            return desc;
        }

        public StatusConstans getStatusConstans() {
            return statusConstans;
        }
    }
}
