/**
 * Copyright 2020 bejson.com
 */
package com.cmpay.hacp.controller.extend.ticket.bo;

import lombok.Data;

import java.util.List;

/**
 * Auto-generated: 2020-09-18 18:33:59
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.bejson.com/java2pojo/
 */
@Data
public class AttributesBO {

    private List<String> credentialType;
    private List<Boolean> isFromNewLogin;
    private List<Double> authenticationDate;
    private List<String> authenticationMethod;
    private List<String> userDetail;
    private List<String> successfulAuthenticationHandlers;
    private List<Boolean> longTermAuthenticationRequestTokenUsed;
    private List<String> authorities;

}
