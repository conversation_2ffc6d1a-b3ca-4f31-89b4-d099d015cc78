package com.cmpay.hacp.cmft.bo;

import com.cmpay.hacp.bo.task.TaskParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class CmftDispatchParamBO extends TaskParam {
    private List<CmftDispatchStrategyBO> list;
    /**
     * 状态，1，全部启用，2，全部关闭
     */
    private Integer cmftStatus;

    private Boolean dynamicConfig;

}
