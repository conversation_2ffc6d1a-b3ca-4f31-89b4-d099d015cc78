package com.cmpay.hacp.cmft.autoconfigure;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;

@Configuration
@EnableFeignClients("com.cmpay.hacp.cmft")
public class CmftDispatchAutoConfigure {

    @Bean("cmftScheduledExecutorService")
    public ScheduledExecutorService scheduledExecutorService(@Value("${hacp.emergence.cmft.dispatch.scheduled: 10}")Integer corePoolSize){
        return new ScheduledThreadPoolExecutor(corePoolSize);
    }
}
