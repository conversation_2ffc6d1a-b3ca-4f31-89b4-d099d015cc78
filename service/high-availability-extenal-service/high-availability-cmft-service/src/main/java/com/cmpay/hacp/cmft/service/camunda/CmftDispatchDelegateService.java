package com.cmpay.hacp.cmft.service.camunda;

import com.cmpay.hacp.bo.task.HacpEmergencyTaskBO;
import com.cmpay.hacp.bo.process.ProcessExecuteLogEntity;
import com.cmpay.hacp.cmft.bo.CmftDispatchConfigBO;
import com.cmpay.hacp.cmft.bo.CmftDispatchParamBO;
import com.cmpay.hacp.cmft.bo.CmftDispatchStrategyBO;
import com.cmpay.hacp.cmft.client.dto.QueryStrategyListReqDTO;
import com.cmpay.hacp.cmft.service.CmftDispatchConfigService;
import com.cmpay.hacp.cmft.service.CmftDispatchService;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.service.EmergencyProcessService;
import com.cmpay.hacp.service.HacpCaseService;
import com.cmpay.hacp.service.HacpNodeRecodeService;
import com.cmpay.hacp.service.camunda.ServiceTaskTemplateDelegate;
import com.cmpay.hacp.service.SystemCacheService;
import com.cmpay.hacp.utils.JsonUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.page.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.RuntimeService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 应急调度或者调度回滚
 * @date 2024/5/15 9:56
 */
@Service("doCmftDispatch")
@Slf4j
public class CmftDispatchDelegateService extends ServiceTaskTemplateDelegate {

    private final CmftDispatchService cmftDispatchService;

    private final CmftDispatchConfigService cmftDispatchConfigService;


    public CmftDispatchDelegateService(HacpCaseService caseService,
            HacpNodeRecodeService nodeRecodeService,
            RuntimeService runtimeService,
            SystemCacheService cacheService,
            EmergencyProcessService emergencyProcessService,
            CmftDispatchService cmftDispatchService,
            CmftDispatchConfigService cmftDispatchConfigService) {
        super(caseService, nodeRecodeService, runtimeService, cacheService, emergencyProcessService);
        this.cmftDispatchService = cmftDispatchService;
        this.cmftDispatchConfigService = cmftDispatchConfigService;
    }

    @Override
    protected void processTask() throws InterruptedException {
        HacpEmergencyTaskBO hacpEmergencyTaskBO = taskInfoHolder.get();
        CmftDispatchParamBO cmftDispatchParamBO = JsonUtil.strToObject(hacpEmergencyTaskBO.getTaskParam(), CmftDispatchParamBO.class);
        List<CmftDispatchStrategyBO> list = cmftDispatchParamBO.getList();
        CmftDispatchConfigBO config = cmftDispatchConfigService.getInfoByWorkspaceId(execution.get().getTenantId());
        String status = cmftDispatchParamBO.getCmftStatus().equals(1) ? "启用" : "禁用";

        Map<String, List<CmftDispatchStrategyBO>> dataMap = list.stream()
                .collect(Collectors.groupingBy(CmftDispatchStrategyBO::getProjectId));
        ProcessExecuteLogEntity processExecuteLogEntity = logEntity.get();

        List<String> releaseIds = new ArrayList<>();
        for (String key : dataMap.keySet()) {
            List<CmftDispatchStrategyBO> strategyBOS = dataMap.get(key);
            List<String> ids=new ArrayList<>();
            QueryStrategyListReqDTO reqDTO = new QueryStrategyListReqDTO();
            reqDTO.setPageNo(1);
            reqDTO.setPageSize(10);
            for (CmftDispatchStrategyBO cmftDispatchStrategyBO : strategyBOS) {
                reqDTO.setId(cmftDispatchStrategyBO.getId());
                PageInfo<CmftDispatchStrategyBO> pageInfo = cmftDispatchService.queryStrategyPage(reqDTO, null, config);
                List<CmftDispatchStrategyBO> pageInfoList = pageInfo.getList();
                if (JudgeUtils.isEmpty(pageInfoList)) {
                    processExecuteLogEntity.appendBuffer("策略【" + cmftDispatchStrategyBO.getName() + "】不存在");
                    continue;
                }
                if(pageInfoList.size()!=1){
                    processExecuteLogEntity.appendBuffer("策略存在多条，请联系【双活管控】平台开发人员检查！策略ID："+cmftDispatchStrategyBO.getId());
                }
                boolean releaseStatus = cmftDispatchParamBO.getCmftStatus()
                        .equals(pageInfoList.get(0).getStatus()) && cmftDispatchParamBO.getCmftStatus()
                        .equals(pageInfoList.get(0).getReleaseStatus());
                if (releaseStatus) {
                    processExecuteLogEntity.appendBuffer("策略【" + cmftDispatchStrategyBO.getName() + "】已处于" + status + "状态");
                    continue;
                }
                log.info("发布前策略状态：{}" ,pageInfoList.get(0));
                ids.add(cmftDispatchStrategyBO.getId());
                processExecuteLogEntity.appendBuffer("正在" + status + "策略：" + cmftDispatchStrategyBO.getName());
                cmftDispatchService.modifyStrategyStatus(config, cmftDispatchStrategyBO.getId(), cmftDispatchParamBO.getCmftStatus());
                processExecuteLogEntity.appendBuffer(status + "策略：" + cmftDispatchStrategyBO.getName() + "，已完成");
                logEntity.set(processExecuteLogEntity);
            }
            if(JudgeUtils.isEmpty(ids)){
                continue;
            }
            releaseIds.addAll(ids);
            processExecuteLogEntity.appendBuffer("正在发布策略");
            cmftDispatchService.releaseStrategy(config, ids.toArray(new String[0]));
            processExecuteLogEntity.appendBuffer("发布策略已完成");
            logEntity.set(processExecuteLogEntity);
        }
        processExecuteLogEntity.appendBuffer("正在核验策略结果");
        if(JudgeUtils.isEmpty(releaseIds)){
            processExecuteLogEntity.appendBuffer("无策略发布，跳过核验！");
            logEntity.set(processExecuteLogEntity);
            return;
        }
        processExecuteLogEntity.appendBuffer("-------------------------------------");
        logEntity.set(processExecuteLogEntity);
        AtomicInteger count = new AtomicInteger(1);
        long stater = System.currentTimeMillis();
        long temp = 0;
        List<String> ids = null;
        while (true) {
            long end = System.currentTimeMillis() - stater;
            if (end > (60 * 1000 * 3) || count.get() > 6) {
                log.error("失败策略ID:{}", ids);
                BusinessException.throwBusinessException(MsgEnum.RELEASE_VERIFY_ERROR);
                break;
            }
            if (( end - temp ) / (1000 * 30) == 0 && count.get() != 1) {
                continue;
            }
            ids = new ArrayList<>();
            ProcessExecuteLogEntity processExecuteLogEntity1 = logEntity.get();
            processExecuteLogEntity1.appendBuffer("开始第【" + count.get() + "】次核验策略");
            QueryStrategyListReqDTO reqDTO = new QueryStrategyListReqDTO();
            reqDTO.setPageNo(1);
            reqDTO.setPageSize(10);
            AtomicInteger i = new AtomicInteger(0);
            for (CmftDispatchStrategyBO data : list) {
                reqDTO.setId(data.getId());
                PageInfo<CmftDispatchStrategyBO> pageInfo = cmftDispatchService.queryStrategyPage(reqDTO, null, config);
                List<CmftDispatchStrategyBO> pageInfoList = pageInfo.getList();
                if (JudgeUtils.isEmpty(pageInfoList)) {
                    processExecuteLogEntity1.appendBuffer("策略【" + data.getName() + "】不存在");
                }
                CmftDispatchStrategyBO cmftDispatchStrategyBO = pageInfoList.get(0);
                log.info("发布后策略状态：{}" ,cmftDispatchStrategyBO);
                /*
                (status，releaseStatus)=(0,0)(1,1)  = 发布成功，
                (status，releaseStatus)=(1,0)(0,1)  = 有修改未发布，
                (status，releaseStatus)=(1,2)(0,2)  = 发布失败，
                 */
                boolean releaseSuccess = cmftDispatchParamBO.getCmftStatus()
                        .equals(cmftDispatchStrategyBO.getReleaseStatus()) && cmftDispatchParamBO.getCmftStatus()
                        .equals(cmftDispatchStrategyBO.getStatus());
                if (releaseSuccess) {
                    continue;
                }
                ids.add(data.getId());
                i.incrementAndGet();
                Integer newReleaseStatus = cmftDispatchStrategyBO.getReleaseStatus();
                int newStatus = cmftDispatchStrategyBO.getStatus();
                String statusVal = newStatus == 1 ? "启用" : "禁用";
                String newReleaseStatusVal = newReleaseStatus.equals(2)?"发布失败":"未发布";
                processExecuteLogEntity1.appendBuffer("策略【" + data.getName() + "】当前状态【"+ statusVal +"】,发布状态：【"+newReleaseStatusVal+"】，预计状态：【"+status+"】，预计发布状态：【已发布】");
            }
            count.incrementAndGet();
            String msg;
            if(count.get() > 6){
                msg = "存在【" + i + "】条策略核验失败";
            }else{
                msg = i.get() > 0 ? "存在【" + i + "】条策略核验失败，等待第【" + count.get() + "/6】次核验" : "全部策略生效，核验成功！";
            }
            processExecuteLogEntity1.appendBuffer("第【" + (count.get()-1) + "】次核验策略已完成，" + msg);
            processExecuteLogEntity1.appendBuffer("-------------------------------------");
            logEntity.set(processExecuteLogEntity1);
            temp = System.currentTimeMillis() - stater;
            if (count.get() > 6 || i.get() ==0) {
                if (i.get()  != 0) {
                    log.error("失败策略ID:{}", ids);
                    traceEnabled = false;
                    BusinessException.throwBusinessException(MsgEnum.RELEASE_VERIFY_ERROR);
                }
                break;
            }
        }
    }

}
