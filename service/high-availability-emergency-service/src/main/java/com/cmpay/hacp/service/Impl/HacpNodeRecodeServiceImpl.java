package com.cmpay.hacp.service.Impl;

import com.cmpay.hacp.bo.HacpEmergencyNodeRecodeBO;
import com.cmpay.hacp.bo.process.TaskExecuteLogPage;
import com.cmpay.hacp.bo.TenantWorkspaceBO;
import com.cmpay.hacp.constant.CommonConstant;
import com.cmpay.hacp.constant.EmergencyConstant;
import com.cmpay.hacp.dao.IHacpEmergencyNodeRecodeExtDao;
import com.cmpay.hacp.entity.HacpEmergencyNodeRecodeDO;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.enums.TaskLogStatusEnum;
import com.cmpay.hacp.service.HacpNodeRecodeService;
import com.cmpay.hacp.service.SystemCacheService;
import com.cmpay.hacp.service.TenantWorkspaceService;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.hacp.utils.date.DateUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.hazelcast.collection.IList;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * @description:
 * <AUTHOR>
 * @date 2024/5/14 15:21
 * @version 1.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class HacpNodeRecodeServiceImpl implements HacpNodeRecodeService {
    private final IHacpEmergencyNodeRecodeExtDao recodeDao;

    private final TenantWorkspaceService tenantWorkspaceService;

    private final SystemCacheService cacheService;

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public Long add(HacpEmergencyNodeRecodeBO recodeBO) {
        HacpEmergencyNodeRecodeDO lastExecuteRecode = recodeDao.getLastExecuteRecode(recodeBO.getBusinessKey(),recodeBO.getActivityNodeId());
        if(JudgeUtils.isNotNull(lastExecuteRecode)){
            recodeDao.delete(lastExecuteRecode.getId());
        }
        HacpEmergencyNodeRecodeDO nodeRecodeDO = new HacpEmergencyNodeRecodeDO();
        nodeRecodeDO.setActivityNodeId(recodeBO.getActivityNodeId());
        nodeRecodeDO.setBusinessKey(recodeBO.getBusinessKey());
        nodeRecodeDO.setWorkspaceId(recodeBO.getWorkspaceId());
        nodeRecodeDO.setTaskId(recodeBO.getTaskId());
        nodeRecodeDO.setTaskName(recodeBO.getTaskName());
        nodeRecodeDO.setTaskDescribe(recodeBO.getTaskDescribe());
        nodeRecodeDO.setTaskType(recodeBO.getTaskType());
        nodeRecodeDO.setProcessId(recodeBO.getProcessId());
        nodeRecodeDO.setCaseId(recodeBO.getCaseId());
        nodeRecodeDO.setTaskParam(recodeBO.getTaskParam());
        nodeRecodeDO.setExecuteResult(recodeBO.getExecuteResult());
        nodeRecodeDO.setCreateTime(recodeBO.getCreateTime());
        TenantWorkspaceBO tenantWorkspaceBO = tenantWorkspaceService.getWorkspaceInfo(recodeBO.getWorkspaceId());
        if (JudgeUtils.isNull(tenantWorkspaceBO)) {
            BusinessException.throwBusinessException(MsgEnum.WORKPLACE_NOT_EXIST);
        }
        nodeRecodeDO.setTenantId(tenantWorkspaceBO.getTenantId());
        if (recodeDao.insert(nodeRecodeDO) != 1) {
            BusinessException.throwBusinessException(MsgEnum.DB_INSERT_FAILED);
        }

        refreshCache(nodeRecodeDO);
        return nodeRecodeDO.getId();
    }

    @Override
    public void updateStatus(HacpEmergencyNodeRecodeBO recodeBO, String resultLog, Integer duration, TaskLogStatusEnum executeResult) {
        HacpEmergencyNodeRecodeDO hacpNodeRecodeDO = BeanConvertUtil.convert(recodeBO, HacpEmergencyNodeRecodeDO.class);
        hacpNodeRecodeDO.setExecuteResult(executeResult.getCode());
        hacpNodeRecodeDO.setDuration(duration);
        hacpNodeRecodeDO.setEndTime(DateTimeUtils.getCurrentLocalDateTime());
        if(JudgeUtils.isNotNull(resultLog)){
            hacpNodeRecodeDO.setResultLog(resultLog);
        }
        if (recodeDao.update(hacpNodeRecodeDO) != 1) {
            BusinessException.throwBusinessException(MsgEnum.DB_UPDATE_FAILED);
        }

        refreshCache(hacpNodeRecodeDO);
    }

    private void refreshCache(HacpEmergencyNodeRecodeDO hacpEmergencyNodeRecodeDO) {
        String key = hacpEmergencyNodeRecodeDO.getBusinessKey() + EmergencyConstant.EXECUTE_TASK_STATE + hacpEmergencyNodeRecodeDO.getActivityNodeId();
        cacheService.delete(key);
        boolean cache = cacheService.setValue(key
                , hacpEmergencyNodeRecodeDO.getExecuteResult() + "|" + DateUtil.localDateTimeToTimeStr(hacpEmergencyNodeRecodeDO.getCreateTime())
                        +"|"+DateUtil.localDateTimeToTimeStr(hacpEmergencyNodeRecodeDO.getEndTime())+"|"+ hacpEmergencyNodeRecodeDO.getDuration(), 30, TimeUnit.MINUTES);
        cacheService.delete(EmergencyConstant.RUNTIME_LOG_PREFIX_KEY + hacpEmergencyNodeRecodeDO.getBusinessKey() +CommonConstant.COLON + hacpEmergencyNodeRecodeDO.getActivityNodeId());
        if (!cache) {
            BusinessException.throwBusinessException(MsgEnum.DB_INSERT_FAILED);
        }
    }

    @Override
    public HacpEmergencyNodeRecodeBO getLastRecode(String businessKey, String activityNodeId) {
        HacpEmergencyNodeRecodeDO lastExecuteRecode = recodeDao.getLastExecuteRecode(businessKey, activityNodeId);
        return Optional.ofNullable(lastExecuteRecode)
                .map(x -> BeanUtils.copyPropertiesReturnDest(new HacpEmergencyNodeRecodeBO(), x))
                .orElse(null);
    }

    @Override
    public HacpEmergencyNodeRecodeBO getLastRecodeAndLog(String businessKey, String activityNodeId) {
        HacpEmergencyNodeRecodeDO lastExecuteRecode = recodeDao.getLastExecuteRecodeAndLog(businessKey, activityNodeId);
        return Optional.ofNullable(lastExecuteRecode)
                .map(x -> BeanUtils.copyPropertiesReturnDest(new HacpEmergencyNodeRecodeBO(), x))
                .orElse(null);
    }

    @Override
    public HacpEmergencyNodeRecodeBO getExecuteLastRecode(String businessKey) {
        if (JudgeUtils.isBlank(businessKey)) {
            BusinessException.throwBusinessException(MsgEnum.ID_NOT_NULL);
        }
        return Optional.ofNullable(recodeDao.getExecuteLastRecode(businessKey))
                .map(x -> BeanUtils.copyPropertiesReturnDest(new HacpEmergencyNodeRecodeBO(), x))
                .orElse(null);
    }

    @Override
    public TaskExecuteLogPage getRuntimeExecuteTaskLog(String businessKey, String activityNodeId, int pageNum) {
        // 查全量的
        String key = EmergencyConstant.RUNTIME_LOG_PREFIX_KEY + businessKey+ CommonConstant.COLON + activityNodeId;
        ArrayList<String> newLogs = new ArrayList<>();
        cacheService.drainToQueue(key, newLogs, 100);
        IList<String> allLog = cacheService.getList(key);
        if (JudgeUtils.isNotEmpty(newLogs)) {
            allLog.addAll(newLogs);
        }
        return toTaskExecuteLogPage(allLog, pageNum);
    }

    @Override
    public List<String> getRuntimeExecuteTaskLog(String businessKey, String activityNodeId) {
        // 查增量的
        String key = EmergencyConstant.RUNTIME_LOG_PREFIX_KEY + businessKey + CommonConstant.COLON + activityNodeId;
        ArrayList<String> newLogs = new ArrayList<>();
        cacheService.drainToQueue(key, newLogs, 100);
        return newLogs;
    }
    @Override
    public TaskExecuteLogPage getHistoryExecuteTaskLog(String businessKey, String activityNodeId, int pageNum) {
        // 查全量的
        String lastExecuteLog = recodeDao.getLastExecuteLog(activityNodeId, businessKey);
        if(JudgeUtils.isBlank(lastExecuteLog)){{
            log.error("log is null : {}",lastExecuteLog);
            return new TaskExecuteLogPage(new ArrayList<>());
        }}
        String[] split = lastExecuteLog.split("(?<=\n)");
        return toTaskExecuteLogPage(Arrays.asList(split), pageNum);
    }

    @Override
    public List<HacpEmergencyNodeRecodeBO> getAllRecode(String businessKey) {
        List<HacpEmergencyNodeRecodeDO> executeRecode = recodeDao.getAllRecode(businessKey);
        if(JudgeUtils.isEmpty(executeRecode)){
            return new ArrayList<>();
        }
        return BeanConvertUtil.convertList(executeRecode, HacpEmergencyNodeRecodeBO.class );
    }

    @Override
    public List<HacpEmergencyNodeRecodeBO> getAllRecodeParam(String businessKey) {
        List<HacpEmergencyNodeRecodeDO> executeRecode = recodeDao.getAllRecodeParam(businessKey);
        if(JudgeUtils.isEmpty(executeRecode)){
            return new ArrayList<>();
        }
        return BeanConvertUtil.convertList(executeRecode, HacpEmergencyNodeRecodeBO.class );
    }

    @Override
    public List<HacpEmergencyNodeRecodeBO> getCompleteRecode(String businessKey) {
        List<HacpEmergencyNodeRecodeDO> executeRecode = recodeDao.getCompleteRecode(businessKey,Arrays.asList(TaskLogStatusEnum.COMPLETE.getCode(),TaskLogStatusEnum.SKIP.getCode()));
        if(JudgeUtils.isEmpty(executeRecode)){
            return new ArrayList<>();
        }
        return BeanConvertUtil.convertList(executeRecode, HacpEmergencyNodeRecodeBO.class );
    }

    @Override
    public void updateProcessInstanceId(String oldVal, String newVal) {
        recodeDao.updateProcessInstanceId(oldVal,newVal);
    }

    @Override
    public void updateSkipNode(String businessKey, String activityNodeId) {
        recodeDao.updateSkipNode(businessKey, activityNodeId);
        HacpEmergencyNodeRecodeDO lastExecuteRecode = recodeDao.getLastExecuteRecode(businessKey, activityNodeId);
        refreshCache(lastExecuteRecode);
    }

    @Override
    public void buildCache(String businessKey) {
        List<HacpEmergencyNodeRecodeDO> hacpEmergencyNodeRecodeDOS = recodeDao.findResult(businessKey);
        if(JudgeUtils.isNotEmpty(hacpEmergencyNodeRecodeDOS)){
            hacpEmergencyNodeRecodeDOS.forEach(this::refreshCache);
        }
    }

    @Override
    public void updateNodeStatus(String businessKey, String activityNodeId, String resultLog, String status) {
        recodeDao.updateNodeStatus(businessKey,activityNodeId,resultLog,status);
    }

    @Override
    public void deleteExt(HacpEmergencyNodeRecodeBO recodeBO) {
        recodeDao.deleteExt(BeanConvertUtil.convert(recodeBO,HacpEmergencyNodeRecodeDO.class));
    }

    private TaskExecuteLogPage toTaskExecuteLogPage(List<String> allLog, int index){
        if (index < 0) {
            BusinessException.throwBusinessException(MsgEnum.PARAM_PARSE_ERROR);
        }
        int from = index * 100;
        int to = from + 100;
        if (to > allLog.size()) {
            to = allLog.size();
        }
        if (from > allLog.size()) {
            TaskExecuteLogPage taskExecuteLogPage = new TaskExecuteLogPage(new ArrayList<>());
            taskExecuteLogPage.setTotal(allLog.size());
            taskExecuteLogPage.setHasNextPage(false);
            taskExecuteLogPage.setIndex(index);
            return taskExecuteLogPage;
        }
        TaskExecuteLogPage pageInfo = new TaskExecuteLogPage(allLog.subList(from, to));
        pageInfo.setTotal(allLog.size());
        pageInfo.setHasNextPage(to != allLog.size());
        pageInfo.setNextPage(index+1);
        pageInfo.setIndex(to);
        return pageInfo;
    }
}
