package com.cmpay.hacp.bo.process;

import com.cmpay.hacp.bo.task.HacpEmergencyTaskBO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Map;

/**
 * @description:
 * <AUTHOR>
 * @date 2024/5/31 10:03
 * @version 1.0
 */

@Getter
@Setter
@ToString
public class ParseProcessEntity {
    private Map<String, HacpEmergencyTaskBO> taskIds;
    private Map<String, ParseTaskTypeEntity> nodeIds;
}
