/*
 * @ClassName HacpEmergencyCaseDO
 * @Description 
 * @version 1.0
 * @Date 2024-05-16 10:53:43
 */
package com.cmpay.hacp.bo;

import com.cmpay.hacp.bo.task.HacpEmergencyTaskBO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * @description:
 * <AUTHOR>
 * @date 2024/5/16 11:11
 * @version 1.0
 */
@Getter
@Setter
@ToString
public class HacpProcessDetailBO {
    /**
     * @Fields  总任务数
     */
    private Integer totalTaskNum;
    /**
     * @Fields 未执行数
     */
    private Integer waitTaskNum;
    /**
     * 参数待确认数
     */
    private Integer confirmTaskNum;

    /**
     * @Fields 执行中数
     */
    private Integer activeTaskNum;

    /**
     * 已执行数量
     */
    private Integer completeTaskNum;
    /**
     * 失败数
     */
    private Integer failedTaskNum;
    /**
     * @Fields 流程状态
     */
    private String processStatus;

    private List<HacpCaseVariableBO> variableList;

    private ArrayList<HashMap<String, HacpEmergencyTaskBO>> taskIds;
}