package com.cmpay.hacp.service.camunda.servicetask;

import com.cmpay.hacp.bo.EmergencyHostArchiveBO;
import com.cmpay.hacp.bo.task.HacpEmergencyTaskBO;
import com.cmpay.hacp.bo.process.ProcessExecuteLogEntity;
import com.cmpay.hacp.bo.task.ShellScriptParamBO;
import com.cmpay.hacp.constant.CommonConstant;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.service.EmergencyHostArchiveService;
import com.cmpay.hacp.service.EmergencyProcessService;
import com.cmpay.hacp.service.HacpCaseService;
import com.cmpay.hacp.service.HacpNodeRecodeService;
import com.cmpay.hacp.service.camunda.ServiceTaskTemplateDelegate;
import com.cmpay.hacp.service.factory.impl.ShellScriptImpl;
import com.cmpay.hacp.service.SystemCacheService;
import com.cmpay.hacp.service.SystemUserService;
import com.cmpay.hacp.utils.JsonUtil;
import com.cmpay.hacp.utils.ShellScriptExec;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.text.StringSubstitutor;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 应急调度或者调度回滚
 * @date 2024/5/15 9:56
 */
@Service("doShellScript")
@Slf4j
public class ShellScriptService extends ServiceTaskTemplateDelegate {

    private final SystemUserService systemUserService;

    private final EmergencyHostArchiveService emergencyHostArchiveService;

    @Value("${user-ssh-path:/app/hacp-admin-app/.ssh/id_rsa}")
    private String sshPath;

    public ShellScriptService(HacpCaseService caseService,
            HacpNodeRecodeService nodeRecodeService,
            RuntimeService runtimeService,
            SystemCacheService cacheService,
            EmergencyProcessService emergencyProcessService,
            SystemUserService systemUserService,
            EmergencyHostArchiveService emergencyHostArchiveService) {
        super(caseService, nodeRecodeService, runtimeService, cacheService, emergencyProcessService);
        this.systemUserService = systemUserService;
        this.emergencyHostArchiveService = emergencyHostArchiveService;
    }

    @Override
    protected void processTask() {
        HacpEmergencyTaskBO hacpEmergencyTaskBO = taskInfoHolder.get();
        DelegateExecution delegateExecution = execution.get();
        String taskType = hacpEmergencyTaskBO.getTaskType();
        //获取动态变量,所有的全部为动态变量
        ShellScriptParamBO shellScriptParamBO = JsonUtil.strToObject(hacpEmergencyTaskBO.getTaskParam(), ShellScriptParamBO.class);
        String script = ShellScriptImpl.getScript(taskType,shellScriptParamBO.getScript());
        List<ShellScriptParamBO.ScriptParam> scriptParam = shellScriptParamBO.getScriptParam();
        if (JudgeUtils.isNotEmpty(scriptParam)) {
            HashMap<String, String> hashMap = new HashMap<>();
            scriptParam.forEach(x-> hashMap.put(x.getName(), x.getValue()));
            StringSubstitutor sub = new StringSubstitutor(hashMap);
            script = sub.replace(script);

        }
        /*
         * 每个命令之间用 ; 隔开。说明：各命令的执行给果，不会影响其它命令的执行。换句话说，各个命令都会执行，但不保证每个命令都执行成功。
         * 每个命令之间用 && 隔开。说明：若前面的命令执行成功，才会去执行后面的命令。这样可以保证所有的命令执行完毕后，执行过程都是成功的。
         * 每个命令之间用 || 隔开。说明：|| 是或的意思，只有前面的命令执行失败后才去执行下一条命令，直到执行成功一条命令为止
         */
        String lastScript = script;
        List<ShellScriptParamBO.ConnectParam> connectParams =new ArrayList<>();
        switch (shellScriptParamBO.getShellLoginType()) {
            case HOST:
            case TAG:
            case APP:
                List<EmergencyHostArchiveBO> hostList=emergencyHostArchiveService.findByBusinessKeyAndTaskId(delegateExecution.getBusinessKey(),delegateExecution.getCurrentActivityId(),delegateExecution.getTenantId(),null);
                connectParams = hostList.stream().map(m -> {
                    ShellScriptParamBO.ConnectParam connectParam = new ShellScriptParamBO.ConnectParam();
                    connectParam.setPassword(m.getHostPassword());
                    connectParam.setIpPort(m.getHostAddress() + CommonConstant.COLON + m.getHostPort());
                    connectParam.setUserName(m.getHostUsername());
                    return connectParam;
                }).collect(Collectors.toList());
                break;
            case USERNAME_PASSWORD:
                connectParams = shellScriptParamBO.getConnectParam();
                break;
            default:
                log.error("login type is error : {}",shellScriptParamBO.getShellLoginType());
                BusinessException.throwBusinessException(MsgEnum.SHELL_LOGIN_TYPE_ERROR);
                break;
        }
        executionHost(connectParams, lastScript);
    }

    private void executionHost(List<ShellScriptParamBO.ConnectParam> hostList,
                               String lastScript) {
        if (JudgeUtils.isNotEmpty(hostList)) {

            for (ShellScriptParamBO.ConnectParam connectParam : hostList) {
                ShellScriptExec shellScriptExec;
                if (JudgeUtils.isNotBlank(connectParam.getPassword())) {
                    //账号密码
                    shellScriptExec = new ShellScriptExec(connectParam.getIpPort(),
                            connectParam.getUserName(),
                            systemUserService.decryptPassword(connectParam.getPassword()),
                            ShellScriptExec.ConnectMethod.USERNAME_PASSWORD);
                } else {
                    shellScriptExec = new ShellScriptExec(connectParam.getIpPort(),
                            connectParam.getUserName(),
                            sshPath,
                            ShellScriptExec.ConnectMethod.USERNAME_NOT_PASSWORD);
                }
                ProcessExecuteLogEntity processExecuteLog =  logEntity.get();
                shellScriptExec.execCommand(lastScript, processExecuteLog);
                logEntity.set(processExecuteLog);
            }
        }
    }

}
