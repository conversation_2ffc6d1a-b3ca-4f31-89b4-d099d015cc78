/*
 * @ClassName HacpEmergencyCaseDO
 * @Description 
 * @version 1.0
 * @Date 2024-05-16 10:53:43
 */
package com.cmpay.hacp.bo.process;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.camunda.bpm.engine.rest.dto.task.TaskDto;

/**
 * @description:
 * <AUTHOR>
 * @date 2024/5/16 11:11
 * @version 1.0
 */
@Getter
@Setter
@ToString
public class HacpProcessTaskExtDto extends TaskDto{
    private Long taskId;

    private String executeLog;

}