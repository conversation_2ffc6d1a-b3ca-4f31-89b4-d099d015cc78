package com.cmpay.hacp.service.Impl;

import com.cmpay.hacp.bo.EmergencyEntityTagBO;
import com.cmpay.hacp.bo.EmergencyTagBO;
import com.cmpay.hacp.bo.task.HacpEmergencyTaskBO;
import com.cmpay.hacp.bo.task.ShellScriptParamBO;
import com.cmpay.hacp.bo.TenantWorkspaceBO;
import com.cmpay.hacp.constant.CommonConstant;
import com.cmpay.hacp.dao.IHacpEmergencyTaskExtDao;
import com.cmpay.hacp.entity.HacpEmergencyTaskDO;
import com.cmpay.hacp.enums.*;
import com.cmpay.hacp.service.EmergencyEntityTagService;
import com.cmpay.hacp.service.EmergencyTagService;
import com.cmpay.hacp.service.HacpTaskService;
import com.cmpay.hacp.service.camunda.ProcessTaskService;
import com.cmpay.hacp.service.factory.AbstractTaskStrategyFactory;
import com.cmpay.hacp.service.SystemCacheService;
import com.cmpay.hacp.service.SystemCipherService;
import com.cmpay.hacp.service.TenantWorkspaceService;
import com.cmpay.hacp.utils.DropDownUtils;
import com.cmpay.hacp.utils.JsonUtil;
import com.cmpay.hacp.utils.crypto.SM2EncryptorUtil;
import com.cmpay.hacp.utils.crypto.SM4EncryptorUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.page.PageInfo;
import com.cmpay.lemon.framework.security.SecurityUtils;
import com.cmpay.lemon.framework.utils.PageUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.rest.dto.task.TaskDto;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/5/14 15:21
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class HacpTaskServiceImpl implements HacpTaskService {
    private final IHacpEmergencyTaskExtDao taskDao;

    private final TenantWorkspaceService tenantWorkspaceService;

    private final ProcessTaskService taskService;


    private final SystemCipherService systemCipherService;

    private final SystemCacheService systemCacheService;

    private final EmergencyEntityTagService emergencyEntityTagService;

    private final EmergencyTagService emergencyTagService;

    @Value("${spring.application.name}")
    private String applicationName;

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void addTask(HacpEmergencyTaskBO taskBO, String workspaceId) {
        String loginUserId = SecurityUtils.getLoginUserId();
        //校验任务参数合法性
        AbstractTaskStrategyFactory factory = AbstractTaskStrategyFactory.newInstance(taskBO.getTaskType());

        if(taskBO.getTaskParam().length()>51200){
            BusinessException.throwBusinessException(MsgEnum.TASK_PARAM_IS_TOO_LONG);
        }

        //参数检查
        if (!factory.checkTaskParam(taskBO.getTaskParam())) {
            log.error("error:{}",taskBO.getTaskParam());
            BusinessException.throwBusinessException(MsgEnum.TASK_PARAM_IS_ERROR);
        }
        TenantWorkspaceBO workspaceInfo = tenantWorkspaceService.getWorkspaceInfo(workspaceId);
        if (JudgeUtils.isNull(workspaceInfo)) {
            log.error("workspaceInfo is null");
            BusinessException.throwBusinessException(MsgEnum.WORKPLACE_NOT_EXIST);
        }
        if (factory.isNeedEncrypt()) {
            taskBO.setTaskParam(factory.toFieldEncrypt(taskBO, AbstractTaskStrategyFactory.ExtState.TASK_ADD));
        }
        HacpEmergencyTaskDO taskDO = new HacpEmergencyTaskDO();
        BeanUtils.copyProperties(taskDO, taskBO);
        taskDO.setCreateTime(DateTimeUtils.getCurrentLocalDateTime());
        taskDO.setStatus(StatusEnum.ENABLE.getValue());
        taskDO.setWorkspaceId(workspaceId);
        taskDO.setTenantId(workspaceInfo.getTenantId());
        taskDO.setOperator(loginUserId);
        taskDO.setOperatorName(SecurityUtils.getLoginName());
        taskDO.setUpdateTime(DateTimeUtils.getCurrentLocalDateTime());
        taskDO.setTaskType(taskBO.getTaskType());
        if (taskDao.insert(taskDO) != 1) {
            log.error("insert is error : {}",taskDO);
            BusinessException.throwBusinessException(MsgEnum.DB_INSERT_FAILED);
        }
        taskBO.setWorkspaceId(workspaceId);
        taskBO.setId(taskDO.getId());
        addTaskTag(taskBO);
    }

    private void addTaskTag(HacpEmergencyTaskBO taskBO) {
        emergencyEntityTagService.deleteOrAdd(taskBO,EntityTypeEnum.TASK);
    }

    @Override
    public PageInfo<HacpEmergencyTaskBO> queryTaskList(HacpEmergencyTaskBO taskBO, int pageNum, int pageSize) {
        taskBO.setStatus(StatusEnum.ENABLE.getValue());
        Optional.ofNullable(taskBO.getTaskType()).ifPresent(taskBO::setTaskType);
        taskBO.setEntityType(EntityTypeEnum.TASK.getValue());
        PageInfo<HacpEmergencyTaskBO> pageInfo = PageUtils.pageQueryWithCount(pageNum,
                pageSize,
                () -> taskDao.findExt(taskBO));
        List<HacpEmergencyTaskBO> collect = pageInfo.getList().stream()
                .peek(x -> x.setTaskType(x.getTaskType()))
                .collect(Collectors.toList());
        PageInfo<HacpEmergencyTaskBO> boPageInfo = new PageInfo<>(collect);
        boPageInfo.setHasNextPage(pageInfo.isHasNextPage());
        boPageInfo.setTotal(pageInfo.getTotal());
        boPageInfo.setPages(pageInfo.getPages());
        return boPageInfo;
    }

    @Override
    public HacpEmergencyTaskBO getTaskInfo(Long id, boolean formatParam, boolean hidePassword) {
        if (JudgeUtils.isNull(id)) {
            BusinessException.throwBusinessException(MsgEnum.ID_NOT_NULL);
        }
        HacpEmergencyTaskDO taskDO = taskDao.get(id);
        if (JudgeUtils.isNull(taskDO) || StatusEnum.DISABLE.getValue().equals(taskDO.getStatus())) {
            return null;
        }
        HacpEmergencyTaskBO taskBO = BeanUtils.copyPropertiesReturnDest(new HacpEmergencyTaskBO(), taskDO);
        taskBO.setTaskType(taskDO.getTaskType());
        if (hidePassword) {
            //校验任务参数合法性
            AbstractTaskStrategyFactory factory = AbstractTaskStrategyFactory.newInstance(taskDO.getTaskType());
            taskBO.setTaskParam(factory.toMaskField(taskBO.getTaskParam()));
        }
        if (JudgeUtils.isNotBlank(taskBO.getTaskParam()) && formatParam) {
            taskBO.setTaskParam(taskBO.getTaskParam().replaceAll("\\\\\"", ""));
        }
        EmergencyEntityTagBO bo = new EmergencyEntityTagBO();
        bo.setEntityType(EntityTypeEnum.TASK);
        bo.setWorkspaceId(taskBO.getWorkspaceId());
        bo.setEntityId(taskBO.getId());
        List<EmergencyEntityTagBO> list = emergencyEntityTagService.getList(bo);
        if(JudgeUtils.isNotEmpty(list)){
            taskBO.setTagIds(list.stream().map(EmergencyEntityTagBO::getTagId).collect(Collectors.toList()));
            taskBO.setTagNames(emergencyTagService.getTagInfoByIds(taskBO.getTagIds()).stream().map(EmergencyTagBO::getTagName).collect(Collectors.toList()));
        }
        return taskBO;
    }

    @Override
    public void deleteTask(HacpEmergencyTaskBO bo) {
        HacpEmergencyTaskDO taskDO = new HacpEmergencyTaskDO();
        taskDO.setId(bo.getId());
        taskDO.setStatus(StatusEnum.DISABLE.getValue());

        if (taskDao.update(taskDO) != 1) {
            BusinessException.throwBusinessException(MsgEnum.DB_DELETE_FAILED);
        }

        EmergencyEntityTagBO emergencyEntityTagBO = new EmergencyEntityTagBO();
        emergencyEntityTagBO.setEntityId(bo.getId());
        emergencyEntityTagBO.setEntityType(EntityTypeEnum.TASK);
        emergencyEntityTagBO.setWorkspaceId(bo.getWorkspaceId());
        emergencyEntityTagService.delete(emergencyEntityTagBO);
    }

    @Override
    public void updateTask(HacpEmergencyTaskBO taskBO) {
        if (JudgeUtils.isNull(taskBO.getId())) {
            BusinessException.throwBusinessException(MsgEnum.ID_NOT_NULL);
        }
        AbstractTaskStrategyFactory factory = AbstractTaskStrategyFactory.newInstance(taskBO.getTaskType());
        HacpEmergencyTaskDO taskDO = new HacpEmergencyTaskDO();
        BeanUtils.copyProperties(taskDO, taskBO);
        taskDO.setUpdateTime(LocalDateTime.now());
        taskDO.setStatus(StatusEnum.ENABLE.getValue());
        taskDO.setTaskParam(factory.toFieldEncrypt(taskBO, AbstractTaskStrategyFactory.ExtState.TASK_UPDATE));
        /*if (JudgeUtils.isNotBlank(taskDO.getTaskParam())) {
            taskDO.setTaskParam(taskDO.getTaskParam().replaceAll("\\\\\"", ""));
        }*/
        taskDO.setTmSmp(DateTimeUtils.getCurrentDateTimeStr());
        if (taskDao.update(taskDO) != 1) {
            BusinessException.throwBusinessException(MsgEnum.DB_UPDATE_FAILED);
        }

        EmergencyEntityTagBO emergencyEntityTagBO = new EmergencyEntityTagBO();
        emergencyEntityTagBO.setEntityId(taskBO.getId());
        emergencyEntityTagBO.setEntityType(EntityTypeEnum.TASK);
        emergencyEntityTagBO.setWorkspaceId(taskBO.getWorkspaceId());
        emergencyEntityTagService.delete(emergencyEntityTagBO);
        addTaskTag(taskBO);
    }


    @Override
    public List<TaskDto> queryWaitingTasks(String userId, int firstResult, int maxResults) {
        return taskService.queryWaitingTasks(userId, firstResult, maxResults);
    }

    @Override
    public String encryptTaskParam(String taskParamJson, String uuid) {
        return this.encrypt(taskParamJson,uuid);
    }

    @Override
    public Map<String, List<KeyValue>> getDropDown() {
        List<KeyValue> quickScriptCommand = DropDownUtils.getList(QuickScriptCommandEnum.values());
        Map<String, List<KeyValue>> result=new HashMap<>();
        result.put("quickScriptCommand",quickScriptCommand);
        return result;
    }


    /**
     * 加密
     *
     * @param taskParamJson task param json
     * @param uuid          uuid
     * @return {@link String}
     */
    private String encrypt(String taskParamJson, String uuid) {
        if (JudgeUtils.isBlank(taskParamJson)) {
            return taskParamJson;
        }

        String sm4RandomSalt = null;
        try {
            // 可能没加密任何东西，但是传递了uuid过来
            sm4RandomSalt = systemCipherService.getSm4RandomSalt(uuid, uuid);
        } catch (BusinessException e) {
            if (e.getMsgCd().equals(MsgEnum.SM4_RANDOM_SALT_CACHE_NULL_ERROR.getMsgCd())) {
                return taskParamJson;
            }
        }

        ShellScriptParamBO taskParam = JsonUtil.strToObject(taskParamJson, ShellScriptParamBO.class);

        if(!ShellLoginTypeEnum.USERNAME_PASSWORD.equals(taskParam.getShellLoginType())){
            return taskParamJson;
        }
        if(taskParam.isDynamicConfig()&&JudgeUtils.isEmpty(taskParam.getConnectParam())){
            return taskParamJson;
        }

        List<ShellScriptParamBO.ConnectParam> connectParam = taskParam.getConnectParam();
        int i = 0;
        for (ShellScriptParamBO.ConnectParam param : connectParam) {
            if (JudgeUtils.isNotNull(param.getId())) {
                param.setId(i++);
                continue;
            }
            param.setId(i++);
            if(JudgeUtils.isBlank(param.getPassword())){
                continue;
            }

            String password = null;
            try {
                String sm4Password = SM4EncryptorUtil.decryptEcb(sm4RandomSalt, param.getPassword());
                //前端sm2公钥加密,后台sm2私钥解密。
                password = SM2EncryptorUtil.decrypt(systemCipherService.getSm2PrivateKey(applicationName), sm4Password);
            } catch (Exception e) {
                BusinessException.throwBusinessException(MsgEnum.WRONG_USERNAME_OR_PASSWORD);
            }
            param.setPassword(systemCipherService.otherModuleEncryptPassword(password));
        }
        systemCacheService.delete(CommonConstant.SM4_RANDOM_SALT + uuid + uuid);
        return JsonUtil.objToStr(taskParam);
    }
}
