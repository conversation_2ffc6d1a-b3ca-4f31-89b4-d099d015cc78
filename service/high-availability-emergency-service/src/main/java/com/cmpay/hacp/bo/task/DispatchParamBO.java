package com.cmpay.hacp.bo.task;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * @description:
 * <AUTHOR>
 * @date 2024/6/18 15:21
 * @version 1.0
 */
@Getter
@Setter
@ToString
public class DispatchParamBO extends TaskParam{
    /**
     * 应急调度id
     */
    private List<Integer> dispatchId;
    /**
     * 应急调度类型 open 1 close 2
     */
    private String dispatchStatus;
}
