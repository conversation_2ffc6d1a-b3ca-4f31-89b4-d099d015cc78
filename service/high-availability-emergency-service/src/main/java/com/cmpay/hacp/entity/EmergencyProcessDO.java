/*
 * @ClassName EmergencyProcessDO
 * @Description 
 * @version 1.0
 * @Date 2024-12-25 17:53:09
 */
package com.cmpay.hacp.entity;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;
import java.time.LocalDateTime;

@DataObject
public class EmergencyProcessDO extends BaseDO {
    /**
     * @Fields id 
     */
    private Long id;
    /**
     * @Fields businessKey 绑定的业务id
     */
    private String businessKey;
    /**
     * @Fields workspaceId 项目空间ID
     */
    private String workspaceId;
    /**
     * @Fields caseTitle 预案名称
     */
    private String caseTitle;
    /**
     * @Fields processDefId ProcessDefinitionID
     */
    private String processDefId;
    /**
     * @Fields caseDeployId 部署id
     */
    private String caseDeployId;
    /**
     * @Fields caseDesc 预案描述
     */
    private String caseDesc;
    /**
     * @Fields auditUserId 处于审核任务时的审核人员
     */
    private String auditUserId;
    /**
     * @Fields startUser 启动用户
     */
    private String startUser;
    /**
     * @Fields endTime 流程结束时间
     */
    private LocalDateTime endTime;
    /**
     * @Fields operatorId 操作人
     */
    private String operatorId;
    /**
     * @Fields operatorName 操作名
     */
    private String operatorName;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields updateTime 修改时间
     */
    private LocalDateTime updateTime;
    /**
     * @Fields status 删除状态
     */
    private String status;
    /**
     * @Fields state 执行状态
     */
    private String state;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBusinessKey() {
        return businessKey;
    }

    public void setBusinessKey(String businessKey) {
        this.businessKey = businessKey;
    }

    public String getWorkspaceId() {
        return workspaceId;
    }

    public void setWorkspaceId(String workspaceId) {
        this.workspaceId = workspaceId;
    }

    public String getCaseTitle() {
        return caseTitle;
    }

    public void setCaseTitle(String caseTitle) {
        this.caseTitle = caseTitle;
    }

    public String getProcessDefId() {
        return processDefId;
    }

    public void setProcessDefId(String processDefId) {
        this.processDefId = processDefId;
    }

    public String getCaseDeployId() {
        return caseDeployId;
    }

    public void setCaseDeployId(String caseDeployId) {
        this.caseDeployId = caseDeployId;
    }

    public String getCaseDesc() {
        return caseDesc;
    }

    public void setCaseDesc(String caseDesc) {
        this.caseDesc = caseDesc;
    }

    public String getAuditUserId() {
        return auditUserId;
    }

    public void setAuditUserId(String auditUserId) {
        this.auditUserId = auditUserId;
    }

    public String getStartUser() {
        return startUser;
    }

    public void setStartUser(String startUser) {
        this.startUser = startUser;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }
}