/*
 * @ClassName HacpEmergencyNodeRecodeDO
 * @Description 
 * @version 1.0
 * @Date 2024-07-30 09:38:51
 */
package com.cmpay.hacp.bo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;
/**
 * @description:
 * <AUTHOR>
 * @date 2024/7/30 9:41
 * @version 1.0
 */
@Getter
@Setter
@ToString
public class HacpEmergencyNodeRecodeBO{
    /**
     * @Fields id id
     */
    private Long id;
    /**
     * @Fields tenantId 租户编号
     */
    private String tenantId;
    /**
     * @Fields workplaceId 项目编号
     */
    private String workspaceId;
    /**
     * @Fields caseId 案例编号
     */
    private Long caseId;
    /**
     * @Fields taskId 任务编号
     */
    private Long taskId;
    /**
     * @Fields taskName 任务名称
     */
    private String taskName;
    /**
     * @Fields taskDescribe 任务描述
     */
    private String taskDescribe;
    /**
     * @Fields processId 部署id
     */
    private String processId;
    /**
     * @Fields businessKey 业务KEY
     */
    private String businessKey;
    /**
     * @Fields taskType 任务类型
     */
    private String taskType;
    /**
     * @Fields activityNodeId 活动节点ID
     */
    private String activityNodeId;
    /**
     * @Fields executeResult 执行结果
     */
    private String executeResult;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields duration 耗时
     */
    private Integer duration;
    /**
     * @Fields taskParam 请求参数
     */
    private String taskParam;
    /**
     * @Fields resultLog 响应参数
     */
    private String resultLog;

    private Boolean skip;

    private LocalDateTime startTime;
    private LocalDateTime endTime;
}