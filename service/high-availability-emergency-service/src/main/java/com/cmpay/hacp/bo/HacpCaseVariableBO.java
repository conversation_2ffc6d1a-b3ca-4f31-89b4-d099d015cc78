/*
 * @ClassName HacpEmergencyTaskDO
 * @Description 
 * @version 1.0
 * @Date 2024-05-14 15:08:39
 */
package com.cmpay.hacp.bo;

import com.cmpay.hacp.bo.task.TaskParam;
import com.cmpay.hacp.capable.TaskEncryptCapable;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * @description:
 * <AUTHOR>
 * @date 2024/5/14 15:16
 * @version 1.0
 */
@Getter
@Setter
@ToString
public class HacpCaseVariableBO implements TaskEncryptCapable {
    private TaskParam taskParamJson;

    private String taskParam;

    private String taskType;

    private String taskName;

    private String activityId;

    private String taskId;

    private String taskDescribe;

    private String uuid;

    private List<Integer> tagIds;

    private List<String> tagNames;

    /**
     * 需要时赋值taskId，用于加密使用
     */
    private Long id;

}