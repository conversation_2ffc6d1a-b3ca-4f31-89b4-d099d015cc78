package com.cmpay.hacp.utils;


import com.cmpay.hacp.bo.process.ProcessExecuteLogEntity;
import com.cmpay.hacp.constant.CommonConstant;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.jcraft.jsch.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;

/**
 * @description:
 * <AUTHOR>
 * @date 2024/7/29 10:36
 * @version 1.0
 */
public class ShellScriptExec {
    private static final Logger logger = LoggerFactory.getLogger(ShellScriptExec.class);
    private final String host;
    private final String username;
    private String password;
    private int port = 22;
    private int timeout = 60 * 60 * 1000;

    private String keyPath = "/app/apprun/.ssh/id_rsa";

    public ShellScriptExec(String host, String username, String password, int port, int timeout) {
        this.host = host;
        this.username = username;
        this.password = password;
        this.port = port;
        this.timeout = timeout;
    }

    public ShellScriptExec(String host, String username, String connectParam, ConnectMethod connectMethod) {
        if (host.contains(CommonConstant.COLON)) {
            //切割端口和IP
            String[] split = host.split(CommonConstant.COLON);
            this.host = split[0];
            this.port = Integer.parseInt(split[1]);
        } else {
            this.host = host;
        }
        this.username = username;
        if (JudgeUtils.equals(ConnectMethod.USERNAME_PASSWORD, connectMethod)) {
            this.password = connectParam;
        } else {
            this.keyPath = connectParam;
        }

    }

    public ShellScriptExec(String host, String username) {
        if (host.contains(CommonConstant.COLON)) {
            //切割端口和IP
            String[] split = host.split(CommonConstant.COLON);
            this.host = split[0];
            this.port = Integer.parseInt(split[1]);
        } else {
            this.host = host;
        }
        this.username = username;
    }

    public void execCommand(String cmd, ProcessExecuteLogEntity logEntity) {
        logEntity.appendBuffer("执行脚本：").appendBuffer(cmd);
        Session session = null;
        ChannelExec channelExec = null;
        BufferedReader inputStreamReader = null;
        BufferedReader errInputStreamReader = null;
        try {
            // 1. 获取 ssh session
            session = setConnectMethod();
            logEntity.appendBuffer("连接配置：" + username + "@"+ host);
            // 2. 通过 exec 方式执行 shell 命令
            channelExec = (ChannelExec) session.openChannel("exec");
            channelExec.setCommand("source /etc/profile&&source ~/.bash_profile&&source ~/.bashrc&&"+cmd);
            channelExec.connect();  // 执行命令
            // 3. 获取标准输入流
            inputStreamReader = new BufferedReader(new InputStreamReader(channelExec.getInputStream()));
            // 4. 获取标准错误输入流
            errInputStreamReader = new BufferedReader(new InputStreamReader(channelExec.getErrStream()));

            // 5. 记录命令执行 log
            logEntity.appendBuffer("执行日志如下：");
            String line;
            while ((line = inputStreamReader.readLine()) != null) {
                logEntity.appendBuffer(line);
            }

            // 6. 记录命令执行错误 log
            String errLine;
            boolean error = true;
            while ((errLine = errInputStreamReader.readLine()) != null) {
                if(error){
                    logEntity.appendBuffer("----------------------------------------");
                    error=false;
                }
                logEntity.appendBuffer(errLine, ProcessExecuteLogEntity.LogLevel.ERROR);
            }

            // 7. 输出 shell 命令执行日志
            logger.info("exitStatus=" + channelExec.getExitStatus() + ", openChannel.isClosed=" + channelExec.isClosed());
            logEntity.appendBuffer("结束状态=" + channelExec.getExitStatus() + ", openChannel.isClosed=" + channelExec.isClosed());
            logEntity.appendBuffer("----------------------------------------");
        } catch (JSchException e) {
            logEntity.appendBuffer("JSchException：");
            logEntity.appendBuffer(e.getMessage(),ProcessExecuteLogEntity.LogLevel.ERROR);
            logger.error("error: ",e);
            BusinessException.throwBusinessException(MsgEnum.REMOTE_MACHINE_CONNECTION_FAIL,e);
        } catch (IOException e) {
            logEntity.appendBuffer("IOException：");
            logEntity.appendBuffer(e.getMessage(), ProcessExecuteLogEntity.LogLevel.ERROR);
            BusinessException.throwBusinessException(MsgEnum.SCRIPT_EXECUTION_FAILED,e);
        } finally {
            try {
                if (inputStreamReader != null) {
                    inputStreamReader.close();
                }
                if (errInputStreamReader != null) {
                    errInputStreamReader.close();
                }

                if (channelExec != null) {
                    channelExec.disconnect();
                }
                if (session != null) {
                    session.disconnect();
                }
            } catch (IOException e) {
                logEntity.appendBuffer(e.getMessage(), ProcessExecuteLogEntity.LogLevel.ERROR);
                BusinessException.throwBusinessException(MsgEnum.REMOTE_MACHINE_DISCONNECTION_FAIL,e);
            }
        }
    }

    private Session setConnectMethod() throws JSchException, IOException {
        JSch jSch = new JSch();
        Session session;
        if (JudgeUtils.isNotBlank(password)) {
            session = jSch.getSession(username, host, port);
            session.setPassword(password);
        } else {
            jSch.addIdentity(keyPath);
            // 1. 获取 ssh session
            session = jSch.getSession(username, host, port);
        }
        session.setTimeout(timeout);
        session.setConfig("StrictHostKeyChecking", "no");
        session.connect();
        return session;
    }

    public enum ConnectMethod{
        USERNAME_PASSWORD,USERNAME_NOT_PASSWORD
    }
}