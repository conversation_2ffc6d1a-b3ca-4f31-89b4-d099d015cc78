/*
 * @ClassName HacpEmergencyTaskDO
 * @Description 
 * @version 1.0
 * @Date 2024-07-09 14:50:55
 */
package com.cmpay.hacp.entity;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;

import java.time.LocalDateTime;

@DataObject
public class HacpEmergencyTaskDO extends BaseDO {
    /**
     * @Fields id id
     */
    private Long id;
    /**
     * @Fields tenantId 租户编号
     */
    private String tenantId;
    /**
     * @Fields workspaceId 项目编号
     */
    private String workspaceId;
    /**
     * @Fields taskName 任务名称
     */
    private String taskName;
    /**
     * @Fields taskType 任务类型：人工、系统应急调度、接口调用、shell
     */
    private String taskType;
    /**
     * @Fields taskDescribe 任务描述
     */
    private String taskDescribe;
    /**
     * @Fields taskOperator 任务执行人
     */
    private String taskOperator;
    /**
     * @Fields operator 操作人
     */
    private String operator;
    /**
     * @Fields operatorName 操作名
     */
    private String operatorName;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields updateTime 修改时间
     */
    private LocalDateTime updateTime;
    /**
     * @Fields status 状态
     */
    private String status;
    /**
     * @Fields taskParam 任务参数JSON
     */
    private String taskParam;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getWorkspaceId() {
        return workspaceId;
    }

    public void setWorkspaceId(String workspaceId) {
        this.workspaceId = workspaceId;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getTaskType() {
        return taskType;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }

    public String getTaskDescribe() {
        return taskDescribe;
    }

    public void setTaskDescribe(String taskDescribe) {
        this.taskDescribe = taskDescribe;
    }

    public String getTaskOperator() {
        return taskOperator;
    }

    public void setTaskOperator(String taskOperator) {
        this.taskOperator = taskOperator;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getTaskParam() {
        return taskParam;
    }

    public void setTaskParam(String taskParam) {
        this.taskParam = taskParam;
    }
}