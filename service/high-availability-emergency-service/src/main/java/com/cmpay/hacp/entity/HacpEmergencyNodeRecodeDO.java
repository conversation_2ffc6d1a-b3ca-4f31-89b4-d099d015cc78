/*
 * @ClassName HacpEmergencyNodeRecodeDO
 * @Description 
 * @version 1.0
 * @Date 2024-11-05 10:23:38
 */
package com.cmpay.hacp.entity;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;
import java.time.LocalDateTime;

@DataObject
public class HacpEmergencyNodeRecodeDO extends BaseDO {
    /**
     * @Fields id id
     */
    private Long id;
    /**
     * @Fields tenantId 租户编号
     */
    private String tenantId;
    /**
     * @Fields workspaceId 项目编号
     */
    private String workspaceId;
    /**
     * @Fields caseId 案例编号
     */
    private Long caseId;
    /**
     * @Fields taskId 任务编号
     */
    private Long taskId;
    /**
     * @Fields taskName 任务名称
     */
    private String taskName;
    /**
     * @Fields taskDescribe 任务描述
     */
    private String taskDescribe;
    /**
     * @Fields processId 部署id
     */
    private String processId;
    /**
     * @Fields businessKey 业务KEY
     */
    private String businessKey;
    /**
     * @Fields taskType 任务类型
     */
    private String taskType;
    /**
     * @Fields activityNodeId 活动节点ID
     */
    private String activityNodeId;
    /**
     * @Fields executeResult 执行结果(0成功，1失败，2进行中，3失败并跳过)
     */
    private String executeResult;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields duration 耗时
     */
    private Integer duration;
    /**
     * @Fields endTime 结束时间
     */
    private LocalDateTime endTime;
    /**
     * @Fields taskParam 请求参数
     */
    private String taskParam;
    /**
     * @Fields resultLog 响应参数
     */
    private String resultLog;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getWorkspaceId() {
        return workspaceId;
    }

    public void setWorkspaceId(String workspaceId) {
        this.workspaceId = workspaceId;
    }

    public Long getCaseId() {
        return caseId;
    }

    public void setCaseId(Long caseId) {
        this.caseId = caseId;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getTaskDescribe() {
        return taskDescribe;
    }

    public void setTaskDescribe(String taskDescribe) {
        this.taskDescribe = taskDescribe;
    }

    public String getProcessId() {
        return processId;
    }

    public void setProcessId(String processId) {
        this.processId = processId;
    }

    public String getBusinessKey() {
        return businessKey;
    }

    public void setBusinessKey(String businessKey) {
        this.businessKey = businessKey;
    }

    public String getTaskType() {
        return taskType;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }

    public String getActivityNodeId() {
        return activityNodeId;
    }

    public void setActivityNodeId(String activityNodeId) {
        this.activityNodeId = activityNodeId;
    }

    public String getExecuteResult() {
        return executeResult;
    }

    public void setExecuteResult(String executeResult) {
        this.executeResult = executeResult;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public String getTaskParam() {
        return taskParam;
    }

    public void setTaskParam(String taskParam) {
        this.taskParam = taskParam;
    }

    public String getResultLog() {
        return resultLog;
    }

    public void setResultLog(String resultLog) {
        this.resultLog = resultLog;
    }
}