package com.cmpay.hacp.service.camunda.servicetask;

import cn.hutool.extra.spring.SpringUtil;
import com.cmpay.hacp.bo.HacpEmergencyNodeRecodeBO;
import com.cmpay.hacp.bo.task.HacpEmergencyTaskBO;
import com.cmpay.hacp.bo.process.ProcessExecuteLogEntity;
import com.cmpay.hacp.bo.task.MessageParamBO;
import com.cmpay.hacp.constant.EmergencyConstant;
import com.cmpay.hacp.service.EmergencyProcessService;
import com.cmpay.hacp.service.HacpCaseService;
import com.cmpay.hacp.service.HacpNodeRecodeService;
import com.cmpay.hacp.service.MessageSendService;
import com.cmpay.hacp.service.camunda.ServiceTaskTemplateDelegate;
import com.cmpay.hacp.service.SystemCacheService;
import com.cmpay.hacp.utils.JsonUtil;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.text.StringSubstitutor;
import org.camunda.bpm.engine.RuntimeService;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 消息通知
 * @date 2024/5/15 9:56
 */
@Service("doSendMessage")
@Slf4j
public class SendMessageService extends ServiceTaskTemplateDelegate {

    public SendMessageService(HacpCaseService caseService,
            HacpNodeRecodeService nodeRecodeService,
            RuntimeService runtimeService,
            SystemCacheService cacheService,
            EmergencyProcessService emergencyProcessService) {
        super(caseService, nodeRecodeService, runtimeService, cacheService, emergencyProcessService);
    }

    @Override
    protected void processTask() throws Exception{
        ProcessExecuteLogEntity processExecuteLogEntity = logEntity.get();
        HacpEmergencyTaskBO hacpEmergencyTaskBO = taskInfoHolder.get();
        String taskParam = hacpEmergencyTaskBO.getTaskParam();
        MessageParamBO messageParamBO = JsonUtil.strToObject(taskParam, MessageParamBO.class);
        MessageSendService messageSendService = SpringUtil.getBean(messageParamBO.getMessageType().getBeanName(), messageParamBO.getMessageType().getClazz());
        Map<String, String> builtInParam = getBuiltInParam(messageParamBO.getExtraParams());
        StringSubstitutor sub = new StringSubstitutor(builtInParam);
        messageParamBO.setMessageContent(sub.replace(messageParamBO.getMessageContent()));
        messageParamBO.setMessageTitle(sub.replace(Optional.ofNullable(messageParamBO.getMessageTitle()).orElse("发送"+ messageParamBO.getMessageType().getDesc())));
        processExecuteLogEntity.appendBuffer("开始发送【"+messageParamBO.getMessageType().getDesc()+"】通知");
        List<MessageParamBO.UserInfo> messageUserInfos = messageParamBO.getMessageUserInfos();
        for (MessageParamBO.UserInfo messageUserInfo : messageUserInfos) {
            processExecuteLogEntity.appendBuffer("【"+messageUserInfo.getUserName()+"】用户通知正在发送");
            messageParamBO.setMessageUserInfos(Collections.singletonList(messageUserInfo));
            traceEnabled = false;
            messageSendService.sendMessage(messageParamBO);
            processExecuteLogEntity.appendBuffer("【"+messageUserInfo.getUserName()+"】用户发送通知成功");
        }
        processExecuteLogEntity.appendBuffer("通知发送完毕！");
        logEntity.set(processExecuteLogEntity);
    }

    private Map<String, String> getBuiltInParam(Map<String, String> extraParam){
        Map<String,String> builtInParam = new HashMap<>(8);
        if(JudgeUtils.isNotEmpty(extraParam)){
            builtInParam.putAll(extraParam);
        }
        // 内置参数，预案名称,上个任务名称
        builtInParam.put(EmergencyConstant.PROCESS_INSTANCE_NAME, (String) execution.get().getVariable(EmergencyConstant.PROCESS_INSTANCE_NAME));
        List<HacpEmergencyNodeRecodeBO> allRecode = nodeRecodeService.getAllRecode(recodeInfo.get().getBusinessKey());
        if(JudgeUtils.isNotEmpty(allRecode)&& allRecode.size() > 1){
            builtInParam.put(EmergencyConstant.LAST_TASK_NAME,allRecode.get(allRecode.size() - 2).getTaskName());
        }
        return builtInParam;
    }
}
