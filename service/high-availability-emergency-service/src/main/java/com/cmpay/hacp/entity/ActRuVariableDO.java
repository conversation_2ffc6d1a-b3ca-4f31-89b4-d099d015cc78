/*
 * @ClassName ActRuVariableDO
 * @Description 
 * @version 1.0
 * @Date 2024-09-25 09:14:09
 */
package com.cmpay.hacp.entity;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;

@DataObject
public class ActRuVariableDO extends BaseDO {
    /**
     * @Fields id 
     */
    private String id;
    /**
     * @Fields rev 
     */
    private Integer rev;
    /**
     * @Fields type 
     */
    private String type;
    /**
     * @Fields name 
     */
    private String name;
    /**
     * @Fields executionId 
     */
    private String executionId;
    /**
     * @Fields procInstId 
     */
    private String procInstId;
    /**
     * @Fields procDefId 
     */
    private String procDefId;
    /**
     * @Fields caseExecutionId 
     */
    private String caseExecutionId;
    /**
     * @Fields caseInstId 
     */
    private String caseInstId;
    /**
     * @Fields taskId 
     */
    private String taskId;
    /**
     * @Fields batchId 
     */
    private String batchId;
    /**
     * @Fields bytearrayId 
     */
    private String bytearrayId;
    /**
     * @Fields double 
     */
    private Double double_;
    /**
     * @Fields long 
     */
    private Long long_;
    /**
     * @Fields text 
     */
    private String text;
    /**
     * @Fields text2 
     */
    private String text2;
    /**
     * @Fields varScope 
     */
    private String varScope;
    /**
     * @Fields sequenceCounter 
     */
    private Long sequenceCounter;
    /**
     * @Fields isConcurrentLocal 
     */
    private Byte isConcurrentLocal;
    /**
     * @Fields tenantId 
     */
    private String tenantId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getRev() {
        return rev;
    }

    public void setRev(Integer rev) {
        this.rev = rev;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getExecutionId() {
        return executionId;
    }

    public void setExecutionId(String executionId) {
        this.executionId = executionId;
    }

    public String getProcInstId() {
        return procInstId;
    }

    public void setProcInstId(String procInstId) {
        this.procInstId = procInstId;
    }

    public String getProcDefId() {
        return procDefId;
    }

    public void setProcDefId(String procDefId) {
        this.procDefId = procDefId;
    }

    public String getCaseExecutionId() {
        return caseExecutionId;
    }

    public void setCaseExecutionId(String caseExecutionId) {
        this.caseExecutionId = caseExecutionId;
    }

    public String getCaseInstId() {
        return caseInstId;
    }

    public void setCaseInstId(String caseInstId) {
        this.caseInstId = caseInstId;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public String getBytearrayId() {
        return bytearrayId;
    }

    public void setBytearrayId(String bytearrayId) {
        this.bytearrayId = bytearrayId;
    }

    public Double getDouble_() {
        return double_;
    }

    public void setDouble_(Double double_) {
        this.double_ = double_;
    }

    public Long getLong_() {
        return long_;
    }

    public void setLong_(Long long_) {
        this.long_ = long_;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getText2() {
        return text2;
    }

    public void setText2(String text2) {
        this.text2 = text2;
    }

    public String getVarScope() {
        return varScope;
    }

    public void setVarScope(String varScope) {
        this.varScope = varScope;
    }

    public Long getSequenceCounter() {
        return sequenceCounter;
    }

    public void setSequenceCounter(Long sequenceCounter) {
        this.sequenceCounter = sequenceCounter;
    }

    public Byte getIsConcurrentLocal() {
        return isConcurrentLocal;
    }

    public void setIsConcurrentLocal(Byte isConcurrentLocal) {
        this.isConcurrentLocal = isConcurrentLocal;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }
}