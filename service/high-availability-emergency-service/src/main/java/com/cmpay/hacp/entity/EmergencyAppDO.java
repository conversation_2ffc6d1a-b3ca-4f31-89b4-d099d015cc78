/*
 * @ClassName EmergencyAppDO
 * @Description 
 * @version 1.0
 * @Date 2024-08-30 14:19:16
 */
package com.cmpay.hacp.entity;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;
import java.time.LocalDateTime;

@DataObject
public class EmergencyAppDO extends BaseDO {
    /**
     * @Fields hostAppId 
     */
    private Integer hostAppId;
    /**
     * @Fields workspaceId 项目空间ID
     */
    private String workspaceId;
    /**
     * @Fields hostApp 
     */
    private String hostApp;
    /**
     * @Fields hostAppName 
     */
    private String hostAppName;
    /**
     * @Fields status 0:禁用  1:启用
     */
    private Byte status;
    /**
     * @Fields operatorId 操作员
     */
    private String operatorId;
    /**
     * @Fields operatorName 操作员名称
     */
    private String operatorName;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields updateTime 修改时间
     */
    private LocalDateTime updateTime;

    public Integer getHostAppId() {
        return hostAppId;
    }

    public void setHostAppId(Integer hostAppId) {
        this.hostAppId = hostAppId;
    }

    public String getWorkspaceId() {
        return workspaceId;
    }

    public void setWorkspaceId(String workspaceId) {
        this.workspaceId = workspaceId;
    }

    public String getHostApp() {
        return hostApp;
    }

    public void setHostApp(String hostApp) {
        this.hostApp = hostApp;
    }

    public String getHostAppName() {
        return hostAppName;
    }

    public void setHostAppName(String hostAppName) {
        this.hostAppName = hostAppName;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
}