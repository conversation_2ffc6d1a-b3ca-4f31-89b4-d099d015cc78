package com.cmpay.hacp.service.camunda.Impl;

import cn.hutool.core.lang.UUID;
import com.cmpay.hacp.dao.*;
import com.cmpay.hacp.entity.*;
import com.cmpay.hacp.service.camunda.ProcessCustomSqlService;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/5/15 9:56
 */
@RequiredArgsConstructor
@Service
public class ProcessCustomSqlServiceImpl implements ProcessCustomSqlService {

    private final IActRuExecutionDao ruExecutionDao;
    private final IActRuTaskExtDao ruTaskDao;
    private final IActRuVariableDao variableDao;
    private final IActHiProinstDao hiProinstDao;
    private final IActHiVarinstDao hiVarinstDao;

    @Override
    public List<ActRuExecutionDO> getRuExecutions(String businessKey) {
        ActRuExecutionDO actRuExecutionDO = new ActRuExecutionDO();
        actRuExecutionDO.setBusinessKey(businessKey);
        //actRuExecutionDO.setTenantId(tenantId);
        //actRuExecutionDO.setIsActive(Byte.parseByte("1"));
        return ruExecutionDao.find(actRuExecutionDO);
    }

    @Override
    public List<ActRuTaskDO> getRuTasks(String processInstanceId, String actId, String tenantId) {
        ActRuTaskDO actRuTaskDO = new ActRuTaskDO();
        actRuTaskDO.setExecutionId(processInstanceId);
        actRuTaskDO.setTaskDefKey(actId);
        actRuTaskDO.setTenantId(tenantId);
        return ruTaskDao.find(actRuTaskDO);
    }

    @Override
    public void updateUserTaskOperation(ActRuTaskDO actRuTaskDO) {
        ruTaskDao.updateUserTaskOperation(actRuTaskDO);
    }

    @Override
    public String addRuVariableNoRollback(String executionId, String name, String value, String tenantId, String processDefId) {
        ActRuVariableDO actRuVariableDO = new ActRuVariableDO();
        actRuVariableDO.setExecutionId(executionId);
        actRuVariableDO.setName(name);
        List<ActRuVariableDO> variableDOS = variableDao.find(actRuVariableDO);
        if (JudgeUtils.isNotEmpty(variableDOS)) {
            if (JudgeUtils.equals(variableDOS.get(0).getText(), value)) {
                return variableDOS.get(0).getId();
            }
            actRuVariableDO.setText(value);
            actRuVariableDO.setId(variableDOS.get(0).getId());
            variableDao.update(actRuVariableDO);
            return variableDOS.get(0).getId();
        }
        actRuVariableDO.setExecutionId(executionId);
        actRuVariableDO.setId(UUID.fastUUID().toString());
        actRuVariableDO.setVarScope(executionId);
        actRuVariableDO.setName(name);
        actRuVariableDO.setText(value);
        actRuVariableDO.setSequenceCounter(1L);
        actRuVariableDO.setProcInstId(executionId);
        actRuVariableDO.setRev(1);
        actRuVariableDO.setType("string");
        actRuVariableDO.setIsConcurrentLocal(Byte.valueOf("0"));
        actRuVariableDO.setTenantId(tenantId);
        actRuVariableDO.setProcDefId(processDefId);
        variableDao.insert(actRuVariableDO);
        return actRuVariableDO.getId();
    }

    @Override
    public void updateHiProinstStartTime(String id, Date startDate) {
        ActHiProinstDO actHiProinstDO = new ActHiProinstDO();
        actHiProinstDO.setId(id);
        ZoneId defaultZoneId = ZoneId.systemDefault();
        LocalDateTime dateTime = LocalDateTime.ofInstant(startDate.toInstant(), defaultZoneId);
        actHiProinstDO.setStartTime(dateTime);
        hiProinstDao.update(actHiProinstDO);
    }

    @Override
    public void updateHiProinstUserId(String id, String userId) {
        ActHiProinstDO actHiProinstDO = new ActHiProinstDO();
        actHiProinstDO.setId(id);
        actHiProinstDO.setStartUserId(userId);
        hiProinstDao.update(actHiProinstDO);
    }

    @Override
    public Set<String> queryHiVarinstTagId(String tagId){
        List<ActHiVarinstDO> varinstDOS = hiVarinstDao.queryByTagId(tagId);
        if (JudgeUtils.isEmpty(varinstDOS)) {
            return null;
        }
        return varinstDOS.stream().map(ActHiVarinstDO::getProcInstId).collect(Collectors.toSet());
    }

}
