package com.cmpay.hacp.utils;

import cn.hutool.core.util.URLUtil;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * @description:
 * <AUTHOR>
 * @date 2024/8/29 17:25
 * @version 1.0
 */
public class URLBuilder {
    private final StringBuilder builder;

    @Getter
    private Map<String, String> params = new HashMap<>();

    private String workspaceId;

    public URLBuilder(String baseUrl) {
        this.builder = new StringBuilder(baseUrl);
    }

    public void addParam(String key, String value) {
        builder.append("&").append(key).append("=").append(URLUtil.encode(value));
        params.put(key, value);
    }

    public String build() {
        if (builder.length() > 0 && builder.charAt(0) == '&') {
            builder.deleteCharAt(0);
        }
        return builder.toString();
    }

    public String getWorkspaceId() {
        return workspaceId;
    }

    public void setWorkspaceId(String workspaceId) {
        this.workspaceId = workspaceId;
    }
}
