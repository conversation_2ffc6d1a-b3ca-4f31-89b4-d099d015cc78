package com.cmpay.hacp.service.Impl;

import com.cmpay.hacp.bo.EmergencyEntityTagBO;
import com.cmpay.hacp.bo.EmergencyTagBO;
import com.cmpay.hacp.dao.IEmergencyTagExtDao;
import com.cmpay.hacp.entity.EmergencyTagDO;
import com.cmpay.hacp.enums.EntityTypeEnum;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.service.EmergencyEntityTagService;
import com.cmpay.hacp.service.EmergencyTagService;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.page.PageInfo;
import com.cmpay.lemon.framework.utils.PageUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024/08/23 9:54
 * @since 1.0.0
 */

@Service
@RequiredArgsConstructor
public class EmergencyTagServiceImpl implements EmergencyTagService {
    private final IEmergencyTagExtDao emergencyTagDao;

    private final EmergencyEntityTagService emergencyEntityTagService;

    @Override
    public EmergencyTagBO add(EmergencyTagBO bo) {
        EmergencyTagDO entity = new EmergencyTagDO();
        entity.setWorkspaceId(bo.getWorkspaceId());
        entity.setTag(bo.getTag());
        entity.setEntityType(bo.getEntityType());
        List<EmergencyTagDO> emergencyTagDOS = emergencyTagDao.find(entity);
        if (JudgeUtils.isNotEmpty(emergencyTagDOS)) {
            BusinessException.throwBusinessException(MsgEnum.DB_THE_SAME_DATA_EXISTS);
        }
        entity.setOperatorId(bo.getOperatorId());
        entity.setOperatorName(bo.getOperatorName());
        emergencyTagDao.insert(bo);
        return bo;
    }

    @Override
    public void update(EmergencyTagBO bo) {
        bo.setUpdateTime(LocalDateTime.now());
        bo.setOperatorId(bo.getOperatorId());
        bo.setOperatorName(bo.getOperatorName());
        emergencyTagDao.update(bo);
    }

    @Override
    public void delete(EmergencyTagBO bo) {
        EmergencyEntityTagBO query = new EmergencyEntityTagBO();
        query.setWorkspaceId(bo.getWorkspaceId());
        query.setTagId(bo.getTagId());
        List<EmergencyEntityTagBO> list=new ArrayList<>();
        bo.getEntityType().getEntityType().forEach(f->{
            query.setEntityType(EntityTypeEnum.valueOf(f));
            list.addAll(emergencyEntityTagService.getList(query));
        });
        if (JudgeUtils.isNotEmpty(list)) {
            BusinessException.throwBusinessException(MsgEnum.DATA_IS_BEING_USE);
        }
        emergencyTagDao.deleteExt(bo);
    }

    @Override
    public EmergencyTagBO getDetailInfo(EmergencyTagBO bo) {
        EmergencyTagDO detailInfo = emergencyTagDao.getDetailInfo(bo);
        if (JudgeUtils.isNull(detailInfo)) {
            BusinessException.throwBusinessException(MsgEnum.DB_SELECT_FAILED);
        }
        return BeanConvertUtil.convert(detailInfo, EmergencyTagBO.class);
    }

    @Override
    public PageInfo<EmergencyTagBO> getPage(int pageNum, int pageSize, EmergencyTagBO bo) {
        return PageUtils.pageQueryWithCount(pageNum,
                pageSize,
                () -> BeanConvertUtil.convertList(emergencyTagDao.find(bo), EmergencyTagBO.class));
    }

    @Override
    public List<EmergencyTagBO> getList(EmergencyTagBO bo) {
        EmergencyTagDO query = BeanConvertUtil.convert(bo, EmergencyTagDO.class);
        List<EmergencyTagDO> data = emergencyTagDao.find(query);
        if (JudgeUtils.isEmpty(data)) {
            return new ArrayList<>();
        }
        return BeanConvertUtil.convertList(data, EmergencyTagBO.class);
    }

    @Override
    public Map<String, Integer> getMap(EmergencyTagBO bo) {
        List<EmergencyTagBO> list = this.getList(bo);
        return list.stream()
                .collect(Collectors.toMap(EmergencyTagBO::getTag, EmergencyTagBO::getTagId));
    }

    @Override
    public List<EmergencyTagBO> getTagInfoByIds(List<Integer> tagIds) {
        return BeanConvertUtil.convertList(emergencyTagDao.getTagInfoByIds(tagIds),EmergencyTagBO.class);
    }
}
