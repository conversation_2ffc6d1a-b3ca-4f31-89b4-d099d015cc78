package com.cmpay.hacp.service.camunda.listener;

import com.cmpay.hacp.bo.HacpEmergencyNodeRecodeBO;
import com.cmpay.hacp.constant.CommonConstant;
import com.cmpay.hacp.constant.EmergencyConstant;
import com.cmpay.hacp.enums.TaskLogStatusEnum;
import com.cmpay.hacp.service.EmergencyProcessService;
import com.cmpay.hacp.service.HacpNodeRecodeService;
import com.cmpay.hacp.service.MessageNoticeService;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.delegate.DelegateTask;
import org.camunda.bpm.engine.delegate.TaskListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
public class UserTaskCompleteListener implements TaskListener {
    @Autowired
    private HacpNodeRecodeService hacpNodeRecodeService;

    @Autowired
    private MessageNoticeService messageNoticeService;
    @Autowired
    private EmergencyProcessService emergencyProcessService;
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = BusinessException.class)
    public void notify(DelegateTask delegateTask) {
        HacpEmergencyNodeRecodeBO lastRecode = hacpNodeRecodeService.getLastRecode(delegateTask.getExecution()
                .getBusinessKey(), delegateTask.getExecution().getCurrentActivityId());
        if(JudgeUtils.isNotNull(lastRecode)&&lastRecode.getExecuteResult().equals(TaskLogStatusEnum.COMPLETE.getCode())){
            return;
        }
        long startTime = Long.parseLong(delegateTask.getVariable(EmergencyConstant.INITIATE_DURATION).toString());
        String resultLog = delegateTask.getExecution()
                .getProcessEngineServices()
                .getTaskService()
                .getTaskComments(delegateTask.getId())
                .get(0)
                .getFullMessage();
        hacpNodeRecodeService.updateStatus(lastRecode,resultLog,(int) (System.currentTimeMillis() - startTime),TaskLogStatusEnum.COMPLETE);

        String extraId = delegateTask.getExecution().getBusinessKey() + CommonConstant.COLON + delegateTask.getId();
        messageNoticeService.readMessageExtraId(extraId,delegateTask.getAssignee(),delegateTask.getTenantId());

        emergencyProcessService.updateAuditUserId(delegateTask.getExecution().getBusinessKey(),delegateTask.getTenantId(),"");
    }
}
