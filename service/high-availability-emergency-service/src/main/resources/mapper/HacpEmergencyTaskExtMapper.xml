<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dao.IHacpEmergencyTaskExtDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.entity.HacpEmergencyTaskDO" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="tenant_id" property="tenantId" jdbcType="VARCHAR" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="task_name" property="taskName" jdbcType="VARCHAR" />
        <result column="task_type" property="taskType" jdbcType="VARCHAR" />
        <result column="task_describe" property="taskDescribe" jdbcType="VARCHAR" />
        <result column="task_operator" property="taskOperator" jdbcType="VARCHAR" />
        <result column="operator" property="operator" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="status" property="status" jdbcType="VARCHAR" />
        <result column="tm_smp" property="tmSmp" jdbcType="VARCHAR" />
    </resultMap>

    <resultMap id="BaseResultExtMap" extends="BaseResultMap" type="com.cmpay.hacp.bo.task.HacpEmergencyTaskBO"  >
        <result column="tag_name" property="tagName" jdbcType="VARCHAR" />
    </resultMap>

    <resultMap id="ResultMapWithBLOBs" type="com.cmpay.hacp.entity.HacpEmergencyTaskDO" extends="BaseResultMap" >
        <result column="task_param" property="taskParam" jdbcType="LONGVARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, tenant_id, hacp_emergency_task.workspace_id, task_name, task_type, task_describe, hacp_emergency_task.task_operator,
        hacp_emergency_task.operator, hacp_emergency_task.operator_name, hacp_emergency_task.create_time, hacp_emergency_task.update_time,
        hacp_emergency_task.status
    </sql>

    <sql id="Blob_Column_List" >
        task_param
    </sql>

    <select id="getUserIdMapByIds" resultMap="ResultMapWithBLOBs">
        select id
        ,
        <include refid="Blob_Column_List" />
        from hacp_emergency_task
        where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="findExt" resultMap="BaseResultExtMap" parameterType="com.cmpay.hacp.bo.task.HacpEmergencyTaskBO">
        select
            <include refid="Base_Column_List" />
        ,GROUP_CONCAT(tag_name SEPARATOR ',') AS tag_name
        from hacp_emergency_task left join
        (select * from emergency_entity_tag where
            entity_type = #{entityType,jdbcType=VARCHAR}
        )
        ht on hacp_emergency_task.id = ht.entity_id
        left join emergency_tag t on t.tag_id = ht.tag_id
        <where >
            <if test="id != null" >
                and id = #{id,jdbcType=BIGINT}
            </if>
            <if test="tenantId != null" >
                and tenant_id = #{tenantId,jdbcType=VARCHAR}
            </if>
            <if test="workspaceId != null" >
                and hacp_emergency_task.workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="tagId != null">
                and ht.tag_id = #{tagId,jdbcType=INTEGER}
            </if>
            <if test="taskName != null" >
                and task_name like concat('%',#{taskName,jdbcType=VARCHAR},'%')
            </if>
            <if test="taskType != null" >
                and task_type = #{taskType,jdbcType=VARCHAR}
            </if>
            <if test="taskDescribe != null" >
                and task_describe = #{taskDescribe,jdbcType=VARCHAR}
            </if>
            <if test="taskOperator != null" >
                and task_operator = #{taskOperator,jdbcType=VARCHAR}
            </if>
            <if test="status != null" >
                and hacp_emergency_task.status = #{status,jdbcType=VARCHAR}
            </if>
            <if test="taskParam != null" >
                and task_param = #{taskParam,jdbcType=LONGVARCHAR}
            </if>
        </where>
        group by hacp_emergency_task.id
        order by hacp_emergency_task.update_time desc
    </select>
</mapper>