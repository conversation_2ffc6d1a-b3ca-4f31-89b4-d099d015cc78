<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dao.IEmergencyHostExtDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.entity.EmergencyHostDO" >
        <id column="host_id" property="hostId" jdbcType="BIGINT" />
        <result column="host_desc" property="hostDesc" jdbcType="VARCHAR" />
        <result column="host_address" property="hostAddress" jdbcType="VARCHAR" />
        <result column="host_port" property="hostPort" jdbcType="INTEGER" />
        <result column="host_username" property="hostUsername" jdbcType="VARCHAR" />
        <result column="host_password" property="hostPassword" jdbcType="VARCHAR" />
        <result column="secret_key" property="secretKey" jdbcType="VARCHAR" />
        <result column="host_app_id" property="hostAppId" jdbcType="INTEGER" />
        <result column="host_os" property="hostOS" jdbcType="VARCHAR" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <resultMap id="BaseResultExtMap" type="com.cmpay.hacp.bo.EmergencyHostBO" extends="BaseResultMap">
        <result column="host_tag" property="hostTag" jdbcType="VARCHAR" />
        <result column="host_app" property="hostApp" jdbcType="VARCHAR" />
    </resultMap>


    <sql id="Base_Column_List" >
        emergency_host.host_id, host_desc, host_address, host_port, host_username,
        secret_key, host_os,emergency_host.host_app_id, emergency_host.workspace_id, emergency_host.operator_id,
        emergency_host.operator_name, emergency_host.create_time, emergency_host.update_time
    </sql>

    <sql id="Password_List" >
        host_password
    </sql>

    <update id="updateExt" parameterType="com.cmpay.hacp.entity.EmergencyHostDO" >
        update emergency_host
        <set >
            <if test="hostDesc != null" >
                host_desc = #{hostDesc,jdbcType=VARCHAR},
            </if>
            <if test="hostAddress != null" >
                host_address = #{hostAddress,jdbcType=VARCHAR},
            </if>
            <if test="hostPort != null" >
                host_port = #{hostPort,jdbcType=INTEGER},
            </if>
            <if test="hostUsername != null" >
                host_username = #{hostUsername,jdbcType=VARCHAR},
            </if>
            <if test="hostPassword != null" >
                host_password = #{hostPassword,jdbcType=VARCHAR},
            </if>
            <if test="secretKey != null" >
                secret_key = #{secretKey,jdbcType=VARCHAR},
            </if>
                host_app_id = #{hostAppId,jdbcType=INTEGER},
            <if test="hostOS != null" >
                host_os = #{hostOS,jdbcType=VARCHAR},
            </if>
            <if test="operatorId != null" >
                operator_id = #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                operator_name = #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where host_id = #{hostId,jdbcType=BIGINT}
        and workspace_id = #{workspaceId,jdbcType=VARCHAR}
    </update>

    <select id="getDetailInfo" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.entity.EmergencyHostDO" >
        select
        <include refid="Base_Column_List" />
            ,
        <include refid="Password_List" />
        from emergency_host
        where host_id = #{hostId,jdbcType=BIGINT}
        and workspace_id = #{workspaceId,jdbcType=VARCHAR}
    </select>

    <select id="findExt" resultMap="BaseResultExtMap" parameterType="com.cmpay.hacp.bo.EmergencyHostBO" >
        select
        <include refid="Base_Column_List" />
        ,GROUP_CONCAT(tag_name SEPARATOR ',') AS host_tag, a.host_app as host_app
        from emergency_host left join
		(select * from
		emergency_entity_tag
        where entity_type = #{entityType,jdbcType=VARCHAR}
		)ht on emergency_host.host_id = ht.entity_id
        left join emergency_tag t on t.tag_id = ht.tag_id
        left join emergency_app a on a.host_app_id = emergency_host.host_app_id
        <where >
            <if test="hostId != null" >
                and emergency_host.host_id = #{hostId,jdbcType=INTEGER}
            </if>
            <if test="hostTagId != null" >
                and ht.tag_id = #{hostTagId,jdbcType=INTEGER}
            </if>
            <if test="hostAppId != null" >
                and emergency_host.host_app_id = #{hostAppId,jdbcType=VARCHAR}
            </if>
            <if test="hostDesc != null" >
                and host_desc = #{hostDesc,jdbcType=VARCHAR}
            </if>
            <if test="hostUsername != null" >
                and host_username = #{hostUsername,jdbcType=VARCHAR}
            </if>
            <if test="hostPort != null" >
                and host_port = #{hostPort,jdbcType=INTEGER}
            </if>
            <if test="hostAddress != null and hostAddress != ''" >
                and host_address like  concat('%',#{hostAddress,jdbcType=VARCHAR},'%')
            </if>
            <if test="workspaceId != null" >
                and emergency_host.workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="hostOS != null" >
                and host_os = #{hostOS,jdbcType=VARCHAR}
            </if>
		    <if test="hostAppIds != null and hostAppIds.size()>0">
                and emergency_host.host_app_id in
                <foreach collection="hostAppIds" open="(" close=")" item="id" separator=",">
		            #{id,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="hostTagIds != null and hostTagIds.size()>0">
                and ht.tag_id in
                <foreach collection="hostTagIds" open="(" close=")" item="id" separator=",">
                    #{id,jdbcType=INTEGER}
                </foreach>
            </if>
        </where>
        group by emergency_host.host_id
        order by emergency_host.update_time asc
    </select>
    <select id="findByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
            ,
        <include refid="Password_List" />
        from emergency_host
        where
            host_id in
            <foreach collection="ids"   open="(" close=")" item="id" separator=",">
                #{id,jdbcType=INTEGER}
            </foreach>
        and workspace_id = #{workspaceId,jdbcType=VARCHAR}
    </select>

    <select id="findByTagIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        ,
        <include refid="Password_List" />
        from emergency_host left join emergency_entity_tag ht on emergency_host.host_id = ht.entity_id
        left join emergency_tag t on t.tag_id = ht.tag_id
        where
        ht.tag_id in
        <foreach collection="ids" open="(" close=")" item="id" separator=",">
            #{id,jdbcType=BIGINT}
        </foreach>
        and ht.entity_type = #{entityType,jdbcType=VARCHAR}
        and emergency_host.workspace_id = #{workspaceId,jdbcType=VARCHAR}
    </select>

    <select id="findByAppIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        ,
        <include refid="Password_List" />
        from emergency_host
        where
        host_app_id in
        <foreach collection="ids" open="(" close=")" item="id" separator=",">
            #{id,jdbcType=INTEGER}
        </foreach>
        and workspace_id = #{workspaceId,jdbcType=VARCHAR}
    </select>

    <delete id="deleteExt" parameterType="com.cmpay.hacp.entity.EmergencyHostDO">
        delete from emergency_host
        where host_id = #{hostId,jdbcType=INTEGER}
          and workspace_id = #{workspaceId,jdbcType=VARCHAR}
    </delete>

</mapper>