<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dao.IActHiVarinstDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.entity.ActHiVarinstDO" >
        <id column="ID_" property="id" jdbcType="VARCHAR" />
        <result column="PROC_DEF_KEY_" property="procDefKey" jdbcType="VARCHAR" />
        <result column="PROC_DEF_ID_" property="procDefId" jdbcType="VARCHAR" />
        <result column="ROOT_PROC_INST_ID_" property="rootProcInstId" jdbcType="VARCHAR" />
        <result column="PROC_INST_ID_" property="procInstId" jdbcType="VARCHAR" />
        <result column="EXECUTION_ID_" property="executionId" jdbcType="VARCHAR" />
        <result column="ACT_INST_ID_" property="actInstId" jdbcType="VARCHAR" />
        <result column="CASE_DEF_KEY_" property="caseDefKey" jdbcType="VARCHAR" />
        <result column="CASE_DEF_ID_" property="caseDefId" jdbcType="VARCHAR" />
        <result column="CASE_INST_ID_" property="caseInstId" jdbcType="VARCHAR" />
        <result column="CASE_EXECUTION_ID_" property="caseExecutionId" jdbcType="VARCHAR" />
        <result column="TASK_ID_" property="taskId" jdbcType="VARCHAR" />
        <result column="NAME_" property="name" jdbcType="VARCHAR" />
        <result column="VAR_TYPE_" property="varType" jdbcType="VARCHAR" />
        <result column="CREATE_TIME_" property="createTime" jdbcType="TIMESTAMP" />
        <result column="REV_" property="rev" jdbcType="INTEGER" />
        <result column="BYTEARRAY_ID_" property="bytearrayId" jdbcType="VARCHAR" />
        <result column="DOUBLE_" property="double_" jdbcType="DOUBLE" />
        <result column="LONG_" property="long_" jdbcType="BIGINT" />
        <result column="TEXT_" property="text" jdbcType="VARCHAR" />
        <result column="TEXT2_" property="text2" jdbcType="VARCHAR" />
        <result column="TENANT_ID_" property="tenantId" jdbcType="VARCHAR" />
        <result column="STATE_" property="state" jdbcType="VARCHAR" />
        <result column="REMOVAL_TIME_" property="removalTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        ID_, PROC_DEF_KEY_, PROC_DEF_ID_, ROOT_PROC_INST_ID_, PROC_INST_ID_, EXECUTION_ID_,
        ACT_INST_ID_, CASE_DEF_KEY_, CASE_DEF_ID_, CASE_INST_ID_, CASE_EXECUTION_ID_, TASK_ID_,
        NAME_, VAR_TYPE_, CREATE_TIME_, REV_, BYTEARRAY_ID_, DOUBLE_, LONG_, TEXT_, TEXT2_,
        TENANT_ID_, STATE_, REMOVAL_TIME_
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Base_Column_List" />
        from act_hi_varinst
        where ID_ = #{id,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from act_hi_varinst
        where ID_ = #{id,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.entity.ActHiVarinstDO" >
        insert into act_hi_varinst
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                ID_,
            </if>
            <if test="procDefKey != null" >
                PROC_DEF_KEY_,
            </if>
            <if test="procDefId != null" >
                PROC_DEF_ID_,
            </if>
            <if test="rootProcInstId != null" >
                ROOT_PROC_INST_ID_,
            </if>
            <if test="procInstId != null" >
                PROC_INST_ID_,
            </if>
            <if test="executionId != null" >
                EXECUTION_ID_,
            </if>
            <if test="actInstId != null" >
                ACT_INST_ID_,
            </if>
            <if test="caseDefKey != null" >
                CASE_DEF_KEY_,
            </if>
            <if test="caseDefId != null" >
                CASE_DEF_ID_,
            </if>
            <if test="caseInstId != null" >
                CASE_INST_ID_,
            </if>
            <if test="caseExecutionId != null" >
                CASE_EXECUTION_ID_,
            </if>
            <if test="taskId != null" >
                TASK_ID_,
            </if>
            <if test="name != null" >
                NAME_,
            </if>
            <if test="varType != null" >
                VAR_TYPE_,
            </if>
            <if test="createTime != null" >
                CREATE_TIME_,
            </if>
            <if test="rev != null" >
                REV_,
            </if>
            <if test="bytearrayId != null" >
                BYTEARRAY_ID_,
            </if>
            <if test="double_ != null" >
                DOUBLE_,
            </if>
            <if test="long_ != null" >
                LONG_,
            </if>
            <if test="text != null" >
                TEXT_,
            </if>
            <if test="text2 != null" >
                TEXT2_,
            </if>
            <if test="tenantId != null" >
                TENANT_ID_,
            </if>
            <if test="state != null" >
                STATE_,
            </if>
            <if test="removalTime != null" >
                REMOVAL_TIME_,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="procDefKey != null" >
                #{procDefKey,jdbcType=VARCHAR},
            </if>
            <if test="procDefId != null" >
                #{procDefId,jdbcType=VARCHAR},
            </if>
            <if test="rootProcInstId != null" >
                #{rootProcInstId,jdbcType=VARCHAR},
            </if>
            <if test="procInstId != null" >
                #{procInstId,jdbcType=VARCHAR},
            </if>
            <if test="executionId != null" >
                #{executionId,jdbcType=VARCHAR},
            </if>
            <if test="actInstId != null" >
                #{actInstId,jdbcType=VARCHAR},
            </if>
            <if test="caseDefKey != null" >
                #{caseDefKey,jdbcType=VARCHAR},
            </if>
            <if test="caseDefId != null" >
                #{caseDefId,jdbcType=VARCHAR},
            </if>
            <if test="caseInstId != null" >
                #{caseInstId,jdbcType=VARCHAR},
            </if>
            <if test="caseExecutionId != null" >
                #{caseExecutionId,jdbcType=VARCHAR},
            </if>
            <if test="taskId != null" >
                #{taskId,jdbcType=VARCHAR},
            </if>
            <if test="name != null" >
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="varType != null" >
                #{varType,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="rev != null" >
                #{rev,jdbcType=INTEGER},
            </if>
            <if test="bytearrayId != null" >
                #{bytearrayId,jdbcType=VARCHAR},
            </if>
            <if test="double_ != null" >
                #{double_,jdbcType=DOUBLE},
            </if>
            <if test="long_ != null" >
                #{long_,jdbcType=BIGINT},
            </if>
            <if test="text != null" >
                #{text,jdbcType=VARCHAR},
            </if>
            <if test="text2 != null" >
                #{text2,jdbcType=VARCHAR},
            </if>
            <if test="tenantId != null" >
                #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="state != null" >
                #{state,jdbcType=VARCHAR},
            </if>
            <if test="removalTime != null" >
                #{removalTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.entity.ActHiVarinstDO" >
        update act_hi_varinst
        <set >
            <if test="procDefKey != null" >
                PROC_DEF_KEY_ = #{procDefKey,jdbcType=VARCHAR},
            </if>
            <if test="procDefId != null" >
                PROC_DEF_ID_ = #{procDefId,jdbcType=VARCHAR},
            </if>
            <if test="rootProcInstId != null" >
                ROOT_PROC_INST_ID_ = #{rootProcInstId,jdbcType=VARCHAR},
            </if>
            <if test="procInstId != null" >
                PROC_INST_ID_ = #{procInstId,jdbcType=VARCHAR},
            </if>
            <if test="executionId != null" >
                EXECUTION_ID_ = #{executionId,jdbcType=VARCHAR},
            </if>
            <if test="actInstId != null" >
                ACT_INST_ID_ = #{actInstId,jdbcType=VARCHAR},
            </if>
            <if test="caseDefKey != null" >
                CASE_DEF_KEY_ = #{caseDefKey,jdbcType=VARCHAR},
            </if>
            <if test="caseDefId != null" >
                CASE_DEF_ID_ = #{caseDefId,jdbcType=VARCHAR},
            </if>
            <if test="caseInstId != null" >
                CASE_INST_ID_ = #{caseInstId,jdbcType=VARCHAR},
            </if>
            <if test="caseExecutionId != null" >
                CASE_EXECUTION_ID_ = #{caseExecutionId,jdbcType=VARCHAR},
            </if>
            <if test="taskId != null" >
                TASK_ID_ = #{taskId,jdbcType=VARCHAR},
            </if>
            <if test="name != null" >
                NAME_ = #{name,jdbcType=VARCHAR},
            </if>
            <if test="varType != null" >
                VAR_TYPE_ = #{varType,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                CREATE_TIME_ = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="rev != null" >
                REV_ = #{rev,jdbcType=INTEGER},
            </if>
            <if test="bytearrayId != null" >
                BYTEARRAY_ID_ = #{bytearrayId,jdbcType=VARCHAR},
            </if>
            <if test="double_ != null" >
                DOUBLE_ = #{double_,jdbcType=DOUBLE},
            </if>
            <if test="long_ != null" >
                LONG_ = #{long_,jdbcType=BIGINT},
            </if>
            <if test="text != null" >
                TEXT_ = #{text,jdbcType=VARCHAR},
            </if>
            <if test="text2 != null" >
                TEXT2_ = #{text2,jdbcType=VARCHAR},
            </if>
            <if test="tenantId != null" >
                TENANT_ID_ = #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="state != null" >
                STATE_ = #{state,jdbcType=VARCHAR},
            </if>
            <if test="removalTime != null" >
                REMOVAL_TIME_ = #{removalTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where ID_ = #{id,jdbcType=VARCHAR}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.entity.ActHiVarinstDO" >
        select
        <include refid="Base_Column_List" />
        from act_hi_varinst
        <where >
            <if test="id != null" >
                and ID_ = #{id,jdbcType=VARCHAR}
            </if>
            <if test="procDefKey != null" >
                and PROC_DEF_KEY_ = #{procDefKey,jdbcType=VARCHAR}
            </if>
            <if test="procDefId != null" >
                and PROC_DEF_ID_ = #{procDefId,jdbcType=VARCHAR}
            </if>
            <if test="rootProcInstId != null" >
                and ROOT_PROC_INST_ID_ = #{rootProcInstId,jdbcType=VARCHAR}
            </if>
            <if test="procInstId != null" >
                and PROC_INST_ID_ = #{procInstId,jdbcType=VARCHAR}
            </if>
            <if test="executionId != null" >
                and EXECUTION_ID_ = #{executionId,jdbcType=VARCHAR}
            </if>
            <if test="actInstId != null" >
                and ACT_INST_ID_ = #{actInstId,jdbcType=VARCHAR}
            </if>
            <if test="caseDefKey != null" >
                and CASE_DEF_KEY_ = #{caseDefKey,jdbcType=VARCHAR}
            </if>
            <if test="caseDefId != null" >
                and CASE_DEF_ID_ = #{caseDefId,jdbcType=VARCHAR}
            </if>
            <if test="caseInstId != null" >
                and CASE_INST_ID_ = #{caseInstId,jdbcType=VARCHAR}
            </if>
            <if test="caseExecutionId != null" >
                and CASE_EXECUTION_ID_ = #{caseExecutionId,jdbcType=VARCHAR}
            </if>
            <if test="taskId != null" >
                and TASK_ID_ = #{taskId,jdbcType=VARCHAR}
            </if>
            <if test="name != null" >
                and NAME_ = #{name,jdbcType=VARCHAR}
            </if>
            <if test="varType != null" >
                and VAR_TYPE_ = #{varType,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null" >
                and CREATE_TIME_ = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="rev != null" >
                and REV_ = #{rev,jdbcType=INTEGER}
            </if>
            <if test="bytearrayId != null" >
                and BYTEARRAY_ID_ = #{bytearrayId,jdbcType=VARCHAR}
            </if>
            <if test="double_ != null" >
                and DOUBLE_ = #{double_,jdbcType=DOUBLE}
            </if>
            <if test="long_ != null" >
                and LONG_ = #{long_,jdbcType=BIGINT}
            </if>
            <if test="text != null" >
                and TEXT_ = #{text,jdbcType=VARCHAR}
            </if>
            <if test="text2 != null" >
                and TEXT2_ = #{text2,jdbcType=VARCHAR}
            </if>
            <if test="tenantId != null" >
                and TENANT_ID_ = #{tenantId,jdbcType=VARCHAR}
            </if>
            <if test="state != null" >
                and STATE_ = #{state,jdbcType=VARCHAR}
            </if>
            <if test="removalTime != null" >
                and REMOVAL_TIME_ = #{removalTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </select>
</mapper>