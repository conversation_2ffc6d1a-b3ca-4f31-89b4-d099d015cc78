<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dao.IEmergencyVariableExtDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.entity.EmergencyVariableDO" >
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="business_key" property="businessKey" jdbcType="VARCHAR" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="variable_name" property="variableName" jdbcType="VARCHAR" />
    </resultMap>

    <resultMap id="ResultMapWithBLOBs" type="com.cmpay.hacp.entity.EmergencyVariableDO" extends="BaseResultMap" >
        <result column="value_json" property="valueJson" jdbcType="LONGVARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, business_key, workspace_id, variable_name
    </sql>

    <sql id="Blob_Column_List" >
        value_json
    </sql>
    <insert id="insertBatch">
        insert into emergency_variable(business_key, workspace_id, variable_name, value_json)
        values
        <foreach collection="list" item="item" separator="," >
            (
            #{item.businessKey,jdbcType=VARCHAR},
            #{item.workspaceId,jdbcType=VARCHAR},
            #{item.variableName,jdbcType=VARCHAR},
            #{item.valueJson,jdbcType=LONGVARCHAR}
            )
        </foreach>
    </insert>

    <select id="getDetailInfo" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List" />
            ,
        <include refid="Blob_Column_List" />
        from emergency_variable
        where
             business_key = #{businessKey,jdbcType=VARCHAR}
            and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            and variable_name = #{variableName,jdbcType=VARCHAR}
    </select>

</mapper>