<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dao.IEmergencyEntityTagExtDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.entity.EmergencyEntityTagDO" >
        <id column="entity_id" property="entityId" jdbcType="BIGINT" />
        <id column="tag_id" property="tagId" jdbcType="INTEGER" />
        <id column="entity_type" property="entityType" jdbcType="VARCHAR" />
        <id column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="TINYINT" />
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        entity_id, tag_id, entity_type, workspace_id, status, operator_id, operator_name,
        create_time, update_time
    </sql>

    <delete id="deleteExt" parameterType="com.cmpay.hacp.entity.EmergencyEntityTagDO" >
        delete from emergency_entity_tag
        where entity_id = #{entityId,jdbcType=BIGINT}
          and workspace_id = #{workspaceId,jdbcType=VARCHAR}
        and entity_type = #{entityType,jdbcType=VARCHAR}
    </delete>

</mapper>