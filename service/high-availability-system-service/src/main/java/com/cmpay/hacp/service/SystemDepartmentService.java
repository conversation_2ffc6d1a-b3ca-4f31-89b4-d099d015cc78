package com.cmpay.hacp.service;


import com.cmpay.hacp.bo.system.DeptBO;
import com.cmpay.hacp.bo.system.UserBO;

import java.util.List;

/**
 * 部门管理
 *
 * <AUTHOR>
 */
public interface SystemDepartmentService {

    /**
     * 查询用户所在部门及下属部门返回树形结构
     *
     * @param operatorId
     * @return
     */
    List<DeptBO> getUserDepartments(String operatorId);

    /**
     * 查询所有部门，返回树形结构
     *
     * @param operatorId
     * @return
     */
    List<DeptBO> getAllDepartments(String operatorId);

    /**
     * 查询单个部门信息
     *
     * @param operatorId
     * @param deptId
     * @return
     */
    DeptBO getDepartmentInfo(String operatorId, String deptId);

    /**
     * 单个删除部门
     *
     * @param deptId
     */
    void delete(String deptId);

    /**
     * 批量删除部门
     *
     * @param deptIds
     */
    void deleteBatch(List<String> deptIds);

    /**
     * 新增部门
     *
     * @param operatorId
     * @param deptBO
     */
    void add(String operatorId, DeptBO deptBO);

    /**
     * 更新部门
     *
     * @param operatorId
     * @param deptBO
     */
    void update(String operatorId, DeptBO deptBO);

    /**
     * 查询用户所属部门及以下子部门Id集合
     *
     * @param userId
     * @return
     */
    List<String> getDeptIds(String userId);

    /**
     * 查询用户信息 避免循环依赖
     *
     * @param userId 用户ID
     * @return
     */
    UserBO getUserInfo(String userId);


    /**
     * 查询部门ID
     *
     * @param deptName 部门名称
     * @return 部门id
     * @description 通过部门名称去部门表（sys_dept）查找部门id,并缓存起来
     */
    String findDeptId(String deptName);

    /**
     * 查询部门id
     *
     * @return
     */
    List<String> getAllDeptIds();
}
