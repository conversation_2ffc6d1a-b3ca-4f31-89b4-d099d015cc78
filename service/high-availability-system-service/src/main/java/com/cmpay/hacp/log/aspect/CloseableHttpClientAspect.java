package com.cmpay.hacp.log.aspect;

import com.cmpay.hacp.log.bo.SysDynamicLogBO;
import com.cmpay.hacp.log.bo.SysStaticLogBO;
import com.cmpay.hacp.log.firefly.enums.AccessLogTypeEnums;
import com.cmpay.hacp.properties.HacpWebAdminLogProperties;
import com.cmpay.lemon.common.context.LemonContext;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.utils.LemonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.HttpUriRequest;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

import java.net.InetAddress;
import java.net.URI;

import static com.cmpay.hacp.constant.CommonConstant.DYNAMIC_ACCESS_LOG;
import static com.cmpay.hacp.constant.CommonConstant.STATIC_ACCESS_LOG;


/**
 * <AUTHOR>
 * @create 2024/03/15 11:28
 * @since 1.0.0
 */
@Slf4j
@Aspect
public class CloseableHttpClientAspect {

    private final String applicationName;

    private final String dataIt;

    private final HacpWebAdminLogProperties.DynamicAccess dynamicAccess;

    public CloseableHttpClientAspect(String applicationName, HacpWebAdminLogProperties hacpWebAdminLogProperties) {
        this.dynamicAccess = hacpWebAdminLogProperties.getDynamicAccess();
        this.applicationName = applicationName;
        this.dataIt = hacpWebAdminLogProperties.getDataIt();
    }


    /**
     *
     */
    @Pointcut(value = "execution (* org.apache.http.impl.client.CloseableHttpClient.execute(org.apache.http.client.methods.HttpUriRequest))")
    public void feignClientExecutePointcut() {

    }

    /**
     *
     */
    @Pointcut(value = "execution (* org.apache.http.impl.client.CloseableHttpClient.execute(org.apache.http.client.methods.HttpUriRequest,org.apache.http.protocol.HttpContext))")
    public void restTemplateClientExecutePointcut() {

    }

    @Around(value = "feignClientExecutePointcut()")
    public Object feignClientExecuteAround(ProceedingJoinPoint pjp) throws Throwable {
        SysDynamicLogBO sysDynamicLogBO = new SysDynamicLogBO();
        sysDynamicLogBO.setType(AccessLogTypeEnums.DYNAMIC_ACCESS_FEIGN_CLIENT.getCode());
        return doAround(sysDynamicLogBO, pjp);
    }

    @Around(value = "restTemplateClientExecutePointcut()")
    public Object restTemplateClientExecuteAround(ProceedingJoinPoint pjp) throws Throwable {
        SysDynamicLogBO sysDynamicLogBO = new SysDynamicLogBO();
        sysDynamicLogBO.setType(AccessLogTypeEnums.DYNAMIC_ACCESS_HTTP_CLIENT.getCode());
        return doAround(sysDynamicLogBO, pjp);
    }

    private Object doAround(SysDynamicLogBO sysDynamicLogBO, ProceedingJoinPoint pjp) throws Throwable {
        this.beforeProceed(sysDynamicLogBO, pjp.getArgs());
        return pjp.proceed();
    }


    private void beforeProceed(SysDynamicLogBO sysDynamicLogBO, Object[] objs) {
        try {
            sysDynamicLogBO.setOperatorTime(DateTimeUtils.getCurrentLocalDateTime());
            sysDynamicLogBO.setDataIt(this.dataIt);
            sysDynamicLogBO.setApplicationName(this.applicationName);
            SysStaticLogBO sysLog = this.getSysLog();
            if (JudgeUtils.isNotNull(sysLog)) {
                sysDynamicLogBO.setLogId(sysLog.getLogId());
                sysDynamicLogBO.setDataPath(sysLog.getRequestUri());
                sysDynamicLogBO.setOperator(sysLog.getUserName());
                sysDynamicLogBO.setOperatorIp(sysLog.getRemoteAddr());
            } else {
                sysDynamicLogBO.setLogId(LemonUtils.getRequestId());
                sysDynamicLogBO.setDataPath(dynamicAccess.getDefaultDataPath());
                sysDynamicLogBO.setOperator(dynamicAccess.getDefaultOperator());
                sysDynamicLogBO.setOperatorIp(InetAddress.getLocalHost().getHostAddress());
            }
            //流转路径，取入口请求url
            for (Object obj : objs) {
                if (obj instanceof HttpUriRequest) {
                    HttpUriRequest httpUriRequest = (HttpUriRequest) obj;
                    URI uri = httpUriRequest.getURI();
                    //执行对象，存接口参数
                    sysDynamicLogBO.setExecutionTarget(uri.getAuthority());
                    //接口记录,取运管调用接口地址
                    sysDynamicLogBO.setInterfaceRecord(uri.getPath());
                    sysDynamicLogBO.setOperationAction(httpUriRequest.getMethod());
                    break;
                }
            }
        } catch (Exception exception) {
            log.warn("beforeProceed Exception {}", exception.getMessage());
        }
        LemonContext.getCurrentContext().put(DYNAMIC_ACCESS_LOG, sysDynamicLogBO);
    }


    public SysStaticLogBO getSysLog() {
        return LemonContext.getCurrentContext().containsKey(STATIC_ACCESS_LOG) ? (SysStaticLogBO) LemonContext.getCurrentContext().get(STATIC_ACCESS_LOG) : null;
    }
}
