package com.cmpay.hacp.auth;

import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.lemon.framework.response.ResponseMessageResolver;
import com.cmpay.lemon.framework.security.HttpServletResponseStatusCode;
import org.springframework.security.web.session.SessionInformationExpiredEvent;
import org.springframework.security.web.session.SessionInformationExpiredStrategy;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 */
public class HacpWebAdminTerminalSessionInformationExpiredStrategy implements SessionInformationExpiredStrategy {
    private final ResponseMessageResolver responseMessageResolver;

    public HacpWebAdminTerminalSessionInformationExpiredStrategy(ResponseMessageResolver responseMessageResolver) {
        this.responseMessageResolver = responseMessageResolver;
    }

    @Override
    public void onExpiredSessionDetected(SessionInformationExpiredEvent event) throws IOException {
        HttpServletResponse response = event.getResponse();
        HttpServletRequest request = event.getRequest();
        response.setStatus(HttpServletResponseStatusCode.SC_EXPIRED_SESSION);
        this.responseMessageResolver.resolve(request, response, MsgEnum.FAILED_TO_CHECK_SESSIONTOKEN_CAUSE_EQUIPMENT);
    }
}
