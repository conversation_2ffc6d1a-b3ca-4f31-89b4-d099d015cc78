package com.cmpay.hacp.dao;

import com.cmpay.hacp.entity.MenuDO;
import com.cmpay.hacp.entity.RoleDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IRoleMenuExtDao extends IRoleMenuDao {
    /**
     * 根据角色删除关联菜单
     *
     * @param roleId
     * @return
     */
    int deleteByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据菜单删除关联角色
     *
     * @param menuId
     * @return
     */
    int deleteByMenuId(@Param("menuId") Long menuId);

    /**
     * 批量新增角色-菜单
     *
     * @param roleId
     * @param menuIds
     * @return
     */
    int batchInsertRoleMenus(@Param("roleId") Long roleId, @Param("menuIds") List<Long> menuIds);

    /**
     * 查询角色拥有的菜单
     *
     * @param roleId
     * @return
     */
    List<MenuDO> getMenusByRoleId(@Param("roleId") Long roleId);

    /**
     * 查询菜单所属的角色
     *
     * @param menuId
     * @return
     */
    List<RoleDO> getRolesByMenuId(@Param("menuId") Long menuId);
}