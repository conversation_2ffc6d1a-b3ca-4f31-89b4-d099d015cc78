package com.cmpay.hacp.utils;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import com.cmpay.hacp.bo.menu.MenuActionMetaBO;
import com.cmpay.hacp.bo.menu.MenuTreeBO;
import com.cmpay.hacp.bo.menu.MenuTreeMetaBO;
import com.cmpay.hacp.entity.MenuDO;
import com.cmpay.hacp.utils.reflection.Reflection;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/05/11 17:41
 * @since 1.0.0
 */
@Component
@Slf4j
public class MenuUtils {
    @Resource(name = "hacpObjectMapper")
    public ObjectMapper objectMapper;

    public List<MenuTreeBO> getTreeMenus(List<MenuDO> menuTrees) {
        //配置
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        treeNodeConfig.setIdKey("menuId");
        treeNodeConfig.setParentIdKey("parentId");
        treeNodeConfig.setWeightKey("orderNum");
        treeNodeConfig.setChildrenKey("children");
        //转换器
        List<Tree<Long>> treeDicts = TreeUtil.build(menuTrees, 0L, treeNodeConfig,
                (menu, tree) -> {
                    if (JudgeUtils.isNull(menu.getOrderNum())) {
                        menu.setOrderNum(0L);
                    }
                    // 扩展属性 ...
                    Reflection.reflectionTree(menu, tree);
                });
        List<MenuTreeBO> menuTreeBOS = new ArrayList<>();
        try {
            String treeJson = objectMapper.writeValueAsString(treeDicts);
            menuTreeBOS = objectMapper.readValue(treeJson, new TypeReference<List<MenuTreeBO>>() {
            });
        } catch (JsonProcessingException e) {
            log.warn("getTreeMenus JsonProcessingException {}", e.getMessage());
        }
        return menuTreeBOS;
    }


    public List<MenuTreeMetaBO> getMenuTreeMetas(List<MenuTreeMetaBO> menuTrees) {
        //配置
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        treeNodeConfig.setIdKey("menuId");
        treeNodeConfig.setParentIdKey("parentId");
        treeNodeConfig.setWeightKey("orderNum");
        treeNodeConfig.setChildrenKey("children");
        //转换器
        List<Tree<Long>> treeDicts = TreeUtil.build(menuTrees, 0L, treeNodeConfig,
                (menu, tree) -> {
                    if (JudgeUtils.isNull(menu.getOrderNum())) {
                        menu.setOrderNum(0L);
                    }
                    // 扩展属性 ...
                    Reflection.reflectionTree(menu, tree);
                });

        List<MenuTreeMetaBO> menuTreeRspDTOS = new ArrayList<>();
        try {
            String treeJson = objectMapper.writeValueAsString(treeDicts);
            menuTreeRspDTOS = objectMapper.readValue(treeJson, new TypeReference<List<MenuTreeMetaBO>>() {
            });
        } catch (JsonProcessingException e) {
            log.warn("getMenuTreeMetas JsonProcessingException {}", e.getMessage());
        }
        return menuTreeRspDTOS;
    }


    /**
     * 转换metaData
     *
     * @param menuDO
     * @return
     */
    public MenuTreeMetaBO getMenuTreeRspMetaDTO(MenuDO menuDO) {
        MenuTreeMetaBO menuTreeMetaBO = new MenuTreeMetaBO();
        BeanUtils.copy(menuTreeMetaBO, menuDO);
        if (JudgeUtils.isNotBlank(menuDO.getMeta())) {
            try {
                //兼容权限中心
                menuTreeMetaBO.setMeta(objectMapper.readValue(menuDO.getMeta(), HashMap.class));
            } catch (JsonProcessingException e) {
                log.warn("getMenuTreeRspMetaDTO JsonProcessingException {}", e.getMessage());
            }
        }
        return menuTreeMetaBO;
    }

    /**
     * 转换metaData
     *
     * @param menuDO
     * @return
     */
    public MenuActionMetaBO getMenuActionRspMetaDTO(MenuDO menuDO) {
        MenuActionMetaBO menuActionMetaBO = new MenuActionMetaBO();
        BeanUtils.copy(menuActionMetaBO, menuDO);
        if (JudgeUtils.isNotBlank(menuDO.getMeta())) {
            try {
                //兼容权限中心
                menuActionMetaBO.setMeta(objectMapper.readValue(menuDO.getMeta(), HashMap.class));
            } catch (JsonProcessingException e) {
                log.warn("getMenuActionRspMetaDTO JsonProcessingException {}", e.getMessage());
            }
        }
        return menuActionMetaBO;
    }

}
