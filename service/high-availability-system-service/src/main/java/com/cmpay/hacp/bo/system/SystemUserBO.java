package com.cmpay.hacp.bo.system;

import com.cmpay.hacp.annotation.mybatis.CryptField;
import com.cmpay.hacp.annotation.mybatis.CryptType;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * 系统用户
 */
@Data
public class SystemUserBO {

    /**
     * @Fields userId 用户编号
     */
    private String userId;

    /**
     * @Fields userName 用户名
     */
    private String userName;

    /**
     * @Fields fullName 姓名
     */
    @CryptField(type = CryptType.SM4)
    private String fullName;

    /**
     * @Fields status 用戶状态
     */
    private String status;

    /**
     * @Fields hasRole 拥有角色
     */
    private String hasRole;

    /**
     * @Fields modifyTime 修改时间
     */
    private LocalDateTime modifyTime;

    /**
     * 所属单位
     */
    private String affiliatedUnit;


    /**
     * 所属部门及团队
     */
    private String affiliatedDeptTeam;

    /**
     * 手机号码
     */
    @CryptField(type = CryptType.SM4)
    private String mobile;

    /**
     * 邮箱
     */
    @CryptField(type = CryptType.SM4)
    private String email;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 部门名称
     */
    private String deptName;
    /**
     * 导入失败原因
     */
    private String failureReason;
}
