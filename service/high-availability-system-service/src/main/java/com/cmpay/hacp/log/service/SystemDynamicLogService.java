package com.cmpay.hacp.log.service;


import com.cmpay.hacp.log.bo.SysDynamicLogBO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SystemDynamicLogService {

    /**
     * 设置响应报文大小
     *
     * @param dataSize     数据大小
     * @param dataSizeType 数据大小类型 可自定义 比如字节大小 bytes, 或者行数 line
     * @param requestId    当前日志ID 通过LemonUtils.getRequestId() 方法获得
     * @param api          当前调用的api地址 比如当前调用的接口是http://127.0.0.1:8080/v1/api/users 那么填写 /v1/api/users
     */
    void setDynamicRspDataSize(Long dataSize, String dataSizeType, String requestId, String api);

    /**
     * 登记动态日志
     *
     * @param sysDynamicLogBO
     */
    void addDynamicLog(SysDynamicLogBO sysDynamicLogBO);

    /**
     * 查询动态日志列表
     *
     * @param requestId
     * @return
     */
    List<SysDynamicLogBO> getDynamicListByRequestId(String requestId);
}
