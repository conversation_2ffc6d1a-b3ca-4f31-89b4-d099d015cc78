package com.cmpay.hacp.log.bo;

import lombok.Data;


/**
 * <AUTHOR>
 */
@Data
public class ExternalLogBO {
    /**
     * 操作执行时间
     */
    private String operateTime;
    /**
     * 资源操作用户会话ID
     */
    private String sessionId;
    /**
     * 主帐号登录会话ID
     */
    private String sessionIdPa;
    /**
     * 主帐号名
     */
    private String pracct;
    /**
     * 从帐号名
     */
    private String slaveAccount;
    /**
     * 客户端IP
     */
    private String srcIp;
    /**
     * 客户端MAC地址
     */
    private String srcMac;
    /**
     * 客户端主机名
     */
    private String srcHost;
    /**
     * 目的资源IP
     */
    private String dstIp;
    /**
     * 目的资源端口
     */
    private String dstPort;
    /**
     * 目的资源名称
     */
    private String dstName;
    /**
     * 目的资源ID
     */
    private String dstId;
    /**
     * 目的资源主机名
     */
    private String dstHost;
    /**
     * 操作类型
     */
    private String operateType;
    /**
     * 操作子类型
     */
    private String operateSubType;
    /**
     * 操作
     */
    private String operate;
    /**
     * 操作执行结果
     */
    private String result;
    /**
     * 操作的描述或者命令
     */
    private String descriptionCmd;
    /**
     * 操作失败原因
     */
    private String errMsg;
    /**
     * 被操作对象名称
     */
    private String optObj;
    /**
     * 被操作对象类型
     */
    private String optObjType;
    /**
     * 被操作对象2名称
     */
    private String optObj2;
    /**
     * 被操作对象2类型
     */
    private String optObj2Type;
    /**
     * 被操作对象3名称
     */
    private String optObj3;
    /**
     * 被操作对象3类型
     */
    private String optObj3Type;
    /**
     * 凭据
     */
    private String workOrder;
    /**
     * 凭据类型
     */
    private String workOrderType;
    /**
     * 票据
     */
    private String ticket;
    /**
     * 事件所属系统名称
     */
    private String fromSys;
    /**
     * 模块名称
     */
    private String module;
}
