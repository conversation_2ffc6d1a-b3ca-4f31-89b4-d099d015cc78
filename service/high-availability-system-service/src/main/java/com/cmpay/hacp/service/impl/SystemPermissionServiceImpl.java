package com.cmpay.hacp.service.impl;

import com.cmpay.hacp.bo.menu.MenuActionMetaBO;
import com.cmpay.hacp.bo.menu.MenuTreeMetaBO;
import com.cmpay.hacp.bo.menu.PermMenuTreeMetaBO;
import com.cmpay.hacp.constant.CommonConstant;
import com.cmpay.hacp.service.SystemMenuService;
import com.cmpay.hacp.service.SystemPermissionService;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.cache.jcache.JCacheCacheable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 系统权限服务实现
 *
 * <AUTHOR>
 * @create 2024/05/11 15:06:16
 * @since 1.0.0
 */
@Service
public class SystemPermissionServiceImpl implements SystemPermissionService {

    @Autowired
    private SystemMenuService systemMenuService;

    /**
     * 获取用户权限
     *
     * @param userId
     * @return
     */
    @Override
    @JCacheCacheable(cacheNames = "HACP_WEB_ADMIN_PERMISSION",
            key = "'PERMISSION:' + #applicationName+':'+#userId",
            unless = "#result == null")
    public List<String> getUserPermissions(String userId, String applicationName) {
        PermMenuTreeMetaBO permMenuTreeMetaBO = this.queryUserPermissions(userId, applicationName);
        List<MenuTreeMetaBO> menus = permMenuTreeMetaBO.getMenuTreeList();
        List<String> permissions = new ArrayList();
        if (JudgeUtils.isNotEmpty(menus)) {
            permissions = this.getPermissionsByMenu(menus, permissions);
        }
        List<MenuActionMetaBO> menuActions = permMenuTreeMetaBO.getMenuActionList();
        if (JudgeUtils.isNotEmpty(menuActions)) {
            permissions = this.getPermissionsByAction(menuActions, permissions);
        }
        return permissions;
    }

    /**
     * @param userMenus
     * @return
     */
    @Override
    public List<String> getPermissionsByMenu(List<MenuTreeMetaBO> userMenus, List<String> permissions) {
        for (MenuTreeMetaBO metaDTO : userMenus) {
            if (JudgeUtils.isNotBlank(metaDTO.getPerms())) {
                String[] perms = metaDTO.getPerms().split(",");
                for (int i = 0; i < perms.length; i++) {
                    permissions.add(perms[i]);
                }
            }
            if (JudgeUtils.isNotEmpty(metaDTO.getChildren())) {
                return this.getPermissionsByMenu(metaDTO.getChildren(), permissions);
            }
        }
        return permissions;
    }

    /**
     * 获取用户权限,菜单并缓存
     *
     * @param userId
     * @return
     */
    @Override
    @JCacheCacheable(cacheNames = "HACP_WEB_ADMIN_MENU", key = "'MENU:'+ #applicationName+':'+#userId", unless = "#result == null")
    public PermMenuTreeMetaBO queryUserPermissions(String userId, String applicationName) {
        return systemMenuService.getUserMenus(userId);
    }

    /**
     * @param userMenus
     * @return
     */
    @Override
    public List<String> getPermissionsByAction(List<MenuActionMetaBO> userMenus, List<String> permissions) {
        for (MenuActionMetaBO metaDTO : userMenus) {
            if (JudgeUtils.isNotBlank(metaDTO.getPerms())) {
                String[] perms = metaDTO.getPerms().split(CommonConstant.COMMA);
                permissions.addAll(Arrays.asList(perms));
            }
        }
        return permissions;
    }
}
