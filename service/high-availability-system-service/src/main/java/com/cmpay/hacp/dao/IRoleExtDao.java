
package com.cmpay.hacp.dao;

import com.cmpay.hacp.bo.system.RoleBO;
import com.cmpay.hacp.entity.RoleDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IRoleExtDao extends IRoleDao {

    /**
     * 查询角色信息
     *
     * @param roleDO
     * @param deptIds
     * @return
     */
    List<RoleBO> getRoles(@Param("roleDO") RoleDO roleDO, @Param("deptIds") List<String> deptIds);

    /**
     * 新增角色并且返回主键
     *
     * @param roleDO
     * @return
     */
    long insertKey(RoleDO roleDO);

}
