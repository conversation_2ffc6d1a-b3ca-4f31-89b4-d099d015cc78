/*
 * @ClassName DictDO
 * @Description
 * @version 1.0
 * @Date 2020-04-21 09:38:45
 */
package com.cmpay.hacp.log.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SysStaticLogBO {
    /**
     * @Fields id 编号
     */
    private String id;

    /**
     * @Fields logId 日志编号
     */
    private String logId;

    /**
     * @Fields type 日志类型
     */
    private String type;

    /**
     * @Fields title 日志标题
     */
    private String title;
    /**
     * @Fields userId 用户id
     */
    private String userId;
    /**
     * @Fields userName 用户名
     */
    private String userName;

    /**
     * @Fields msgCd 消息码
     */
    private String msgCd;

    /**
     * @Fields mobile 手机号码
     */
    private String mobile;

    /**
     * @Fields createBy 创建者
     */
    private String createBy;

    /**
     * @Fields createDate 创建时间
     */
    private LocalDateTime createDate;

    /**
     * @Fields remoteAddr 操作IP地址
     */
    private String remoteAddr;

    /**
     * @Fields userAgent 用户代理
     */
    private String userAgent;

    /**
     * @Fields requestUri 请求URI
     */
    private String requestUri;

    /**
     * @Fields applicationName 应用名称/ID
     */
    private String applicationName;
    /**
     * @Fields dataIt 数据所属对象
     */
    private String dataIt;

    /**
     * @Fields method 操作方式
     */
    private String method;

    /**
     * @Fields params 操作提交的数据
     */
    private String params;

    /**
     * @Fields exception 异常信息
     */
    private String exception;

    /**
     * @Fields beginDate 开始时间
     */
    private String beginDate;


    /**
     * @Fields endDate 结束时间
     */
    private String endDate;

    /**
     * @Fields rspDataSizeType 返回数据大小类型(line-行数、byte-大小)
     */
    private String rspDataSizeType;

    /**
     * @Fields rspDataSize  返回数据大小(line-行数、byte-大小)
     */
    private Long rspDataSize;

    /**
     * @Fields duration 耗时(毫秒-millisecond）
     */
    private Long duration;

    /**
     * @Fields endTime 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 是否是动态操作
     */
    @JsonProperty(value = "isDynamic")
    private Integer isDynamic;

    /**
     * 动态日志
     */
    private List<SysDynamicLogBO> dynamicLogs;
}
