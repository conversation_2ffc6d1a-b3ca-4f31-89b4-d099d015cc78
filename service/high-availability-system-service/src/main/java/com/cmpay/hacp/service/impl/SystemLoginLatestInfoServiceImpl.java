package com.cmpay.hacp.service.impl;

import com.cmpay.hacp.bo.system.LoginHistoryLogBO;
import com.cmpay.hacp.bo.system.LoginLatestInfoBO;
import com.cmpay.hacp.dao.ILoginHistoryLogExtDao;
import com.cmpay.hacp.dao.ILoginLatestInfoExtDao;
import com.cmpay.hacp.entity.LoginHistoryLogDO;
import com.cmpay.hacp.entity.LoginLatestInfoDO;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.service.SystemLoginLatestInfoService;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Service
public class SystemLoginLatestInfoServiceImpl implements SystemLoginLatestInfoService {

    @Autowired
    private ILoginLatestInfoExtDao loginLatestInfoExtDao;

    @Autowired
    private ILoginHistoryLogExtDao loginHistoryLogExtDao;

    @Override
    public void addLoginLatestInfo(LoginLatestInfoBO loginLatestInfoBO) {
        LoginLatestInfoDO loginLatestInfo = new LoginLatestInfoDO();
        BeanUtils.copyProperties(loginLatestInfo, loginLatestInfoBO);
        LoginLatestInfoDO latestInfo = this.getLoginLatestInfo(loginLatestInfo.getUserId());
        if (JudgeUtils.isNull(latestInfo)) {
            loginLatestInfo.setId(UUID.randomUUID().toString().replace("-", "").toUpperCase());
            loginLatestInfo.setFirstDate(loginLatestInfoBO.getLatestDate());
            loginLatestInfo.setFirstTime(loginLatestInfoBO.getLatestTime());
            loginLatestInfo.setLatestDate(loginLatestInfoBO.getLatestDate());
            loginLatestInfo.setLatestTime(loginLatestInfoBO.getLatestTime());
            loginLatestInfo.setIsUse((short) 0);
            if (loginLatestInfoExtDao.insert(loginLatestInfo) < 1) {
                BusinessException.throwBusinessException(MsgEnum.DB_INSERT_FAILED);
            }
        } else {
            latestInfo.setPid(loginLatestInfoBO.getPid());
            latestInfo.setLatestDate(loginLatestInfoBO.getLatestDate());
            latestInfo.setLatestTime(loginLatestInfoBO.getLatestTime());
            latestInfo.setIsUse((short) 0);
            if (loginLatestInfoExtDao.updateByUserId(latestInfo) < 1) {
                BusinessException.throwBusinessException(MsgEnum.DB_INSERT_FAILED);
            }
        }
    }

    @Override
    public List<LoginLatestInfoBO> queryLoginLastInfoByUserIds(List<String> userIds) {
        if (JudgeUtils.isEmpty(userIds)) {
            return new ArrayList<>();
        }
        List<LoginLatestInfoDO> latestInfoDOS = loginLatestInfoExtDao.findByUserIds(userIds);
        return BeanConvertUtil.convertList(latestInfoDOS, LoginLatestInfoBO.class);
    }

    @Override
    public LoginHistoryLogBO queryLoginLatestInfo(String userId) {
        LoginHistoryLogDO latestInfoDO = loginHistoryLogExtDao.queryLoginLatestInfo(userId);
        if (JudgeUtils.isNull(latestInfoDO)) {
            return null;
        }
        LoginHistoryLogBO latestInfo = new LoginHistoryLogBO();
        BeanUtils.copyProperties(latestInfo, latestInfoDO);
        return latestInfo;
    }

    /**
     * @param userId
     * @return
     */
    private LoginLatestInfoDO getLoginLatestInfo(String userId) {
        LoginLatestInfoDO loginLatestInfo = new LoginLatestInfoDO();
        loginLatestInfo.setUserId(userId);
        List<LoginLatestInfoDO> loginLatestInfos = loginLatestInfoExtDao.find(loginLatestInfo);
        if (JudgeUtils.isEmpty(loginLatestInfos)) {
            return null;
        }
        LoginLatestInfoDO latestInfoDO = loginLatestInfos.get(0);
        return latestInfoDO;
    }
}
