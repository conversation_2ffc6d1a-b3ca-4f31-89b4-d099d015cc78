package com.cmpay.hacp.service;


import com.cmpay.hacp.bo.menu.MenuActionMetaBO;
import com.cmpay.hacp.bo.menu.MenuTreeMetaBO;
import com.cmpay.hacp.bo.menu.PermMenuTreeMetaBO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SystemPermissionService {

    /**
     * 获取用户权限
     *
     * @param userId
     * @param applicationName
     * @return
     */
    List<String> getUserPermissions(String userId, String applicationName);

    /**
     * 获取用户菜单权限并缓存
     *
     * @param userId
     * @param applicationName
     * @return
     */
    PermMenuTreeMetaBO queryUserPermissions(String userId, String applicationName);

    /**
     * 获取权限标识从菜单中
     *
     * @param userMenus
     * @param permissions
     * @return
     */
    List<String> getPermissionsByMenu(List<MenuTreeMetaBO> userMenus, List<String> permissions);

    /**
     * 获取权限标识从按钮中
     *
     * @param userMenus
     * @param permissions
     * @return
     */
    List<String> getPermissionsByAction(List<MenuActionMetaBO> userMenus, List<String> permissions);
}
