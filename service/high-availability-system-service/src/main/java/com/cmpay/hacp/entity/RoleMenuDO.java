/*
 * @ClassName RoleMenuDO
 * @Description
 * @version 1.0
 * @Date 2021-09-02 15:24:38
 */
package com.cmpay.hacp.entity;

import com.cmpay.lemon.framework.annotation.DataObject;

@DataObject
public class RoleMenuDO extends BaseDO {
    /**
     * @Fields id
     */
    private String id;
    /**
     * @Fields roleId 角色id
     */
    private Long roleId;
    /**
     * @Fields menuId 菜单id
     */
    private Long menuId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    public Long getMenuId() {
        return menuId;
    }

    public void setMenuId(Long menuId) {
        this.menuId = menuId;
    }
}