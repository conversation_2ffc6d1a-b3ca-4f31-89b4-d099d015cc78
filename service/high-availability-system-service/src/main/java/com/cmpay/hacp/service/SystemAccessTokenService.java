package com.cmpay.hacp.service;

import com.cmpay.lemon.framework.cache.jcache.JCacheCacheable;

/**
 * <AUTHOR>
 */
public interface SystemAccessTokenService {

    /**
     * 获取应用token
     *
     * @return
     */
    String getAccessToken();

    /**
     * 查询公钥
     *
     * @param appId
     * @return
     */
    @JCacheCacheable(cacheNames = "HACP_WEB_ADMIN_CIPHER", key = "'UPMS:PUBLICKEY:'+#appId", unless = "#result == null")
    String getPublicKey(String appId);


    /**
     * 查询私钥
     *
     * @return
     */
    String getPrivateKey();


    /**
     * 查询AesKey
     *
     * @return
     */
    String getAesKey();
}
