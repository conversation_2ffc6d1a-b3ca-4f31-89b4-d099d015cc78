package com.cmpay.hacp.service;

import com.cmpay.hacp.bo.system.DictBO;
import com.cmpay.lemon.framework.page.PageInfo;

import java.util.List;
import java.util.Map;

/**
 * 系统字典服务
 *
 * <AUTHOR>
 */
public interface SystemDictionaryService {

    /**
     * 查询所有字典树形结构
     *
     * @return
     */
    List<DictBO> queryTreeDict();

    /**
     * 分页查询字典列表
     *
     * @param pageNum
     * @param pageSize
     * @param dictBO
     * @return
     */
    PageInfo<DictBO> getPage(int pageNum, int pageSize, DictBO dictBO);


    /**
     * 根据字典编号id查询字典详细信息
     *
     * @param id
     * @return
     */
    DictBO info(String id);

    /**
     * 根据父ID查询字典
     *
     * @param dictBO
     * @return
     */
    List<DictBO> queryDictInfos(DictBO dictBO);

    /**
     * 新增字典
     *
     * @param dictBO
     */
    void add(DictBO dictBO);

    /**
     * 删除字典信息
     *
     * @param ids
     * @return
     */
    void deleteDictInfo(List<String> ids);

    /**
     * 修改字典
     *
     * @param dictBO
     */

    void update(DictBO dictBO);

    /**
     * 判断是否是管理员，配置在字典中
     *
     * @param userId
     * @param applicationName
     * @return
     */
    boolean isAdmin(String userId, String applicationName);

    List<DictBO> queryDictChildren(DictBO dictBO);

    Map<String,Map<String,String>> getDictMapByTypes(String... types);
}
