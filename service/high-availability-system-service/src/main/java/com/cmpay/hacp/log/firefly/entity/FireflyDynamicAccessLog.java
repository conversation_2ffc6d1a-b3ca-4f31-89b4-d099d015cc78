package com.cmpay.hacp.log.firefly.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 动态日志/调用第三方日志对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class FireflyDynamicAccessLog extends FireflyLog {

    /**
     * 日志类型
     */
    @JsonProperty(value = "UILogType")
    private String logType;
    /**
     * 操作者
     */
    private String operator;
    /**
     * 操作动作
     */
    private String operationAction;
    /**
     * 执行对象
     */
    private String executionTarget;
    /**
     * 操作者IP地址
     */
    @JsonProperty(value = "operatorIP")
    private String operatorIp;
    /**
     * 执行时间
     */
    private LocalDateTime operatorTime;
    /**
     * 操作状态
     */
    private String operatorStatus;
    /**
     * 数据量级
     */
    private Long dataSize;
    /**
     * 数据量级单位(line-行数、byte-大小)
     */
    private String dataSizeType;
    /**
     * 数据所属对象
     */
    @JsonProperty(value = "dataIT")
    private String dataIt;

    /**
     * 流转路径
     */
    private String dataPath;

    /**
     * 接口记录
     */
    private String interfaceRecord;


}
