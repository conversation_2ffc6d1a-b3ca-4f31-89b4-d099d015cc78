package com.cmpay.hacp.service;


import com.cmpay.hacp.bo.system.LoginHistoryLogBO;
import com.cmpay.hacp.bo.system.LoginLatestInfoBO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SystemLoginLatestInfoService {

    /**
     * 登记最近一次登录信息
     *
     * @param loginLatestInfo
     */
    void addLoginLatestInfo(LoginLatestInfoBO loginLatestInfo);

    /**
     * 查询用户最近授权或登录记录
     * isUse 0-登录，1-授权
     *
     * @param userIds
     * @return
     */
    List<LoginLatestInfoBO> queryLoginLastInfoByUserIds(List<String> userIds);

    /**
     * 查询用户最近一次登录信息
     *
     * @param userId
     * @return
     */
    LoginHistoryLogBO queryLoginLatestInfo(String userId);
}
