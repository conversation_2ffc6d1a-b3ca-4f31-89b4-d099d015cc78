/*
 * @ClassName RoleDO
 * @Description
 * @version 1.0
 * @Date 2021-09-02 15:24:38
 */
package com.cmpay.hacp.entity;

import com.cmpay.lemon.framework.annotation.DataObject;

import java.time.LocalDateTime;

@DataObject
public class RoleDO extends BaseDO {
    /**
     * @Fields roleId 角色编号
     */
    private Long roleId;
    /**
     * @Fields roleName 角色名称
     */
    private String roleName;
    /**
     * @Fields remark 备注
     */
    private String remark;
    /**
     * @Fields status DISABLE:禁用  ENABLE:启用
     */
    private String status;
    /**
     * @Fields deptId 部门id
     */
    private String deptId;
    /**
     * @Fields createUserId 创建者id
     */
    private String createUserId;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields modifyTime 更新时间
     */
    private LocalDateTime modifyTime;
    /**
     * @Fields ownerAppId 归属应用
     */
    private String ownerAppId;

    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public String getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getOwnerAppId() {
        return ownerAppId;
    }

    public void setOwnerAppId(String ownerAppId) {
        this.ownerAppId = ownerAppId;
    }
}