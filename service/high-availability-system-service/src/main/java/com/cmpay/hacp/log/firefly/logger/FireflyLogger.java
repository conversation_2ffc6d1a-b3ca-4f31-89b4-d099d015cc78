package com.cmpay.hacp.log.firefly.logger;


import com.cmpay.hacp.log.firefly.entity.FireflyLog;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;

/**
 * <AUTHOR>
 * 萤火虫针对安全审计日志的抽象类
 */
public abstract class FireflyLogger {

    protected final Logger logger;

    protected final ObjectMapper objectMapper;


    public FireflyLogger(ObjectMapper objectMapper,
                         Logger logger) {
        this.logger = logger;
        this.objectMapper = objectMapper;
    }

    /**
     * 请求日志
     *
     * @param fireflyLog
     */
    public abstract void println(FireflyLog fireflyLog);
}
