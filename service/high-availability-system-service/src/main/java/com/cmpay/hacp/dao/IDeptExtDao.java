package com.cmpay.hacp.dao;

import com.cmpay.hacp.entity.DeptDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IDeptExtDao extends IDeptDao {
    /**
     * 查询部门信息
     *
     * @param deptDO
     * @return
     */
    List<DeptDO> queryDepartments(@Param("deptDO") DeptDO deptDO);

    /**
     * 查询部门信息
     *
     * @param deptDO
     * @param deptIds
     * @return
     */
    List<DeptDO> queryChildDepartments(@Param("deptDO") DeptDO deptDO, @Param("deptIds") List<String> deptIds);

    List<String> getAllDeptIds();

}