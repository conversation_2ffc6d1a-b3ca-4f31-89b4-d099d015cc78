package com.cmpay.hacp.service.impl;

import com.cmpay.hacp.bo.system.DictBO;
import com.cmpay.hacp.dao.IDictExtDao;
import com.cmpay.hacp.entity.DictDO;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.service.SystemDictionaryService;
import com.cmpay.hacp.utils.IdGenUtil;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.cache.jcache.JCacheCacheable;
import com.cmpay.lemon.framework.page.PageInfo;
import com.cmpay.lemon.framework.utils.PageUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.cmpay.hacp.constant.TenantConstant.ADMIN_USER;
import static com.cmpay.hacp.constant.TenantConstant.PARENT_ID;

/**
 * <AUTHOR>
 */
@Service
public class SystemDictionaryServiceImpl implements SystemDictionaryService {

    @Resource
    protected IDictExtDao dictDao;

    /**
     * 查询字典树形结构
     *
     * @return
     */
    @Override
    public List<DictBO> queryTreeDict() {
        //查询根字典列表
        List<DictBO> dictbos = this.getListParentId(PARENT_ID);
        //递归子字典列表
        if (JudgeUtils.isEmpty(dictbos)) {
            BusinessException.throwBusinessException(MsgEnum.SUCCESS);
        }
        return this.getDictBoTreeList(dictbos, new ArrayList<>());
    }

    /**
     * 可嵌套多级
     * 递归查询子集字典表
     *
     * @param resultDicts
     * @return
     */
    private List<DictBO> getDictBoTreeList(List<DictBO> queryDicts, List<DictBO> resultDicts) {
        queryDicts.stream().forEach(dictBO -> {
            //是否有下级字典项
            List<DictBO> childDicts = this.getListParentId(dictBO.getDictId());
            //存在下级字典项
            if (JudgeUtils.isNotEmpty(childDicts)) {
                //继续查询
                List<DictBO> dictbotreelist = this.getDictBoTreeList(childDicts, new ArrayList<>());
                //将查询的结果放在子集的子节点中
                dictBO.setChild(dictbotreelist);
            }
            resultDicts.add(dictBO);
        });
        return resultDicts;
    }

    /**
     * 查询一级字典列表
     *
     * @param parentId
     * @return
     */
    private List<DictBO> getListParentId(String parentId) {
        DictDO dictDO = new DictDO();
        dictDO.setParentId(parentId);
        List<DictDO> dictdos = dictDao.find(dictDO);
        if (JudgeUtils.isEmpty(dictdos)) {
            return null;
        }
        List<DictBO> dictbos = BeanConvertUtil.convertList(dictdos, DictBO.class);
        return dictbos;
    }


    /**
     * 分页查询字典列表结构
     *
     * @param pageNum
     * @param pageSize
     * @param dictBO
     * @return
     */
    @Override
    public PageInfo<DictBO> getPage(int pageNum, int pageSize, DictBO dictBO) {
        //查询全部
        PageInfo<DictBO> pageInfo = null;
        DictDO dictDO = new DictDO();
        BeanUtils.copyProperties(dictDO, dictBO);
        if (pageNum == 0 || pageSize == 0) {
            pageInfo = new PageInfo<DictBO>(BeanConvertUtil.convertList(dictDao.queryListDict(dictDO), DictBO.class));
        } else {
            pageInfo = PageUtils.pageQueryWithCount(pageNum,
                    pageSize,
                    () -> BeanConvertUtil.convertList(dictDao.queryListDict(dictDO), DictBO.class));
        }
        return pageInfo;
    }


    /**
     * 根据字典编号查询字典详细信息
     *
     * @param id
     * @return
     */
    @Override
    public DictBO info(String id) {
        DictDO dictDO = dictDao.get(id);
        if (JudgeUtils.isNull(dictDO)) {
            BusinessException.throwBusinessException(MsgEnum.DB_SELECT_FAILED);
        }
        DictBO dictBO = new DictBO();
        BeanUtils.copyProperties(dictBO, dictDO);
        return dictBO;
    }

    /**
     * 根据多个条件查询字典详细信息
     *
     * @return
     */
    @Override
    public List<DictBO> queryDictInfos(DictBO dictBO) {
        DictDO dictDO = new DictDO();
        BeanUtils.copyProperties(dictDO, dictBO);
        List<DictDO> dictdos = dictDao.find(dictDO);
        if (JudgeUtils.isEmpty(dictdos)) {
            return new ArrayList<DictBO>();
        }
        List<DictBO> dictbos = BeanConvertUtil.convertList(dictdos, DictBO.class);
        return dictbos;
    }

    /**
     * 新增字典信息
     *
     * @param dictBO
     * @return
     */
    @Override
    public void add(DictBO dictBO) {
        dictBO.setCreateTime(DateTimeUtils.getCurrentLocalDateTime());
        dictBO.setUpdateTime(DateTimeUtils.getCurrentLocalDateTime());
        DictDO dictDO = dictDao.get(dictBO.getDictId());
        if (JudgeUtils.isNotNull(dictDO)) {
            BusinessException.throwBusinessException(MsgEnum.DB_INSERT_FAILED);
        }
        DictDO dict = new DictDO();
        BeanUtils.copyProperties(dict, dictBO);
        dict.setDictId(IdGenUtil.generatorDictId());
        int insert = dictDao.insert(dict);
        if (insert <= 0) {
            BusinessException.throwBusinessException(MsgEnum.DB_INSERT_FAILED);
        }
    }

    /**
     * 删除字典信息
     *
     * @param ids
     * @return
     */
    @Override
    public void deleteDictInfo(List<String> ids) {
        long deleteBatch = dictDao.deleteBatch(ids);
        if (deleteBatch != ids.size()) {
            BusinessException.throwBusinessException(MsgEnum.DB_DELETE_FAILED);
        }
        //同时删除父ID为以上Id的记录
        dictDao.deleteBatchByParentId(ids);
    }

    /**
     * 更新字段
     *
     * @param dictBO
     * @return
     */
    @Override
    public void update(DictBO dictBO) {
        dictBO.setCreateTime(DateTimeUtils.getCurrentLocalDateTime());
        dictBO.setUpdateTime(DateTimeUtils.getCurrentLocalDateTime());
        DictDO dictDO = new DictDO();
        BeanUtils.copyProperties(dictDO, dictBO);
        int update = dictDao.update(dictDO);
        if (update <= 0) {
            BusinessException.throwBusinessException(MsgEnum.DB_UPDATE_FAILED);
        }
        //如果是字典项类型
        if (JudgeUtils.equalsIgnoreCase(PARENT_ID, dictDO.getParentId())) {
            DictDO parentDictDO = new DictDO();
            parentDictDO.setParentId(dictDO.getDictId());
            parentDictDO.setType(dictDO.getValue());
            dictDao.updateTypeByParentId(parentDictDO);
        }
    }


    @Override
    @JCacheCacheable(cacheNames = "HACP_WEB_ADMIN_USER", key = "'ADMIN:' + #applicationName+':' + #userId")
    public boolean isAdmin(String userId, String applicationName) {
        DictBO dictBO = new DictBO();
        dictBO.setType(ADMIN_USER);
        dictBO.setValue(userId);
        List<DictBO> dictBOS = this.queryDictInfos(dictBO);
        return JudgeUtils.isNotEmpty(dictBOS);
    }

    @Override
    public List<DictBO> queryDictChildren(DictBO dictBO) {
        DictDO dictDO = new DictDO();
        BeanUtils.copyProperties(dictDO, dictBO);
        List<DictDO> dictdos = dictDao.queryChildren(dictDO);
        if (JudgeUtils.isEmpty(dictdos)) {
            return new ArrayList<>();
        }
        List<DictBO> dictbos = BeanConvertUtil.convertList(dictdos, DictBO.class);
        return dictbos;
    }

    @Override
    public Map<String, Map<String, String>> getDictMapByTypes(String... types) {
        List<DictDO> dictDOS = dictDao.listAllByType(types);
        if(JudgeUtils.isEmpty(dictDOS)){
            return new HashMap<>();
        }
        return dictDOS.stream()
                .collect(Collectors.groupingBy(DictDO::getType, Collectors.toMap(DictDO::getValue, DictDO::getLabel)));
    }
}
