package com.cmpay.hacp.log.aspect;

import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.log.bo.SysDynamicLogBO;
import com.cmpay.hacp.log.bo.SysStaticLogBO;
import com.cmpay.hacp.log.firefly.entity.FireflyDynamicAccessLog;
import com.cmpay.hacp.log.firefly.enums.AccessLogTypeEnums;
import com.cmpay.hacp.log.firefly.enums.FireflyLogDataTypeEnum;
import com.cmpay.hacp.log.firefly.enums.FireflyLogTypeEnum;
import com.cmpay.hacp.log.firefly.logger.FireflyDynamicAccessLogger;
import com.cmpay.hacp.log.firefly.logger.FireflyLogger;
import com.cmpay.hacp.log.service.SystemDynamicLogService;
import com.cmpay.hacp.properties.HacpWebAdminLogProperties;
import com.cmpay.lemon.common.context.LemonContext;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.file.entity.FileEnv;
import com.cmpay.lemon.framework.utils.LemonUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

import java.io.File;
import java.net.InetAddress;
import java.time.temporal.ChronoUnit;
import java.util.List;

import static com.cmpay.hacp.constant.CommonConstant.STATIC_ACCESS_LOG;

/**
 * <AUTHOR>
 */
@Slf4j
@Aspect
public class DynamicFileSystemLogAspect {

    private final SystemDynamicLogService systemDynamicLogService;

    private final ObjectMapper objectMapper;

    private final String applicationName;

    private final String dataIt;

    private final FireflyLogger fireflyLogger;

    private final HacpWebAdminLogProperties.DynamicAccess dynamicAccess;


    public DynamicFileSystemLogAspect(SystemDynamicLogService systemDynamicLogService,
                                      ObjectMapper objectMapper,
                                      FireflyDynamicAccessLogger fireflyLogger,
                                      HacpWebAdminLogProperties hacpWebAdminLogProperties,
                                      String applicationName) {
        this.systemDynamicLogService = systemDynamicLogService;
        this.objectMapper = objectMapper;
        this.objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        this.fireflyLogger = fireflyLogger;
        this.dynamicAccess = hacpWebAdminLogProperties.getDynamicAccess();
        this.dataIt = hacpWebAdminLogProperties.getDataIt();
        this.applicationName = applicationName;
    }


    @Pointcut(value = "execution (* com.cmpay.lemon.file.IFileSystem.process(..))")
    public void fileSystemPointcut() {

    }

    @Around(value = "fileSystemPointcut()")
    public Object fileSystemAround(ProceedingJoinPoint pjp) throws Throwable {
        SysDynamicLogBO sysDynamicLogBO = new SysDynamicLogBO();
        sysDynamicLogBO.setType(AccessLogTypeEnums.DYNAMIC_ACCESS_FILE_CLIENT.getCode());
        return getObject(sysDynamicLogBO, pjp);
    }

    private Object getObject(SysDynamicLogBO sysDynamicLogBO, ProceedingJoinPoint pjp) throws Throwable {
        Object responseObject = null;
        Object[] args = pjp.getArgs();
        this.beforeProceed(sysDynamicLogBO, args);
        try {
            responseObject = pjp.proceed();
            sysDynamicLogBO.setOperatorStatus(this.getOperatorStatus(responseObject));
        } catch (Throwable throwable) {
            // 业务异常
            this.throwableProceed(throwable, sysDynamicLogBO);
            throw throwable;
        } finally {
            this.finallyProceed(sysDynamicLogBO, args);
        }
        return responseObject;
    }


    private SysDynamicLogBO beforeProceed(SysDynamicLogBO sysDynamicLogBO, Object[] objs) {
        try {
            sysDynamicLogBO.setOperatorTime(DateTimeUtils.getCurrentLocalDateTime());
            sysDynamicLogBO.setDataIt(this.dataIt);
            sysDynamicLogBO.setApplicationName(this.applicationName);
            SysStaticLogBO sysLog = this.getSysLog();
            if (JudgeUtils.isNotNull(sysLog)) {
                sysDynamicLogBO.setLogId(sysLog.getLogId());
                sysDynamicLogBO.setDataPath(sysLog.getRequestUri());
                sysDynamicLogBO.setOperator(sysLog.getUserName());
                sysDynamicLogBO.setOperatorIp(sysLog.getRemoteAddr());
            } else {
                sysDynamicLogBO.setLogId(LemonUtils.getRequestId());
                sysDynamicLogBO.setDataPath(dynamicAccess.getDefaultDataPath());
                sysDynamicLogBO.setOperator(dynamicAccess.getDefaultOperator());
                sysDynamicLogBO.setOperatorIp(InetAddress.getLocalHost().getHostAddress());
            }
            for (Object obj : objs) {
                if (obj instanceof FileEnv) {
                    FileEnv fileEnv = (FileEnv) obj;
                    sysDynamicLogBO.setExecutionTarget(fileEnv.getUserName() + "@" + fileEnv.getUrl());
                    sysDynamicLogBO.setParams(InetAddress.getLocalHost().getHostAddress() + ":" + fileEnv.getLocalDir() + ":" + fileEnv.getFiles().toString());
                    sysDynamicLogBO.setInterfaceRecord(fileEnv.getRemoteDir());
                    if (fileEnv.getOperType() == 1) {
                        sysDynamicLogBO.setOperationAction("上传");
                        sysDynamicLogBO.setDataSizeType(FireflyLogDataTypeEnum.bytes.name());
                        sysDynamicLogBO.setDataSize(this.getFilesCountSize(fileEnv.getLocalDir(), fileEnv.getFiles()));
                    }
                    break;
                }
            }
        } catch (Exception exception) {
            log.warn("beforeProceed Exception {}", exception.getMessage());
        }
        return sysDynamicLogBO;
    }

    private long getFilesCountSize(String localDir, List<String> files) {
        long totalSize = 0;
        for (String fileDirs : files) {
            File file = new File(localDir, fileDirs);
            if (file.exists() && file.isFile()) {
                totalSize = totalSize + file.length();
            }
        }
        return totalSize;
    }

    private String getOperatorStatus(Object responseObject) {
        String operatorStatus = dynamicAccess.getDefaultOperatorStatus();
        if (JudgeUtils.isNull(responseObject)) {
            return operatorStatus;
        }
        if (responseObject instanceof Boolean) {
            boolean resp = (Boolean) responseObject;
            operatorStatus = resp ? String.valueOf(HttpStatus.SC_OK) : operatorStatus;
        }
        return operatorStatus;
    }

    private void finallyProceed(SysDynamicLogBO sysDynamicLogBO, Object[] objs) {
        sysDynamicLogBO.setEndTime(DateTimeUtils.getCurrentLocalDateTime());
        sysDynamicLogBO.setDuration(ChronoUnit.MILLIS.between(sysDynamicLogBO.getOperatorTime(), sysDynamicLogBO.getEndTime()));
        //流转路径，取入口请求url
        for (Object obj : objs) {
            if (obj instanceof FileEnv) {
                FileEnv fileEnv = (FileEnv) obj;
                if (fileEnv.getOperType() == 2) {
                    sysDynamicLogBO.setOperationAction("下载");
                    sysDynamicLogBO.setDataSizeType(FireflyLogDataTypeEnum.bytes.name());
                    sysDynamicLogBO.setDataSize(this.getFilesCountSize(fileEnv.getLocalDir(), fileEnv.getFiles()));
                }
                break;
            }
        }
        if (JudgeUtils.isNotBlank(sysDynamicLogBO.getOperatorIp())) {
            fireflyLogger.println(convertFireflyDynamicAccessLog(sysDynamicLogBO));
            systemDynamicLogService.addDynamicLog(sysDynamicLogBO);
        }
    }

    private FireflyDynamicAccessLog convertFireflyDynamicAccessLog(SysDynamicLogBO sysDynamicLogBO) {
        FireflyDynamicAccessLog fireflyDynamicAccessLog = new FireflyDynamicAccessLog();
        BeanUtils.copyProperties(fireflyDynamicAccessLog, sysDynamicLogBO);
        fireflyDynamicAccessLog.setLogType(FireflyLogTypeEnum.dynamicLog.name());
        fireflyDynamicAccessLog.setRequestId(sysDynamicLogBO.getLogId());
        return fireflyDynamicAccessLog;
    }

    private void throwableProceed(Throwable throwable, SysDynamicLogBO sysDynamicLogBO) {
        if (throwable instanceof BusinessException) {
            BusinessException businessException = (BusinessException) throwable;
            sysDynamicLogBO.setOperatorStatus(businessException.getMsgCd());
            sysDynamicLogBO.setType(AccessLogTypeEnums.BUSINESS_EXCEPTION.getCode());
        } else {
            //系统异常日志
            sysDynamicLogBO.setType(AccessLogTypeEnums.TYPE_EXCEPTION.getCode());
            sysDynamicLogBO.setOperatorStatus(MsgEnum.SYSTEM_EXCEPTION.getMsgCd());
            sysDynamicLogBO.setException(throwable.getMessage());
        }
    }

    public SysStaticLogBO getSysLog() {
        return LemonContext.getCurrentContext().containsKey(STATIC_ACCESS_LOG) ? (SysStaticLogBO) LemonContext.getCurrentContext().get(STATIC_ACCESS_LOG) : null;
    }
}
