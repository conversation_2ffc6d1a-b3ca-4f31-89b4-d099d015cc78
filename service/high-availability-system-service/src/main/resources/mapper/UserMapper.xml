<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dao.IUserDao">
    <resultMap id="BaseResultMap" type="com.cmpay.hacp.entity.UserDO">
        <id column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="user_name" property="userName" jdbcType="VARCHAR"/>
        <result column="full_name" property="fullName" jdbcType="VARCHAR"/>
        <result column="password" property="password" jdbcType="VARCHAR"/>
        <result column="salt" property="salt" jdbcType="VARCHAR"/>
        <result column="dept_id" property="deptId" jdbcType="VARCHAR"/>
        <result column="duty_id" property="dutyId" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="mobile" property="mobile" jdbcType="VARCHAR"/>
        <result column="weixin" property="weixin" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="create_user_id" property="createUserId" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="last_login_time" property="lastLoginTime" jdbcType="TIMESTAMP"/>
        <result column="has_role" property="hasRole" jdbcType="VARCHAR"/>
        <result column="cst_user_id" property="cstUserId" jdbcType="VARCHAR"/>
        <result column="app_id" property="appId" jdbcType="VARCHAR"/>
        <result column="pwd_modify_time" property="pwdModifyTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        user_id,
        user_name,
        full_name,
        password,
        salt,
        dept_id,
        duty_id,
        email,
        mobile,
        weixin,
        status,
        create_user_id,
        create_time,
        modify_time,
        last_login_time,
        has_role,
        cst_user_id,
        app_id,
        pwd_modify_time
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from sys_user
        where user_id = #{userId,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String">
        delete
        from sys_user
        where user_id = #{userId,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.entity.UserDO">
        insert into sys_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                user_id,
            </if>
            <if test="userName != null">
                user_name,
            </if>
            <if test="fullName != null">
                full_name,
            </if>
            <if test="password != null">
                password,
            </if>
            <if test="salt != null">
                salt,
            </if>
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="dutyId != null">
                duty_id,
            </if>
            <if test="email != null">
                email,
            </if>
            <if test="mobile != null">
                mobile,
            </if>
            <if test="weixin != null">
                weixin,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="createUserId != null">
                create_user_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
            <if test="lastLoginTime != null">
                last_login_time,
            </if>
            <if test="hasRole != null">
                has_role,
            </if>
            <if test="cstUserId != null">
                cst_user_id,
            </if>
            <if test="appId != null">
                app_id,
            </if>
            <if test="pwdModifyTime != null">
                pwd_modify_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="userName != null">
                #{userName,jdbcType=VARCHAR},
            </if>
            <if test="fullName != null">
                #{fullName,jdbcType=VARCHAR},
            </if>
            <if test="password != null">
                #{password,jdbcType=VARCHAR},
            </if>
            <if test="salt != null">
                #{salt,jdbcType=VARCHAR},
            </if>
            <if test="deptId != null">
                #{deptId,jdbcType=VARCHAR},
            </if>
            <if test="dutyId != null">
                #{dutyId,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                #{email,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null">
                #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="weixin != null">
                #{weixin,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="createUserId != null">
                #{createUserId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastLoginTime != null">
                #{lastLoginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="hasRole != null">
                #{hasRole,jdbcType=VARCHAR},
            </if>
            <if test="cstUserId != null">
                #{cstUserId,jdbcType=VARCHAR},
            </if>
            <if test="appId != null">
                #{appId,jdbcType=VARCHAR},
            </if>
            <if test="pwdModifyTime != null">
                #{pwdModifyTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.entity.UserDO">
        update sys_user
        <set>
            <if test="userName != null">
                user_name = #{userName,jdbcType=VARCHAR},
            </if>
            <if test="fullName != null">
                full_name = #{fullName,jdbcType=VARCHAR},
            </if>
            <if test="password != null">
                password = #{password,jdbcType=VARCHAR},
            </if>
            <if test="salt != null">
                salt = #{salt,jdbcType=VARCHAR},
            </if>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=VARCHAR},
            </if>
            <if test="dutyId != null">
                duty_id = #{dutyId,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                email = #{email,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null">
                mobile = #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="weixin != null">
                weixin = #{weixin,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="createUserId != null">
                create_user_id = #{createUserId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastLoginTime != null">
                last_login_time = #{lastLoginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="hasRole != null">
                has_role = #{hasRole,jdbcType=VARCHAR},
            </if>
            <if test="cstUserId != null">
                cst_user_id = #{cstUserId,jdbcType=VARCHAR},
            </if>
            <if test="appId != null">
                app_id = #{appId,jdbcType=VARCHAR},
            </if>
            <if test="pwdModifyTime != null">
                pwd_modify_time = #{pwdModifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where user_id = #{userId,jdbcType=VARCHAR}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.entity.UserDO">
        select
        <include refid="Base_Column_List"/>
        from sys_user
        <where>
            <if test="userId != null">
                and user_id = #{userId,jdbcType=VARCHAR}
            </if>
            <if test="userName != null">
                and user_name = #{userName,jdbcType=VARCHAR}
            </if>
            <if test="fullName != null">
                and full_name = #{fullName,jdbcType=VARCHAR}
            </if>
            <if test="password != null">
                and password = #{password,jdbcType=VARCHAR}
            </if>
            <if test="salt != null">
                and salt = #{salt,jdbcType=VARCHAR}
            </if>
            <if test="deptId != null">
                and dept_id = #{deptId,jdbcType=VARCHAR}
            </if>
            <if test="dutyId != null">
                and duty_id = #{dutyId,jdbcType=VARCHAR}
            </if>
            <if test="email != null">
                and email = #{email,jdbcType=VARCHAR}
            </if>
            <if test="mobile != null">
                and mobile = #{mobile,jdbcType=VARCHAR}
            </if>
            <if test="weixin != null">
                and weixin = #{weixin,jdbcType=VARCHAR}
            </if>
            <if test="status != null">
                and status = #{status,jdbcType=VARCHAR}
            </if>
            <if test="createUserId != null">
                and create_user_id = #{createUserId,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="modifyTime != null">
                and modify_time = #{modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="lastLoginTime != null">
                and last_login_time = #{lastLoginTime,jdbcType=TIMESTAMP}
            </if>
            <if test="hasRole != null">
                and has_role = #{hasRole,jdbcType=VARCHAR}
            </if>
            <if test="cstUserId != null">
                and cst_user_id = #{cstUserId,jdbcType=VARCHAR}
            </if>
            <if test="appId != null">
                and app_id = #{appId,jdbcType=VARCHAR}
            </if>
            <if test="pwdModifyTime != null">
                and pwd_modify_time = #{pwdModifyTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </select>
</mapper>
