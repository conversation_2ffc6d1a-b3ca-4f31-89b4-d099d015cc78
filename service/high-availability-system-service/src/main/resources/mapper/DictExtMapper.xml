<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dao.IDictExtDao">

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.entity.DictDO">
        <result column="dict_id" property="dictId" jdbcType="VARCHAR"/>
        <result column="value" property="value" jdbcType="VARCHAR"/>
        <result column="label" property="label" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="sort" property="sort" jdbcType="DECIMAL"/>
        <result column="parent_id" property="parentId" jdbcType="VARCHAR"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="remarks" property="remarks" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        dict_id
        , value, label, type, description, sort, parent_id, create_user, create_time, update_user,
        update_time, remarks, status
    </sql>

    <select id="queryListDict" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sys_dict
        <where>
            <if test="label != null and label != '' ">
                and label like concat('%',#{label,jdbcType=VARCHAR},'%')
            </if>
            <if test="type != null and type !='' ">
                and type = #{type,jdbcType=VARCHAR}
            </if>
            <if test="value != null and value !='' ">
                and value like concat('%',#{value,jdbcType=VARCHAR},'%')
            </if>
        </where>
        order by parent_id asc,sort asc,dict_id asc
    </select>
    <select id="queryChildren" resultType="com.cmpay.hacp.entity.DictDO">
        select
        <include refid="Base_Column_List"/>
        from sys_dict
        <where>
            parent_id != '0'
            <if test="type != null and type !='' ">
                and type = #{type,jdbcType=VARCHAR}
            </if>
        </where>
        order by sort asc
    </select>
    <select id="listAllByType" resultType="com.cmpay.hacp.entity.DictDO">
        select
            <include refid="Base_Column_List" />
        from sys_dict
        <where>
            type in
            <foreach collection="types" item="type" open="(" close=")" separator=",">
                #{type,jdbcType=VARCHAR}
            </foreach>
        </where>
    </select>

    <delete id="deleteBatch">
        delete FROM sys_dict where dict_id in
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <delete id="deleteBatchByParentId">
        delete FROM sys_dict where parent_id in
        <foreach collection="parentIds" item="parentId" index="index" open="(" close=")" separator=",">
            #{parentId,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <update id="updateTypeByParentId" parameterType="com.cmpay.hacp.entity.DictDO">
        update sys_dict
        <set>
            <if test="type != null">
                type = #{type,jdbcType=VARCHAR},
            </if>
        </set>
        where parent_id = #{parentId,jdbcType=VARCHAR}
    </update>

</mapper>
