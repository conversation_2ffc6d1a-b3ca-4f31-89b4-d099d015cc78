<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dao.ILoginLatestInfoExtDao">

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.entity.LoginLatestInfoDO">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="pid" property="pid" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="latest_date" property="latestDate" jdbcType="DATE"/>
        <result column="latest_time" property="latestTime" jdbcType="TIMESTAMP"/>
        <result column="first_date" property="firstDate" jdbcType="DATE"/>
        <result column="first_time" property="firstTime" jdbcType="TIMESTAMP"/>
        <result column="is_use" property="isUse" jdbcType="SMALLINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , pid, user_id, latest_date, latest_time, first_date, first_time, is_use
    </sql>

    <update id="updateByUserId" parameterType="com.cmpay.hacp.entity.LoginLatestInfoDO">
        update sys_login_latest_info
        <set>
            <if test="pid != null">
                pid = #{pid,jdbcType=VARCHAR},
            </if>
            <if test="id != null">
                id = #{id,jdbcType=VARCHAR},
            </if>
            <if test="latestDate != null">
                latest_date = #{latestDate,jdbcType=DATE},
            </if>
            <if test="latestTime != null">
                latest_time = #{latestTime,jdbcType=TIMESTAMP},
            </if>
            <if test="firstDate != null">
                first_date = #{firstDate,jdbcType=DATE},
            </if>
            <if test="firstTime != null">
                first_time = #{firstTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isUse != null">
                is_use = #{isUse,jdbcType=SMALLINT},
            </if>
        </set>
        where user_id = #{userId,jdbcType=VARCHAR}
    </update>

    <select id="findByUserIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sys_login_latest_info
        where user_id in
        <foreach collection="userIds" item="userId" index="index" open="(" close=")" separator=",">
            #{userId}
        </foreach>
    </select>
</mapper>
