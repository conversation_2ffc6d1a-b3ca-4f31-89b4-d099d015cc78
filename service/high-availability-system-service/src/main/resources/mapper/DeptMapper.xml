<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dao.IDeptDao">
    <resultMap id="BaseResultMap" type="com.cmpay.hacp.entity.DeptDO">
        <id column="dept_id" property="deptId" jdbcType="VARCHAR"/>
        <result column="parent_id" property="parentId" jdbcType="BIGINT"/>
        <result column="dept_name" property="deptName" jdbcType="VARCHAR"/>
        <result column="order_num" property="orderNum" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="create_user_id" property="createUserId" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        dept_id,
        parent_id,
        dept_name,
        order_num,
        status,
        create_user_id,
        create_time,
        modify_time
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from sys_dept
        where dept_id = #{deptId,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String">
        delete
        from sys_dept
        where dept_id = #{deptId,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.entity.DeptDO">
        insert into sys_dept
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="parentId != null">
                parent_id,
            </if>
            <if test="deptName != null">
                dept_name,
            </if>
            <if test="orderNum != null">
                order_num,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="createUserId != null">
                create_user_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                #{deptId,jdbcType=VARCHAR},
            </if>
            <if test="parentId != null">
                #{parentId,jdbcType=BIGINT},
            </if>
            <if test="deptName != null">
                #{deptName,jdbcType=VARCHAR},
            </if>
            <if test="orderNum != null">
                #{orderNum,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="createUserId != null">
                #{createUserId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.entity.DeptDO">
        update sys_dept
        <set>
            <if test="parentId != null">
                parent_id = #{parentId,jdbcType=BIGINT},
            </if>
            <if test="deptName != null">
                dept_name = #{deptName,jdbcType=VARCHAR},
            </if>
            <if test="orderNum != null">
                order_num = #{orderNum,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="createUserId != null">
                create_user_id = #{createUserId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where dept_id = #{deptId,jdbcType=VARCHAR}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.entity.DeptDO">
        select
        <include refid="Base_Column_List"/>
        from sys_dept
        <where>
            <if test="deptId != null">
                and dept_id = #{deptId,jdbcType=VARCHAR}
            </if>
            <if test="parentId != null">
                and parent_id = #{parentId,jdbcType=BIGINT}
            </if>
            <if test="deptName != null">
                and dept_name = #{deptName,jdbcType=VARCHAR}
            </if>
            <if test="orderNum != null">
                and order_num = #{orderNum,jdbcType=INTEGER}
            </if>
            <if test="status != null">
                and status = #{status,jdbcType=VARCHAR}
            </if>
            <if test="createUserId != null">
                and create_user_id = #{createUserId,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="modifyTime != null">
                and modify_time = #{modifyTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </select>
</mapper>
