<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dao.ITenantWorkspaceUserExtDao">
    <resultMap id="BaseResultMap" type="com.cmpay.hacp.entity.TenantWorkspaceUserDO">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="workspace_user_type" property="workspaceUserType" jdbcType="VARCHAR"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="remarks" property="remarks" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="ExtBaseResultMap" extends="BaseResultMap" type="com.cmpay.hacp.bo.TenantWorkspaceUserBO">
        <result column="workspace_name" property="workspaceName" jdbcType="VARCHAR"/>
        <result column="user_name" property="userName" jdbcType="VARCHAR"/>
        <result column="full_name" property="fullName" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="mobile" property="mobile" jdbcType="VARCHAR"/>
        <result column="user_status" property="userStatus" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , workspace_id, user_id, workspace_user_type, create_user, create_time, update_user,
        update_time, remarks, status
    </sql>

    <delete id="deleteWorkspaceUserByWorkspaceId">
        delete
        from tenant_workspace_user
        where workspace_id = #{workspaceId,jdbcType=VARCHAR}
    </delete>

    <update id="updateAdminToAnyoneWorkspaceUserByWorkspaceId">
        update tenant_workspace_user
        set workspace_user_type = '0',
            update_user= #{updateUser,jdbcType=VARCHAR},
            update_time= #{updateTime,jdbcType=TIMESTAMP}
        where workspace_id = #{workspaceId,jdbcType=VARCHAR}
          and workspace_user_type = '1'
    </update>

    <update id="updateAdminWorkspaceUserByWorkspaceId">
        update tenant_workspace_user
        set workspace_user_type = '1',
            update_user= #{updateUser,jdbcType=VARCHAR},
            update_time= #{updateTime,jdbcType=TIMESTAMP}
        where workspace_id = #{workspaceId,jdbcType=VARCHAR}
          and user_id = #{userId,jdbcType=VARCHAR}
    </update>

    <select id="getWorkspaceUserInfo" resultMap="ExtBaseResultMap">
        SELECT t1.workspace_id,
               t1.workspace_name,
               t2.id,
               t2.workspace_user_type,
               t2.create_user,
               t2.create_time,
               t2.update_user,
               t2.update_time,
               t2.remarks,
               t2.`status`,
               t3.user_id,
               t3.user_name,
               t3.full_name,
               t3.email,
               t3.mobile,
               t3.`status` AS user_status,
               t5.tenant_id,
               t5.tenant_name
        FROM workspace t1
                 LEFT JOIN tenant_workspace_user t2 ON t1.workspace_id = t2.workspace_id
                 LEFT JOIN sys_user t3 ON t2.user_id = t3.user_id
                 LEFT JOIN tenant_workspace t4 ON t1.workspace_id = t4.workspace_id
                 LEFT JOIN tenant t5 ON t4.tenant_id = t5.tenant_id
        where t2.workspace_id = #{workspaceId,jdbcType=VARCHAR}
          and t2.user_id = #{userId,jdbcType=VARCHAR}
        order by t2.update_time desc
    </select>

    <select id="getWorkspaceUsers" parameterType="com.cmpay.hacp.bo.TenantWorkspaceUserBO"
            resultMap="ExtBaseResultMap">
        SELECT
        t1.workspace_id,
        t1.workspace_name,
        t2.id,
        t2.workspace_user_type,
        t2.create_user,
        t2.create_time,
        t2.update_user,
        t2.update_time,
        t2.remarks,
        t2.`status`,
        t3.user_id,
        t3.user_name,
        t3.full_name,
        t3.email,
        t3.mobile,
        t3.`status` AS user_status,
        t5.tenant_id,
        t5.tenant_name
        FROM
        workspace t1
        LEFT JOIN tenant_workspace_user t2 ON t1.workspace_id = t2.workspace_id
        LEFT JOIN sys_user t3 ON t2.user_id = t3.user_id
        LEFT JOIN tenant_workspace t4 ON t1.workspace_id = t4.workspace_id
        LEFT JOIN tenant t5 ON t4.tenant_id = t5.tenant_id
        <if test="workspaceRoleId != null and workspaceRoleId!=''">
            LEFT JOIN tenant_workspace_user_role t6 ON t2.user_id = t6.user_id
        </if>
        <where>
            <if test="workspaceRoleId != null and workspaceRoleId!=''">
                and t6.workspace_role_id = #{workspaceRoleId,jdbcType=VARCHAR}
            </if>
            <if test="id != null and id!=''">
                and t2.id = #{id,jdbcType=VARCHAR}
            </if>
            <if test="workspaceId != null and workspaceId!=''">
                and t2.workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="userId != null and userId!=''">
                and t2.user_id = #{userId,jdbcType=VARCHAR}
            </if>
            <if test="workspaceUserType != null and workspaceUserType!=''">
                and t2.workspace_user_type = #{workspaceUserType,jdbcType=VARCHAR}
            </if>
            <if test="createUser != null and createUser!=''">
                and t2.create_user = #{createUser,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                and t2.create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateUser != null and updateUser!=''">
                and t2.update_user = #{updateUser,jdbcType=VARCHAR}
            </if>
            <if test="updateTime != null">
                and t2.update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="remarks != null and remarks!=''">
                and t2.remarks = #{remarks,jdbcType=VARCHAR}
            </if>
            <if test="status != null and status!=''">
                and t2.status = #{status,jdbcType=VARCHAR}
            </if>
            <if test="workspaceName != null and workspaceName!=''">
                and t1.workspace_name like concat('%',#{workspaceName,jdbcType=VARCHAR},'%')
            </if>
            <if test="userName != null and userName!=''">
                and t3.user_name = #{userName,jdbcType=VARCHAR}
            </if>
            <if test="fullName != null and fullName!=''">
                and t3.full_name like concat('%',#{fullName,jdbcType=VARCHAR},'%')
            </if>
            <if test="mobile != null and mobile!=''">
                and t3.mobile = #{mobile,jdbcType=VARCHAR}
            </if>
        </where>
        order by t2.update_time desc
    </select>

    <select id="getWorkspaceUserIds" resultType="java.lang.String">
        select user_id
        from tenant_workspace_user
        where workspace_id = #{workspaceId,jdbcType=VARCHAR}
        order by update_time desc
    </select>

    <select id="existWorkspaceUser" resultMap="ExtBaseResultMap">
        SELECT workspace_id, user_id
        FROM tenant_workspace_user
        WHERE workspace_id = #{workspaceId,jdbcType=VARCHAR}
          AND user_id = #{userId,jdbcType=VARCHAR}
        UNION
        SELECT t1.workspace_id, t2.user_id
        FROM tenant_workspace t1
                 LEFT JOIN tenant_user t2 ON t1.tenant_id = t2.tenant_id
        WHERE t1.workspace_id = #{workspaceId,jdbcType=VARCHAR}
          AND t2.user_id = #{userId,jdbcType=VARCHAR}
    </select>

    <delete id="deleteWorkspaceUser">
        delete
        from tenant_workspace_user
        where workspace_id = #{workspaceId,jdbcType=VARCHAR}
          and user_id = #{userId,jdbcType=VARCHAR}
    </delete>
</mapper>
