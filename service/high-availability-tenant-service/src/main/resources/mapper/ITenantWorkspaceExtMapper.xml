<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dao.ITenantWorkspaceExtDao">
    <resultMap id="BaseResultMap" type="com.cmpay.hacp.entity.TenantWorkspaceDO">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="tenant_id" property="tenantId" jdbcType="VARCHAR"/>
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="remarks" property="remarks" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="WorkspaceResultMap" type="com.cmpay.hacp.bo.TenantWorkspaceBO">
        <id column="workspace_id" property="workspaceId" jdbcType="VARCHAR"/>
        <result column="workspace_name" property="workspaceName" jdbcType="VARCHAR"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="remarks" property="remarks" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , tenant_id, workspace_id, create_user, create_time, update_user, update_time,
        remarks, status
    </sql>

    <delete id="deleteTenantWorkspaceByTenantId">
        delete
        from tenant_workspace
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
    </delete>

    <select id="getWorkspacesByTenantId" parameterType="java.lang.String" resultMap="WorkspaceResultMap">
        SELECT a.workspace_id,
               b.workspace_name,
               a.create_user,
               a.create_time,
               a.update_user,
               a.update_time,
               a.remarks,
               a.status
        FROM tenant_workspace a
                 LEFT JOIN workspace b ON a.workspace_id = b.workspace_id
        WHERE a.tenant_id = #{tenantId,jdbcType=VARCHAR}
        order by a.update_time desc
    </select>
    <select id="getWorkspaceIdsByTenantId" resultType="java.lang.String">
        select workspace_id
        from tenant_workspace
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
        order by update_time desc
    </select>

    <select id="getTenantWorkspaceByWorkspaceId" parameterType="java.lang.String" resultMap="BaseResultMap">
        select tenant_id
        from tenant_workspace
        where workspace_id = #{workspaceId,jdbcType=VARCHAR}
    </select>
</mapper>
