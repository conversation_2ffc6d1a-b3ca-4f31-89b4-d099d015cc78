<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dao.ITenantWorkspaceUserRoleExtDao">
    <resultMap id="BaseResultMap" type="com.cmpay.hacp.entity.TenantWorkspaceUserRoleDO">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="workspace_role_id" property="workspaceRoleId" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="remarks" property="remarks" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , workspace_role_id, user_id, create_user, create_time, update_user, update_time,
        remarks, status
    </sql>

    <delete id="deleteWorkspaceUserRoleByWorkspaceId">
        DELETE
        FROM tenant_workspace_user_role
        WHERE user_id = #{userId,jdbcType=VARCHAR}
          AND workspace_role_id IN
              (SELECT workspace_role_id FROM tenant_workspace_role WHERE workspace_id = #{workspaceId,jdbcType=VARCHAR})
    </delete>

    <select id="getUserWorkspaceRoleIds" resultType="java.lang.String">
        SELECT workspace_role_id
        FROM tenant_workspace_user_role
        WHERE workspace_role_id IN
              (SELECT workspace_role_id FROM tenant_workspace_role WHERE workspace_id = #{workspaceId,jdbcType=VARCHAR})
          AND user_id = #{userId,jdbcType=VARCHAR}
        order by update_time desc
    </select>

    <delete id="deleteUserByWorkspaceRoleId">
        DELETE
        FROM tenant_workspace_user_role
        WHERE workspace_role_id = #{workspaceRoleId,jdbcType=VARCHAR}
    </delete>
</mapper>
