
package com.cmpay.hacp.dao;

import com.cmpay.hacp.bo.TenantWorkspaceBO;
import com.cmpay.hacp.entity.WorkspaceDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IWorkspaceExtDao extends IWorkspaceDao {
    /**
     * @param workspaceId 项目ID
     * @return 项目详情
     */
    TenantWorkspaceBO getDetailWorkspaceInfo(@Param("workspaceId") String workspaceId);

    /**
     * @param workspace 项目详情
     * @return 项目详情列表
     */
    List<TenantWorkspaceBO> getWorkspaceOwnList(TenantWorkspaceBO workspace);

    /**
     * @param tenantId      租户ID
     * @param workspaceName 项目名称
     * @return 项目列表
     */
    List<WorkspaceDO> getWorkspaceByWorkspaceName(@Param("tenantId") String tenantId, @Param("workspaceName") String workspaceName);

    /**
     * 查询我的项目列表
     *
     * @param userId 用户ID
     * @return 项目列表
     */
    List<TenantWorkspaceBO> getIndexOwnWorkspaces(@Param("userId") String userId);

    /**
     * 查询项目ID列表
     *
     * @return 项目ID列表
     */
    List<String> getAllWorkspaces();

    /**
     * 查询用户可管理项目列表
     *
     * @param workspace 项目信息
     * @return 用户可管理项目列表
     */
    List<TenantWorkspaceBO> getWorkspaceAdminList(TenantWorkspaceBO workspace);
}
