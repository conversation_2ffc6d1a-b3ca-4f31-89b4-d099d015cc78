package com.cmpay.hacp.service;

import com.cmpay.hacp.bo.TenantWorkspaceUserBO;
import com.cmpay.lemon.framework.page.PageInfo;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 项目成员管理
 *
 * <AUTHOR>
 */
public interface TenantWorkspaceUserService {

    /**
     * 将项目管理员置为普通用户
     *
     * @param loginName     操作用户
     * @param localDateTime 操作时间
     * @param workspaceId   项目ID
     */
    void updateAdminToAnyoneWorkspaceUserByWorkspaceId(String loginName, LocalDateTime localDateTime, String workspaceId);

    /**
     * 更新管理员
     *
     * @param loginName     操作用户
     * @param localDateTime 操作时间
     * @param workspaceId   项目ID
     * @param userId        用户ID
     */
    void updateAdminWorkspaceUserByWorkspaceId(String loginName, LocalDateTime localDateTime, String workspaceId, String userId);


    /**
     * 删除项目成员
     *
     * @param workspaceId 项目ID
     */
    void deleteWorkspaceUserByWorkspaceId(String workspaceId);

    /**
     * 新增项目成员
     *
     * @param loginName           操作用户账号
     * @param tenantWorkspaceUser 项目成员详情
     */
    void addWorkspaceUser(String loginName, TenantWorkspaceUserBO tenantWorkspaceUser);

    /**
     * 删除项目成员角色
     *
     * @param workspaceId 项目ID
     * @param userId      项目成员ID
     */
    void deleteWorkspaceUserRoleByWorkspaceId(String workspaceId, String userId);

    /**
     * 新增项目成员角色
     *
     * @param loginName           操作用户
     * @param localDateTime       操作时间
     * @param tenantWorkspaceUser 项目角色详情
     */
    void addWorkspaceUserRole(String loginName, LocalDateTime localDateTime, TenantWorkspaceUserBO tenantWorkspaceUser);

    /**
     * 修改项目成员
     *
     * @param loginName           操作用户账号
     * @param tenantWorkspaceUser 项目成员详情
     */
    void updateWorkspaceUser(String loginName, TenantWorkspaceUserBO tenantWorkspaceUser);

    /**
     * 批量删除项目成员
     *
     * @param ids 主键ID集合
     */
    void deletesWorkspaceUser(List<String> ids);

    /**
     * 删除项目成员
     *
     * @param id 主键ID
     */
    void deleteWorkspaceUser(String id);

    /**
     * 删除项目成员
     *
     * @param workspaceId
     * @param userId
     */
    void deleteWorkspaceUser(String workspaceId, String userId);

    /**
     * 查询项目成员详情
     *
     * @param id 主键ID
     * @return 项目成员详情
     */
    TenantWorkspaceUserBO getWorkspaceUserInfo(String id);

    /**
     * 查询项目成员详情
     *
     * @param workspaceId 项目ID
     * @param userId      项目成员ID
     * @return
     */
    TenantWorkspaceUserBO getTenantWorkspaceUserInfo(String workspaceId, String userId);

    /**
     * 用戶是否是项目成员
     *
     * @param workspaceId 项目ID
     * @param userId      用户ID
     */
    void existWorkspaceUser(String workspaceId, String userId);

    /**
     * 查询项目成员列表详情
     *
     * @param tenantWorkspaceUser 项目成员详情
     * @return 项目成员列表详情
     */
    List<TenantWorkspaceUserBO> getWorkspaceUsers(TenantWorkspaceUserBO tenantWorkspaceUser);

    /**
     * 查询项目成员ID列表
     *
     * @param workspaceId 项目ID
     * @return 项目成员ID列表
     */
    List<String> getWorkspaceUserIds(String workspaceId);

    /**
     * 分页查询项目成员详情列表
     *
     * @param pageNum             第几页
     * @param pageSize            一页多少条
     * @param tenantWorkspaceUser 项目成员详情
     * @return 项目成员列表详情
     */
    PageInfo<TenantWorkspaceUserBO> getWorkspaceUsersByPage(int pageNum, int pageSize, TenantWorkspaceUserBO tenantWorkspaceUser);


}
