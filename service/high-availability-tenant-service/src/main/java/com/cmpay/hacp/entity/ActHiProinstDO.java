/*
 * @ClassName ActHiProinstDO
 * @Description 
 * @version 1.0
 * @Date 2024-05-20 10:10:13
 */
package com.cmpay.hacp.entity;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;

import java.time.LocalDateTime;

@DataObject
public class ActHiProinstDO extends BaseDO {
    /**
     * @Fields id 
     */
    private String id;
    /**
     * @Fields procInstId 
     */
    private String procInstId;
    /**
     * @Fields businessKey 
     */
    private String businessKey;
    /**
     * @Fields procDefKey 
     */
    private String procDefKey;
    /**
     * @Fields procDefId 
     */
    private String procDefId;
    /**
     * @Fields startTime 
     */
    private LocalDateTime startTime;
    /**
     * @Fields endTime 
     */
    private LocalDateTime endTime;
    /**
     * @Fields removalTime 
     */
    private LocalDateTime removalTime;
    /**
     * @Fields duration 
     */
    private Long duration;
    /**
     * @Fields startUserId 
     */
    private String startUserId;
    /**
     * @Fields startActId 
     */
    private String startActId;
    /**
     * @Fields endActId 
     */
    private String endActId;
    /**
     * @Fields superProcessInstanceId 
     */
    private String superProcessInstanceId;
    /**
     * @Fields rootProcInstId 
     */
    private String rootProcInstId;
    /**
     * @Fields superCaseInstanceId 
     */
    private String superCaseInstanceId;
    /**
     * @Fields caseInstId 
     */
    private String caseInstId;
    /**
     * @Fields deleteReason 
     */
    private String deleteReason;
    /**
     * @Fields tenantId 
     */
    private String tenantId;
    /**
     * @Fields state 
     */
    private String state;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getProcInstId() {
        return procInstId;
    }

    public void setProcInstId(String procInstId) {
        this.procInstId = procInstId;
    }

    public String getBusinessKey() {
        return businessKey;
    }

    public void setBusinessKey(String businessKey) {
        this.businessKey = businessKey;
    }

    public String getProcDefKey() {
        return procDefKey;
    }

    public void setProcDefKey(String procDefKey) {
        this.procDefKey = procDefKey;
    }

    public String getProcDefId() {
        return procDefId;
    }

    public void setProcDefId(String procDefId) {
        this.procDefId = procDefId;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public LocalDateTime getRemovalTime() {
        return removalTime;
    }

    public void setRemovalTime(LocalDateTime removalTime) {
        this.removalTime = removalTime;
    }

    public Long getDuration() {
        return duration;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }

    public String getStartUserId() {
        return startUserId;
    }

    public void setStartUserId(String startUserId) {
        this.startUserId = startUserId;
    }

    public String getStartActId() {
        return startActId;
    }

    public void setStartActId(String startActId) {
        this.startActId = startActId;
    }

    public String getEndActId() {
        return endActId;
    }

    public void setEndActId(String endActId) {
        this.endActId = endActId;
    }

    public String getSuperProcessInstanceId() {
        return superProcessInstanceId;
    }

    public void setSuperProcessInstanceId(String superProcessInstanceId) {
        this.superProcessInstanceId = superProcessInstanceId;
    }

    public String getRootProcInstId() {
        return rootProcInstId;
    }

    public void setRootProcInstId(String rootProcInstId) {
        this.rootProcInstId = rootProcInstId;
    }

    public String getSuperCaseInstanceId() {
        return superCaseInstanceId;
    }

    public void setSuperCaseInstanceId(String superCaseInstanceId) {
        this.superCaseInstanceId = superCaseInstanceId;
    }

    public String getCaseInstId() {
        return caseInstId;
    }

    public void setCaseInstId(String caseInstId) {
        this.caseInstId = caseInstId;
    }

    public String getDeleteReason() {
        return deleteReason;
    }

    public void setDeleteReason(String deleteReason) {
        this.deleteReason = deleteReason;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }
}