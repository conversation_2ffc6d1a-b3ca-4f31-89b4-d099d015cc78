package com.cmpay.hacp.service.impl;

import com.cmpay.hacp.bo.system.SystemUserBO;
import com.cmpay.hacp.dao.IUserExtDao;
import com.cmpay.hacp.dao.ITenantExtDao;
import com.cmpay.hacp.dao.IWorkspaceExtDao;
import com.cmpay.hacp.entity.TenantDO;
import com.cmpay.hacp.entity.WorkspaceDO;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.service.HacpBaseService;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * 公用业务
 */
@Service
public class HacpBaseServiceImpl implements HacpBaseService {
    @Resource
    private IWorkspaceExtDao workspaceExtDao;

    @Resource
    private ITenantExtDao tenantExtDao;

    @Resource
    private IUserExtDao userExtDao;


    @Override
    public void workspaceExist(String workspaceId) {
        WorkspaceDO workspaceEntity = workspaceExtDao.get(workspaceId);
        if (JudgeUtils.isNull(workspaceEntity)) {
            BusinessException.throwBusinessException(MsgEnum.WORKPLACE_NOT_EXIST);
        }
    }


    @Override
    public void tenantExist(String tenantId) {
        TenantDO tenantEntity = tenantExtDao.get(tenantId);
        if (JudgeUtils.isNull(tenantEntity)) {
            BusinessException.throwBusinessException(MsgEnum.TENANT_NOT_EXIST);
        }
    }


    @Override
    public void userExist(String userId) {
        SystemUserBO user = new SystemUserBO();
        user.setUserId(userId);
        List<SystemUserBO> systemUsers = userExtDao.getSystemUsers(user);
        if (JudgeUtils.isEmpty(systemUsers)) {
            BusinessException.throwBusinessException(MsgEnum.SYS_USER_NOT_EXIST);
        }
    }
}
