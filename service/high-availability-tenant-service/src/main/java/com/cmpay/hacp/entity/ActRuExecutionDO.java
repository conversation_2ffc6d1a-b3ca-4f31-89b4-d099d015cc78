/*
 * @ClassName ActRuExecutionDO
 * @Description 
 * @version 1.0
 * @Date 2024-05-17 16:37:42
 */
package com.cmpay.hacp.entity;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;

@DataObject
public class ActRuExecutionDO extends BaseDO {
    /**
     * @Fields id 
     */
    private String id;
    /**
     * @Fields rev 
     */
    private Integer rev;
    /**
     * @Fields rootProcInstId 
     */
    private String rootProcInstId;
    /**
     * @Fields procInstId 
     */
    private String procInstId;
    /**
     * @Fields businessKey 
     */
    private String businessKey;
    /**
     * @Fields parentId 
     */
    private String parentId;
    /**
     * @Fields procDefId 
     */
    private String procDefId;
    /**
     * @Fields superExec 
     */
    private String superExec;
    /**
     * @Fields superCaseExec 
     */
    private String superCaseExec;
    /**
     * @Fields caseInstId 
     */
    private String caseInstId;
    /**
     * @Fields actId 
     */
    private String actId;
    /**
     * @Fields actInstId 
     */
    private String actInstId;
    /**
     * @Fields isActive 
     */
    private Byte isActive;
    /**
     * @Fields isConcurrent 
     */
    private Byte isConcurrent;
    /**
     * @Fields isScope 
     */
    private Byte isScope;
    /**
     * @Fields isEventScope 
     */
    private Byte isEventScope;
    /**
     * @Fields suspensionState 
     */
    private Integer suspensionState;
    /**
     * @Fields cachedEntState 
     */
    private Integer cachedEntState;
    /**
     * @Fields sequenceCounter 
     */
    private Long sequenceCounter;
    /**
     * @Fields tenantId 
     */
    private String tenantId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getRev() {
        return rev;
    }

    public void setRev(Integer rev) {
        this.rev = rev;
    }

    public String getRootProcInstId() {
        return rootProcInstId;
    }

    public void setRootProcInstId(String rootProcInstId) {
        this.rootProcInstId = rootProcInstId;
    }

    public String getProcInstId() {
        return procInstId;
    }

    public void setProcInstId(String procInstId) {
        this.procInstId = procInstId;
    }

    public String getBusinessKey() {
        return businessKey;
    }

    public void setBusinessKey(String businessKey) {
        this.businessKey = businessKey;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getProcDefId() {
        return procDefId;
    }

    public void setProcDefId(String procDefId) {
        this.procDefId = procDefId;
    }

    public String getSuperExec() {
        return superExec;
    }

    public void setSuperExec(String superExec) {
        this.superExec = superExec;
    }

    public String getSuperCaseExec() {
        return superCaseExec;
    }

    public void setSuperCaseExec(String superCaseExec) {
        this.superCaseExec = superCaseExec;
    }

    public String getCaseInstId() {
        return caseInstId;
    }

    public void setCaseInstId(String caseInstId) {
        this.caseInstId = caseInstId;
    }

    public String getActId() {
        return actId;
    }

    public void setActId(String actId) {
        this.actId = actId;
    }

    public String getActInstId() {
        return actInstId;
    }

    public void setActInstId(String actInstId) {
        this.actInstId = actInstId;
    }

    public Byte getIsActive() {
        return isActive;
    }

    public void setIsActive(Byte isActive) {
        this.isActive = isActive;
    }

    public Byte getIsConcurrent() {
        return isConcurrent;
    }

    public void setIsConcurrent(Byte isConcurrent) {
        this.isConcurrent = isConcurrent;
    }

    public Byte getIsScope() {
        return isScope;
    }

    public void setIsScope(Byte isScope) {
        this.isScope = isScope;
    }

    public Byte getIsEventScope() {
        return isEventScope;
    }

    public void setIsEventScope(Byte isEventScope) {
        this.isEventScope = isEventScope;
    }

    public Integer getSuspensionState() {
        return suspensionState;
    }

    public void setSuspensionState(Integer suspensionState) {
        this.suspensionState = suspensionState;
    }

    public Integer getCachedEntState() {
        return cachedEntState;
    }

    public void setCachedEntState(Integer cachedEntState) {
        this.cachedEntState = cachedEntState;
    }

    public Long getSequenceCounter() {
        return sequenceCounter;
    }

    public void setSequenceCounter(Long sequenceCounter) {
        this.sequenceCounter = sequenceCounter;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }
}