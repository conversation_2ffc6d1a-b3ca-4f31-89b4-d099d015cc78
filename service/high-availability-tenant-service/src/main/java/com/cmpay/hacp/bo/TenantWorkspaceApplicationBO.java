
package com.cmpay.hacp.bo;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class TenantWorkspaceApplicationBO {
    /**
     * @Fields applicationId 应用ID
     */
    private String applicationId;
    /**
     * @Fields workspaceId 项目ID
     */
    private String workspaceId;
    /**
     * @Fields application 应用名称
     */
    private String application;
    /**
     * @Fields profile 应用运行profile
     */
    private String profile;
    /**
     * @Fields type 类型(0-业务应用、1-网关应用、2-中间件应用)
     */
    private String type;
    /**
     * @Fields status 状态(0-数据库有，服务列表没有，1-数据库有，服务列表有)
     */
    private String status;
    /**
     * @Fields remark 备注
     */
    private String remark;
    /**
     * @Fields createTs 创建时间
     */
    private LocalDateTime createTs;
    /**
     * @Fields createBy 创建人
     */
    private String createBy;
    /**
     * @Fields updateTs 更新时间
     */
    private LocalDateTime updateTs;
    /**
     * @Fields updateBy 更新人
     */
    private String updateBy;
    /**
     * @Fields delete 删除标记('0'-未删除,'1'-'已删除')
     */
    private String delete;
}
