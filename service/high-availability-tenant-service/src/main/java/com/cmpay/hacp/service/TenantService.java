package com.cmpay.hacp.service;

import com.cmpay.hacp.bo.TenantBO;
import com.cmpay.hacp.bo.TenantUserBO;
import com.cmpay.lemon.framework.page.PageInfo;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 租户管理
 *
 * <AUTHOR>
 */
public interface TenantService {

    /**
     * 新增租户
     *
     * @param loginName 操作用户账号
     * @param tenant    租户信息
     */
    void addTenant(String loginName, TenantBO tenant);

    /**
     * 修改租户
     *
     * @param loginName 操作用户账号
     * @param tenant    租户信息
     */
    void updateTenant(String loginName, TenantBO tenant);

    /**
     * 用户是否是租户管理员
     *
     * @param tenantId 租户ID
     * @param userId   用户ID
     * @return 是、否
     */
    boolean isAdminTenantUser(String tenantId, String userId);

    /**
     * 删除租户
     *
     * @param loginName     操作用户账号
     * @param localDateTime 操作时间
     * @param tenantId      租户ID
     */
    void deleteTenant(String loginName, LocalDateTime localDateTime, String tenantId);

    /**
     * 批量删除租户
     *
     * @param loginName     操作用户账号
     * @param localDateTime 操作时间
     * @param tenantIds     租户ID集合
     */
    void deleteTenants(String loginName, LocalDateTime localDateTime, List<String> tenantIds);

    /**
     * 根据租户名称查询
     *
     * @param tenantName 租户名称
     * @return 租户列表
     */
    List<TenantBO> getTenantByTenantName(String tenantName);

    /**
     * 给租户管理员绑定租户管理员角色
     *
     * @param userId 租户管理员ID
     */
    void bindAdminTenantRole(String userId);

    /**
     * 新增租户成员
     *
     * @param loginName      操作用户账号
     * @param localDateTime  操作时间
     * @param userId         租户成员ID
     * @param tenantId       租户ID
     * @param tenantUserType 租户成员类型
     */
    void addTenantUser(String loginName, LocalDateTime localDateTime, String userId, String tenantId, String tenantUserType);

    /**
     * 查询租户成员列表
     *
     * @param tenantId 租户ID
     * @return 租户成员列表
     */
    List<TenantUserBO> getTenantUsers(String tenantId);

    /**
     * 删除租户管理员
     *
     * @param tenantId 租户ID
     */
    void deleteAdminUserByTenantId(String tenantId);

    /**
     * 查询租户详情列表
     *
     * @param tenant 租户信息
     * @return 租户详情列表
     */
    List<TenantBO> getDetailTenants(TenantBO tenant);

    /**
     * 查询租户详情
     *
     * @param tenantId 租户ID
     * @return 租户详情
     */
    TenantBO getDetailTenantInfo(String tenantId);

    /**
     * 分页查询租户列表
     *
     * @param pageNum  第几页
     * @param pageSize 一页多少条
     * @param tenant   租户信息
     * @return 分页租户列表
     */
    PageInfo<TenantBO> getTenantListByPage(int pageNum, int pageSize, TenantBO tenant);

    /**
     * 查询登录用户租户列表
     *
     * @param userId 用户id
     * @return 登录用户租户列表
     */
    List<TenantBO> getOwnTenantList(String userId);

    /**
     * 查询用户管理租户列表
     *
     * @param userId 用户id
     * @return 用户管理租户列表
     */
    List<TenantBO> getAdminTenantList(String userId);
}
