package com.cmpay.hacp.service;

import com.cmpay.hacp.bo.TenantWorkspaceRoleBO;
import com.cmpay.hacp.entity.MenuDO;
import com.cmpay.lemon.framework.page.PageInfo;

import java.util.List;

/**
 * 项目角色管理服务接口
 *
 * <AUTHOR>
 * @date 2023/08/02
 */
public interface TenantWorkspaceRoleService {
    /**
     * 项目管理角色
     */
    String adminWorkspaceRoleName = "项目管理角色";
    /**
     * 项目普通角色
     */
    String anyOneWorkspaceRoleName = "项目普通角色";

    /**
     * 新增项目角色
     *
     * @param loginName     登录用户名
     * @param workspaceRole 项目角色BO
     */
    void addWorkspaceRole(String loginName, TenantWorkspaceRoleBO workspaceRole);

    /**
     * 根据角色名称，查询角色ID
     *
     * @param roleName 角色名称
     * @return 角色ID
     */
    Long getRoleIdByRoleName(String roleName);

    /**
     * 新增用户系统角色
     *
     * @param roleId 角色ID
     * @param userId 用户ID
     * @return
     */
    void insertSystemUserRole(Long roleId, String userId);

    /**
     * 删除用户系统角色
     *
     * @param roleId 角色ID
     * @param userId 用户ID
     * @return
     */
    void deleteSystemUserRole(Long roleId, String userId);

    /**
     * 根据角色名称，查询系统菜单
     *
     * @param roleName 角色名称
     * @return 菜单ID
     */
    List<Integer> getMenuIdsBySystemWorkspaceRole(String roleName);

    /**
     * 初始化项目角色
     *
     * @param loginName   登录用户名
     * @param workspaceId 项目ID
     */
    void initWorkspaceRole(String loginName, String workspaceId);

    /**
     * 修改项目角色
     *
     * @param loginName     登录用户名
     * @param workspaceRole 项目角色BO
     */
    void updateWorkspaceRole(String loginName, TenantWorkspaceRoleBO workspaceRole);

    /**
     * 新增项目角色菜单
     *
     * @param loginName     操作用户名
     * @param workspaceRole 项目角色菜单
     */
    void addWorkspaceRoleMenu(String loginName, TenantWorkspaceRoleBO workspaceRole);

    /**
     * 删除项目角色菜单
     *
     * @param workspaceRoleId 项目角色ID
     */
    void deleteMenuByWorkspaceRoleId(String workspaceRoleId);

    /**
     * 删除项目角色
     *
     * @param workspaceRoleId 项目角色ID
     */
    void deleteWorkspaceRole(String workspaceRoleId);

    /**
     * 删除角色与用户关联关系
     *
     * @param workspaceRoleId 项目角色ID
     */
    void deleteUserByWorkspaceRoleId(String workspaceRoleId);

    /**
     * 删除项目所有角色
     *
     * @param workspaceId 项目ID
     */
    void deleteWorkspaceRoleByWorkspaceId(String workspaceId);

    /**
     * 批量删除项目角色
     *
     * @param workspaceRoleIds 项目角色ID列表
     */
    void deleteWorkspaceRoles(List<String> workspaceRoleIds);

    /**
     * 查询项目角色详情
     *
     * @param workspaceRoleId 项目角色ID
     * @return 项目角色详情BO
     */
    TenantWorkspaceRoleBO getWorkspaceRoleInfo(String workspaceRoleId);

    /**
     * 分页查询项目角色列表
     *
     * @param pageNum       第几页
     * @param pageSize      一页多少条
     * @param workspaceRole 项目角色查询条件
     * @return 分页项目角色列表
     */
    PageInfo<TenantWorkspaceRoleBO> getDetailWorkspaceRolesByPage(Integer pageNum, Integer pageSize, TenantWorkspaceRoleBO workspaceRole);

    /**
     * 查询项目角色列表
     *
     * @param workspaceRole 项目角色查询条件
     * @return 项目角色列表
     */
    List<TenantWorkspaceRoleBO> getDetailWorkspaceRoles(TenantWorkspaceRoleBO workspaceRole);

    /**
     * 查询项目角色列表
     *
     * @param workspaceId 项目ID
     * @return
     */
    List<String> getWorkspaceRoleIds(String workspaceId);

    /**
     * 查询项目角色列表
     *
     * @param workspaceId       项目ID
     * @param workspaceRoleName 项目角色名称
     * @return 项目角色列表
     */
    List<TenantWorkspaceRoleBO> getWorkspaceRoleByRoleName(String workspaceId, String workspaceRoleName);

    /**
     * 查询项目管理员角色
     *
     * @param workspaceId 项目ID
     * @return 项目角色ID列表
     */
    List<String> getAdminWorkspaceRoleIds(String workspaceId);

    /**
     * 查询用户在项目中拥有的角色列表
     *
     * @param workspaceId 项目ID
     * @param userId      用户ID
     * @return 用户在项目中拥有的角色列表
     */
    List<TenantWorkspaceRoleBO> getWorkspaceRolesByUserId(String workspaceId, String userId);

    /**
     * 查询角色菜单ID列表
     *
     * @param workspaceRoleId 项目ID
     * @return 角色菜单ID列表
     */
    List<Integer> getMenuIdsByWorkspaceRoleId(String workspaceRoleId);


    /**
     * 查询用户所在项目拥有菜单列表及系统菜单
     *
     * @param userId      用户ID
     * @param workspaceId 项目ID
     * @return 菜单列表
     */
    List<MenuDO> getWorkspaceMenusByUserId(String userId, String workspaceId);

}
