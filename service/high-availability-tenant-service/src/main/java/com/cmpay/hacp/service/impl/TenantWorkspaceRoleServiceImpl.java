package com.cmpay.hacp.service.impl;

import com.cmpay.hacp.bo.TenantWorkspaceRoleBO;
import com.cmpay.hacp.dao.ITenantWorkspaceRoleExtDao;
import com.cmpay.hacp.dao.ITenantWorkspaceRoleMenuExtDao;
import com.cmpay.hacp.dao.ITenantWorkspaceUserRoleExtDao;
import com.cmpay.hacp.dao.IWorkspaceExtDao;
import com.cmpay.hacp.entity.MenuDO;
import com.cmpay.hacp.entity.TenantWorkspaceRoleDO;
import com.cmpay.hacp.entity.TenantWorkspaceRoleMenuDO;
import com.cmpay.hacp.entity.WorkspaceDO;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.enums.WorkspaceRoleTypeEnum;
import com.cmpay.hacp.service.TenantWorkspaceRoleService;
import com.cmpay.hacp.service.HacpBaseService;
import com.cmpay.hacp.utils.IdGenUtil;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.page.PageInfo;
import com.cmpay.lemon.framework.utils.PageUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 项目角色管理服务实现接口
 *
 * <AUTHOR>
 * @date 2023/08/02
 */
@Service
public class TenantWorkspaceRoleServiceImpl implements TenantWorkspaceRoleService {

    @Resource
    private ITenantWorkspaceRoleExtDao tenantWorkspaceRoleExtDao;

    @Resource
    private ITenantWorkspaceRoleMenuExtDao tenantWorkspaceRoleMenuExtDao;

    @Resource
    private ITenantWorkspaceUserRoleExtDao tenantWorkspaceUserRoleExtDao;


    @Resource
    private IWorkspaceExtDao workspaceExtDao;
    @Resource
    private HacpBaseService hacpBaseService;

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void addWorkspaceRole(String loginName, TenantWorkspaceRoleBO workspaceRole) {
        hacpBaseService.workspaceExist(workspaceRole.getWorkspaceId());
        // 判断项目角色是否在项目中存在
        List<TenantWorkspaceRoleBO> tenantWorkspaceRoles = this.getWorkspaceRoleByRoleName(workspaceRole.getWorkspaceId(),
                workspaceRole.getWorkspaceRoleName());
        if (JudgeUtils.isNotEmpty(tenantWorkspaceRoles)) {
            BusinessException.throwBusinessException(MsgEnum.EXIST_WORKPLACE_ROLE_NAME);
        }
        // 新增项目角色
        String workspaceRoleId = IdGenUtil.generateWorkspaceRoleId();
        LocalDateTime localDateTime = LocalDateTime.now();
        TenantWorkspaceRoleDO workspaceRoleEntity = new TenantWorkspaceRoleDO();
        BeanUtils.copyProperties(workspaceRoleEntity, workspaceRole);
        workspaceRoleEntity.setWorkspaceRoleId(workspaceRoleId);
        workspaceRoleEntity.setCreateUser(loginName);
        workspaceRoleEntity.setCreateTime(localDateTime);
        workspaceRoleEntity.setUpdateUser(loginName);
        workspaceRoleEntity.setUpdateTime(localDateTime);
        int insertWorkspaceRoleEntity = tenantWorkspaceRoleExtDao.insert(workspaceRoleEntity);
        if (insertWorkspaceRoleEntity < 1) {
            BusinessException.throwBusinessException(MsgEnum.FAIL);
        }
        //这里菜单id可以让前端传，也可以后端查，前端传入角色id则以角色ID为主
        if (JudgeUtils.isNotBlank(workspaceRole.getWorkspaceRoleId())) {
            String parentWorkspaceRoleId = workspaceRole.getWorkspaceRoleId();
            List<Integer> menuIds = this.getMenuIdsByWorkspaceRoleId(parentWorkspaceRoleId);
            if (JudgeUtils.isNotEmpty(menuIds)) {
                if (JudgeUtils.isNotEmpty(workspaceRole.getMenuIds())) {
                    workspaceRole.setMenuIds(Stream.of(menuIds, workspaceRole.getMenuIds())
                            .flatMap(Collection::stream)
                            .distinct()
                            .collect(Collectors.toList()));
                } else {
                    workspaceRole.setMenuIds(menuIds);
                }
            }
        }
        workspaceRole.setWorkspaceRoleId(workspaceRoleId);
        // 添加项目角色菜单
        this.addWorkspaceRoleMenu(loginName, workspaceRole);
    }

    @Override
    public Long getRoleIdByRoleName(String roleName) {
        return tenantWorkspaceRoleExtDao.getRoleIdByRoleName(roleName);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void insertSystemUserRole(Long roleId, String userId) {
        int insertSystemUserRole = tenantWorkspaceRoleExtDao.insertSystemUserRole(roleId, userId);
        if (insertSystemUserRole < 1) {
            BusinessException.throwBusinessException(MsgEnum.FAIL);
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void deleteSystemUserRole(Long roleId, String userId) {
        tenantWorkspaceRoleExtDao.deleteSystemUserRole(roleId, userId);
    }

    @Override
    public List<Integer> getMenuIdsBySystemWorkspaceRole(String roleName) {
        return tenantWorkspaceRoleExtDao.getMenuIdsBySystemWorkspaceRole(roleName);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void initWorkspaceRole(String loginName, String workspaceId) {
        //分别查询角色为项目管理员的角色，普通项目成员角色菜单
        List<Integer> adminWorkspaceRoleMenuIds = this.getMenuIdsBySystemWorkspaceRole(adminWorkspaceRoleName);
        if (JudgeUtils.isNotEmpty(adminWorkspaceRoleMenuIds)) {
            TenantWorkspaceRoleBO tenantWorkspaceRole = new TenantWorkspaceRoleBO();
            tenantWorkspaceRole.setWorkspaceRoleName(adminWorkspaceRoleName);
            tenantWorkspaceRole.setWorkspaceRoleType(WorkspaceRoleTypeEnum.ADMIN.getCode());
            tenantWorkspaceRole.setWorkspaceId(workspaceId);
            tenantWorkspaceRole.setMenuIds(adminWorkspaceRoleMenuIds);
            this.addWorkspaceRole(loginName, tenantWorkspaceRole);
        }
        List<Integer> anyOneWorkspaceRoleMenuIds = this.getMenuIdsBySystemWorkspaceRole(anyOneWorkspaceRoleName);
        if (JudgeUtils.isNotEmpty(anyOneWorkspaceRoleMenuIds)) {
            TenantWorkspaceRoleBO tenantWorkspaceRole = new TenantWorkspaceRoleBO();
            tenantWorkspaceRole.setWorkspaceRoleName(anyOneWorkspaceRoleName);
            tenantWorkspaceRole.setWorkspaceRoleType(WorkspaceRoleTypeEnum.ANYONE.getCode());
            tenantWorkspaceRole.setWorkspaceId(workspaceId);
            tenantWorkspaceRole.setMenuIds(anyOneWorkspaceRoleMenuIds);
            this.addWorkspaceRole(loginName, tenantWorkspaceRole);
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void updateWorkspaceRole(String loginName, TenantWorkspaceRoleBO tenantWorkspaceRole) {
        // 忽略前端传入的项目ID,不可改变项目与角色的绑定关系，需要查询项目角色所属项目
        TenantWorkspaceRoleBO tenantWorkspaceRoleInfo = this.getWorkspaceRoleInfo(tenantWorkspaceRole.getWorkspaceRoleId());
        if (JudgeUtils.isNull(tenantWorkspaceRoleInfo)) {
            BusinessException.throwBusinessException(MsgEnum.FAIL);
        }
        tenantWorkspaceRole.setWorkspaceRoleType(tenantWorkspaceRoleInfo.getWorkspaceRoleType());
        tenantWorkspaceRole.setWorkspaceId(tenantWorkspaceRoleInfo.getWorkspaceId());
        // 判断项目角色是否在项目中存在
        List<TenantWorkspaceRoleBO> tenantWorkspaceRoles = this.getWorkspaceRoleByRoleName(tenantWorkspaceRole.getWorkspaceId(),
                tenantWorkspaceRole.getWorkspaceRoleName());
        if (JudgeUtils.isNotEmpty(tenantWorkspaceRoles)) {
            tenantWorkspaceRoles.stream().forEach(
                    workspaceRole -> {
                        if (!JudgeUtils.equalsIgnoreCase(workspaceRole.getWorkspaceId(), tenantWorkspaceRole.getWorkspaceId())) {
                            BusinessException.throwBusinessException(MsgEnum.EXIST_WORKPLACE_ROLE_NAME);
                        }
                    }
            );
        }
        // 修改项目角色
        LocalDateTime localDateTime = LocalDateTime.now();
        TenantWorkspaceRoleDO workspaceRoleEntity = new TenantWorkspaceRoleDO();
        BeanUtils.copyProperties(workspaceRoleEntity, tenantWorkspaceRole);
        workspaceRoleEntity.setUpdateUser(loginName);
        workspaceRoleEntity.setUpdateTime(localDateTime);
        int updateWorkspaceRoleEntity = tenantWorkspaceRoleExtDao.update(workspaceRoleEntity);
        if (updateWorkspaceRoleEntity < 1) {
            BusinessException.throwBusinessException(MsgEnum.FAIL);
        }
        // 删除项目角色
        this.deleteMenuByWorkspaceRoleId(tenantWorkspaceRole.getWorkspaceRoleId());
        // 添加项目角色菜单
        this.addWorkspaceRoleMenu(loginName, tenantWorkspaceRole);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void addWorkspaceRoleMenu(String loginName, TenantWorkspaceRoleBO tenantWorkspaceRole) {
        LocalDateTime localDateTime = LocalDateTime.now();
        tenantWorkspaceRole.getMenuIds().forEach(menuId -> {
            if(JudgeUtils.isNull(menuId)){ return; }
            TenantWorkspaceRoleMenuDO workspaceRoleMenuEntity = new TenantWorkspaceRoleMenuDO();
            workspaceRoleMenuEntity.setId(IdGenUtil.generateWorkspaceRoleMenuId());
            workspaceRoleMenuEntity.setWorkspaceRoleId(tenantWorkspaceRole.getWorkspaceRoleId());
            workspaceRoleMenuEntity.setMenuId(menuId);
            workspaceRoleMenuEntity.setCreateUser(loginName);
            workspaceRoleMenuEntity.setCreateTime(localDateTime);
            workspaceRoleMenuEntity.setUpdateUser(loginName);
            workspaceRoleMenuEntity.setUpdateTime(localDateTime);
            tenantWorkspaceRoleMenuExtDao.insert(workspaceRoleMenuEntity);
        });
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void deleteMenuByWorkspaceRoleId(String workspaceRoleId) {
        tenantWorkspaceRoleMenuExtDao.deleteMenuByWorkspaceRoleId(workspaceRoleId);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void deleteWorkspaceRole(String workspaceRoleId) {
        //删除角色
        tenantWorkspaceRoleExtDao.delete(workspaceRoleId);
        //删除角色菜单关联关系
        this.deleteMenuByWorkspaceRoleId(workspaceRoleId);
        //删除角色用户关联关系
        this.deleteUserByWorkspaceRoleId(workspaceRoleId);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void deleteUserByWorkspaceRoleId(String workspaceRoleId) {
        tenantWorkspaceUserRoleExtDao.deleteUserByWorkspaceRoleId(workspaceRoleId);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void deleteWorkspaceRoleByWorkspaceId(String workspaceId) {
        //查询项目角色
        List<String> workspaceRoleIds = this.getWorkspaceRoleIds(workspaceId);
        if (JudgeUtils.isNotEmpty(workspaceRoleIds)) {
            //循环项目下所有角色
            workspaceRoleIds.stream().forEach(this::deleteWorkspaceRole);
        }
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void deleteWorkspaceRoles(List<String> workspaceRoleIds) {
        // 批量删除项目角色
        workspaceRoleIds.forEach(this::deleteWorkspaceRole);
    }


    @Override
    public TenantWorkspaceRoleBO getWorkspaceRoleInfo(String workspaceRoleId) {
        TenantWorkspaceRoleBO tenantWorkspaceRole = new TenantWorkspaceRoleBO();
        TenantWorkspaceRoleDO workspaceRoleEntity = tenantWorkspaceRoleExtDao.get(workspaceRoleId);
        if (JudgeUtils.isNull(workspaceRoleEntity)) {
            return tenantWorkspaceRole;
        }
        //通过项目id查询项目名称
        WorkspaceDO workspace = workspaceExtDao.get(workspaceRoleEntity.getWorkspaceId());
        if (JudgeUtils.isNull(workspace)) {
            return tenantWorkspaceRole;
        }
        BeanUtils.copyProperties(tenantWorkspaceRole, workspaceRoleEntity);
        tenantWorkspaceRole.setWorkspaceName(workspace.getWorkspaceName());
        tenantWorkspaceRole.setMenuIds(this.getMenuIdsByWorkspaceRoleId(workspaceRoleId));
        return tenantWorkspaceRole;
    }

    @Override
    public PageInfo<TenantWorkspaceRoleBO> getDetailWorkspaceRolesByPage(Integer pageNum,
            Integer pageSize,
            TenantWorkspaceRoleBO tenantWorkspaceRole) {
        return PageUtils.pageQueryWithCount(pageNum,
                pageSize,
                () -> tenantWorkspaceRoleExtDao.getDetailWorkspaceRoles(tenantWorkspaceRole));
    }

    @Override
    public List<TenantWorkspaceRoleBO> getDetailWorkspaceRoles(TenantWorkspaceRoleBO tenantWorkspaceRole) {
        return tenantWorkspaceRoleExtDao.getDetailWorkspaceRoles(tenantWorkspaceRole);
    }

    @Override
    public List<String> getAdminWorkspaceRoleIds(String workspaceId) {
        return tenantWorkspaceRoleExtDao.getAdminWorkspaceRoleIds(workspaceId);
    }

    @Override
    public List<String> getWorkspaceRoleIds(String workspaceId) {
        return tenantWorkspaceRoleExtDao.getWorkspaceRoleIds(workspaceId);
    }

    @Override
    public List<TenantWorkspaceRoleBO> getWorkspaceRoleByRoleName(String workspaceId, String workspaceRoleName) {
        TenantWorkspaceRoleDO workspaceRoleEntity = new TenantWorkspaceRoleDO();
        workspaceRoleEntity.setWorkspaceId(workspaceId);
        workspaceRoleEntity.setWorkspaceRoleName(workspaceRoleName);
        List<TenantWorkspaceRoleDO> workspaceRoleEntities = tenantWorkspaceRoleExtDao.find(workspaceRoleEntity);
        if (JudgeUtils.isEmpty(workspaceRoleEntities)) {
            return new ArrayList<>();
        }
        return BeanConvertUtil.convertList(workspaceRoleEntities, TenantWorkspaceRoleBO.class);
    }

    @Override
    public List<TenantWorkspaceRoleBO> getWorkspaceRolesByUserId(String workspaceId, String userId) {
        return tenantWorkspaceRoleExtDao.getWorkspaceRolesByUserId(workspaceId, userId);
    }

    @Override
    public List<Integer> getMenuIdsByWorkspaceRoleId(String workspaceRoleId) {
        return tenantWorkspaceRoleMenuExtDao.getMenuIdsByWorkspaceRoleId(workspaceRoleId);
    }

    @Override
    public List<MenuDO> getWorkspaceMenusByUserId(String userId, String workspaceId) {
        return tenantWorkspaceRoleMenuExtDao.getWorkspaceMenusByUserId(userId, workspaceId);
    }


}
