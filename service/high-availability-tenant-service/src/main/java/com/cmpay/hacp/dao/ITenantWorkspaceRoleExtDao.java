package com.cmpay.hacp.dao;

import com.cmpay.hacp.bo.TenantWorkspaceRoleBO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 项目角色扩展DAO
 *
 * <AUTHOR>
 * @date 2023/08/03
 */
@Mapper
public interface ITenantWorkspaceRoleExtDao extends ITenantWorkspaceRoleDao {
    /**
     * 查询项目角色列表
     *
     * @param workspaceRole 项目角色查询条件
     * @return 项目角色列表
     */
    List<TenantWorkspaceRoleBO> getDetailWorkspaceRoles(TenantWorkspaceRoleBO workspaceRole);

    /**
     * 查询项目管理员角色列表
     *
     * @param workspaceId 项目ID
     * @return 项目管理员角色列表
     */
    List<String> getAdminWorkspaceRoleIds(@Param("workspaceId") String workspaceId);

    /**
     * 查询项目角色列表
     *
     * @param workspaceId 项目ID
     * @return 项目角色列表
     */
    List<String> getWorkspaceRoleIds(@Param("workspaceId") String workspaceId);

    /**
     * 查询用户在项目中拥有的角色列表
     *
     * @param workspaceId 项目ID
     * @param userId      用户ID
     * @return 用户在项目中拥有的角色列表
     */
    List<TenantWorkspaceRoleBO> getWorkspaceRolesByUserId(@Param("workspaceId") String workspaceId, @Param("userId") String userId);

    /**
     * 查询系统预制租户、项目角色菜单
     *
     * @param roleName
     * @return 菜单ID集合
     */
    List<Integer> getMenuIdsBySystemWorkspaceRole(@Param("roleName") String roleName);

    /**
     * 查询角色ID
     *
     * @param roleName 角色名称
     * @return 角色ID
     */
    Long getRoleIdByRoleName(@Param("roleName") String roleName);

    /**
     * 新增系统角色
     *
     * @param roleId 角色ID
     * @param userId 用户ID
     * @return
     */
    int insertSystemUserRole(@Param("roleId") Long roleId, @Param("userId") String userId);

    /**
     * 删除用户系统角色
     *
     * @param roleId 角色ID
     * @param userId 用户ID
     * @return
     */
    int deleteSystemUserRole(@Param("roleId") Long roleId, @Param("userId") String userId);
}
