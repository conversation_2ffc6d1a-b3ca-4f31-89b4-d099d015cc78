package com.cmpay.hacp.utils;

import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.tenant.context.TenantContextHolder;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Optional;

/**
 * <AUTHOR>
 * @create 2024/06/26 9:22
 * @since 1.0.0
 */

public class TenantUtils {
    private static final Logger logger = LoggerFactory.getLogger(TenantUtils.class);
    public static TenantContextHolder.TenantContext getTenantContext(){
        return TenantContextHolder.getContext();
    }

    public static String getWorkspaceId(){
        return Optional.ofNullable(getTenantContext()).map(TenantContextHolder.TenantContext::getWorkspaceId).orElse(null);
    }

    public static String getWorkspaceIdNotNull(){
        String workspaceId = Optional.ofNullable(getTenantContext()).map(TenantContextHolder.TenantContext::getWorkspaceId).orElse(null);
        if (JudgeUtils.isBlank(workspaceId)) {
            BusinessException.throwBusinessException(MsgEnum.WORKPLACE_PROFILE_NOT_NULL);
            //return "5789c27872464379927d30e94adf8336";
        }
        logger.info("当前登录项目：{}", workspaceId);
        return workspaceId;
    }

}
