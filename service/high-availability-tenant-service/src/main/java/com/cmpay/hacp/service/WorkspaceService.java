package com.cmpay.hacp.service;

import com.cmpay.hacp.bo.TenantWorkspaceBO;
import com.cmpay.lemon.framework.page.PageInfo;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 项目管理
 *
 * <AUTHOR>
 */
public interface WorkspaceService {
    /**
     * 新增项目
     *
     * @param loginName 操作用户账号
     * @param workspace 项目信息
     */
    void addWorkspace(String loginName, TenantWorkspaceBO workspace);

    /**
     * 修改项目
     *
     * @param loginName 操作用户账号
     * @param workspace 项目信息
     */
    void updateWorkspace(String loginName, TenantWorkspaceBO workspace);


    /**
     * 删除项目
     *
     * @param loginName     操作用户账号
     * @param localDateTime 操作时间
     * @param workspaceId   项目ID
     */
    void deleteWorkspace(String loginName, LocalDateTime localDateTime, String workspaceId);

    /**
     * @param loginName     操作用户账号
     * @param localDateTime 操作时间
     * @param workspaceIds  项目ID集合
     */
    void deleteWorkspaces(String loginName, LocalDateTime localDateTime, List<String> workspaceIds);

    /**
     * 根据项目名称查询
     *
     * @param tenantId      租户ID
     * @param workspaceName 项目名称
     * @return 项目列表
     */
    List<TenantWorkspaceBO> getWorkspaceByWorkspaceName(String tenantId, String workspaceName);

    /**
     * 查询项目详情列表
     *
     * @param tenantWorkspaceBO 项目信息
     * @return 项目详情列表
     */
    List<TenantWorkspaceBO> getWorkspaceOwnList(TenantWorkspaceBO tenantWorkspaceBO);

    /**
     * 查询项目ID列表
     *
     * @return 项目ID列表
     */
    List<String> getAllWorkspaceIds();

    /**
     * 查询首页我的项目列表
     *
     * @param userId 用户ID
     * @return 项目列表
     */
    List<TenantWorkspaceBO> getIndexOwnWorkspaces(String userId);

    /**
     * 查询项目详情
     *
     * @param workspaceId 项目ID
     * @return 项目详情
     */
    TenantWorkspaceBO getDetailWorkspaceInfo(String workspaceId);

    /**
     * 查询项目所属租户
     *
     * @param workspaceId 项目ID
     * @return 项目所属租户
     */
    TenantWorkspaceBO getWorkspaceInfo(String workspaceId);

    /**
     * 分页查询项目列表
     *
     * @param pageNum   第几页
     * @param pageSize  一页多少条
     * @param workspace 项目信息
     * @return 分页项目列表
     */
    PageInfo<TenantWorkspaceBO> getWorkspaceListByPage(int pageNum, int pageSize, TenantWorkspaceBO workspace);

    /**
     * 删除租户下的项目
     *
     * @param loginName     操作用户账号
     * @param localDateTime 操作时间
     * @param tenantId      租户ID
     */
    void deleteWorkspaceByTenantId(String loginName, LocalDateTime localDateTime, String tenantId);

    /**
     * 查询用户可管理项目列表
     *
     * @param workspace 项目信息
     * @return 用户可管理项目列表
     */
    List<TenantWorkspaceBO> getWorkspaceAdminList(TenantWorkspaceBO workspace);
}
