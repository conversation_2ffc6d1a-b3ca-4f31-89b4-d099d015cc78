package com.cmpay.hacp.utils;

import com.cmpay.lemon.framework.autoconfigure.security.SecurityProperties;
import com.cmpay.lemon.framework.security.auth.GenericAuthenticationFilter;

import java.util.Optional;

/**
 * <AUTHOR>
 */
public class LoginPathUtil {

    private static final String SSO_POSTFIX = "/sso";

    private static final String TICKET_POSTFIX = "/ticket";

    public static String getLoginPathPrefix(SecurityProperties securityProperties) {
        return Optional.ofNullable(securityProperties.getAuthentication()).map(SecurityProperties.Authentication::getLoginPathPrefix).orElse(GenericAuthenticationFilter.DEFAULT_PREFIX_FILTER_PROCESSES_URL);
    }

    public static String getSsoLoginPathPrefix(SecurityProperties securityProperties) {
        return Optional.ofNullable(securityProperties.getAuthentication())
                .map(a
                        -> a.getLoginPathPrefix().concat(SSO_POSTFIX))
                .orElse(GenericAuthenticationFilter.DEFAULT_PREFIX_FILTER_PROCESSES_URL.concat(SSO_POSTFIX));
    }

    public static String getTicketLoginPathPrefix(SecurityProperties securityProperties) {
        return Optional.ofNullable(securityProperties.getAuthentication())
                .map(a
                        -> a.getLoginPathPrefix().concat(TICKET_POSTFIX))
                .orElse(GenericAuthenticationFilter.DEFAULT_PREFIX_FILTER_PROCESSES_URL.concat(TICKET_POSTFIX));
    }
}
