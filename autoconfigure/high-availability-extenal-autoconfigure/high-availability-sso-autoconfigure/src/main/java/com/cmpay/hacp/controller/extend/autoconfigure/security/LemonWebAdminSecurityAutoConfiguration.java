package com.cmpay.hacp.controller.extend.autoconfigure.security;

import com.cmpay.hacp.controller.extend.ticket.auth.LemonWebAdminScimSsoLoginAuthenticationProcessor;
import com.cmpay.hacp.controller.extend.ticket.auth.LemonWebAdminSsoLoginAuthenticationProcessor;
import com.cmpay.hacp.controller.extend.ticket.service.SystemSsoLoginService;
import com.cmpay.hacp.service.SystemDictionaryService;
import com.cmpay.hacp.utils.LoginPathUtil;
import com.cmpay.lemon.common.codec.ObjectDecoder;
import com.cmpay.lemon.framework.autoconfigure.context.ContextAutoConfiguration;
import com.cmpay.lemon.framework.autoconfigure.security.SecurityAuthenticationConfiguration;
import com.cmpay.lemon.framework.autoconfigure.security.SecurityAutoConfiguration;
import com.cmpay.lemon.framework.autoconfigure.security.SecurityProperties;
import com.cmpay.lemon.framework.security.auth.MatchableAuthenticationProcessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
@AutoConfigureAfter({ContextAutoConfiguration.class })
@AutoConfigureBefore({SecurityAutoConfiguration.class, SecurityAuthenticationConfiguration.class})
@EnableConfigurationProperties({SecurityProperties.class})
@ConditionalOnClass({MatchableAuthenticationProcessor.class, SecurityAutoConfiguration.class})
public class LemonWebAdminSecurityAutoConfiguration {

    @Value("${spring.application.name}")
    private String applicationName;


    /**
     * 租户门户单点登录
     *
     * @param systemLoginService
     * @param systemDictionaryService
     * @param objectDecoder
     * @param securityProperties
     * @param applicationContext
     * @param lemonWebAdminObjectMapper
     * @return
     */
    @Bean
    @ConditionalOnProperty(prefix = "hacp.web.admin.scim-iam", name = "sso", havingValue = "false", matchIfMissing = false)
    @ConditionalOnClass({LemonWebAdminSsoLoginAuthenticationProcessor.class})
    public MatchableAuthenticationProcessor lemonWebAdminSsoLoginAuthenticationProcessor(SystemSsoLoginService systemLoginService, SystemDictionaryService systemDictionaryService, ObjectDecoder objectDecoder, SecurityProperties securityProperties, ApplicationContext applicationContext, @Qualifier("lemonWebAdminObjectMapper") ObjectMapper lemonWebAdminObjectMapper) {
        return new LemonWebAdminSsoLoginAuthenticationProcessor(systemLoginService, systemDictionaryService, LoginPathUtil.getSsoLoginPathPrefix(securityProperties), objectDecoder, applicationName, applicationContext, lemonWebAdminObjectMapper);
    }

    /**
     * 新4A单点登录
     *
     * @param systemLoginService
     * @param objectDecoder
     * @param securityProperties
     * @param lemonWebAdminObjectMapper
     * @return
     */
    @Bean
    @ConditionalOnProperty(prefix = "hacp.web.admin.scim-iam", name = "sso", havingValue = "true", matchIfMissing = true)
    @ConditionalOnClass({LemonWebAdminScimSsoLoginAuthenticationProcessor.class})
    public MatchableAuthenticationProcessor lemonWebAdminScimSsoLoginAuthenticationProcessor(SystemSsoLoginService systemLoginService, ObjectDecoder objectDecoder, SecurityProperties securityProperties, @Qualifier("hacpObjectMapper") ObjectMapper lemonWebAdminObjectMapper) {
        return new LemonWebAdminScimSsoLoginAuthenticationProcessor(systemLoginService, LoginPathUtil.getSsoLoginPathPrefix(securityProperties), objectDecoder, lemonWebAdminObjectMapper);
    }

}
