package com.cmpay.hacp.controller.tenant;

import com.cmpay.hacp.bo.TenantWorkspaceUserBO;
import com.cmpay.hacp.dto.tenant.TenantWorkspaceUserDTO;
import com.cmpay.hacp.log.annotation.LogRecord;
import com.cmpay.hacp.service.TenantWorkspaceUserService;
import com.cmpay.hacp.utils.TenantUtils;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/07/12 17:25
 * @since 1.0.0
 */
@RestController()
@Api("租户相关-拦截")
@RequestMapping("/v1/permit/tenant")
public class TenantPermitController {

    @Autowired
    private TenantWorkspaceUserService tenantWorkspaceUserService;

    /**
     * 查询项目成员列表
     *
     * @param workspaceRoleId 项目角色ID
     * @return 项目成员列表
     */
    @GetMapping("/user/list")
    @ApiOperation(value = "查询项目成员列表", notes = "查询项目成员列表")
    @ApiResponse(code = 200, message = "项目成员列表")
    @LogRecord(title = "查询项目成员列表", action = "查询")
    public DefaultRspDTO<List<TenantWorkspaceUserDTO>> getWorkspaceUsers(
            @ApiParam(name = "workspaceRoleId", value = "项目角色ID", required = false, example = "3279035dc433428c84ff434379374157")
            @RequestParam(name = "workspaceRoleId", required = false) String workspaceRoleId) {
        TenantWorkspaceUserBO tenantWorkspaceUser = new TenantWorkspaceUserBO();
        tenantWorkspaceUser.setWorkspaceId(TenantUtils.getWorkspaceId());
        tenantWorkspaceUser.setWorkspaceRoleId(workspaceRoleId);
        List<TenantWorkspaceUserBO> workspaceUsers = tenantWorkspaceUserService.getWorkspaceUsers(tenantWorkspaceUser);
        List<TenantWorkspaceUserDTO> tenantWorkspaceUsers = new ArrayList<>();
        if (JudgeUtils.isNotEmpty(workspaceUsers)) {
            tenantWorkspaceUsers = BeanConvertUtil.convertList(workspaceUsers, TenantWorkspaceUserDTO.class);
        }
        return DefaultRspDTO.newSuccessInstance(tenantWorkspaceUsers);
    }
}
