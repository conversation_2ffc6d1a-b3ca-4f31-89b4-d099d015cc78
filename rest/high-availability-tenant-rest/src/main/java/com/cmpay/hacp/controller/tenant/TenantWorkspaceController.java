package com.cmpay.hacp.controller.tenant;

import com.cmpay.hacp.bo.TenantWorkspaceBO;
import com.cmpay.hacp.dto.tenant.*;
import com.cmpay.hacp.log.annotation.LogNoneRecord;
import com.cmpay.hacp.log.annotation.LogRecord;
import com.cmpay.hacp.service.WorkspaceService;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import com.cmpay.lemon.framework.page.PageInfo;
import com.cmpay.lemon.framework.security.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/v1/tenant/workspace")
@Api(tags = "项目管理")
@Validated
public class TenantWorkspaceController {

    @Resource
    private WorkspaceService workspaceService;

    /**
     * 新增项目
     *
     * @param tenantWorkspaceAddDto 项目信息
     * @return 成功/失败
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增项目", notes = "新增项目")
    @ApiResponse(code = 200, message = "返回结果")
    @LogRecord(title = "新增项目", action = "新增")
    @PreAuthorize("hasPermission('TenantWorkspaceController','tenant:workspace:admin:add')")
    public DefaultRspDTO<NoBody> addWorkspace(@Validated @RequestBody TenantWorkspaceAddDTO tenantWorkspaceAddDto) {
        TenantWorkspaceBO tenantWorkspace = new TenantWorkspaceBO();
        BeanUtils.copyProperties(tenantWorkspace, tenantWorkspaceAddDto);
        workspaceService.addWorkspace(SecurityUtils.getLoginName(), tenantWorkspace);
        return DefaultRspDTO.newSuccessInstance();
    }


    /**
     * 修改项目
     *
     * @param tenantWorkspaceUpdateDto 项目信息
     * @return 成功/失败
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改项目", notes = "修改项目")
    @ApiResponse(code = 200, message = "返回结果")
    @LogRecord(title = "修改项目", action = "修改")
    @PreAuthorize("hasPermission('TenantWorkspaceController','tenant:workspace:admin:update')")
    public DefaultRspDTO<NoBody> updateWorkspace(@Validated @RequestBody TenantWorkspaceUpdateDTO tenantWorkspaceUpdateDto) {
        TenantWorkspaceBO tenantWorkspace = new TenantWorkspaceBO();
        BeanUtils.copyProperties(tenantWorkspace, tenantWorkspaceUpdateDto);
        workspaceService.updateWorkspace(SecurityUtils.getLoginName(), tenantWorkspace);
        return DefaultRspDTO.newSuccessInstance();
    }

    /**
     * 删除项目
     *
     * @param tenantWorkspaceDeleteDto 项目信息
     * @return 成功/失败
     */
    @PostMapping("/delete")
    @ApiOperation(value = "删除项目", notes = "删除项目")
    @ApiResponse(code = 200, message = "返回结果")
    @LogRecord(title = "删除项目", action = "删除")
    @PreAuthorize("hasPermission('TenantWorkspaceController','tenant:workspace:admin:delete')")
    public DefaultRspDTO<NoBody> deleteWorkspace(@Validated @RequestBody TenantWorkspaceDeleteDTO tenantWorkspaceDeleteDto) {
        workspaceService.deleteWorkspace(SecurityUtils.getLoginName(),
                LocalDateTime.now(),
                tenantWorkspaceDeleteDto.getWorkspaceId());
        return DefaultRspDTO.newSuccessInstance();
    }

    /**
     * 批量删除项目
     *
     * @param tenantWorkspaceDeletesDto 项目信息
     * @return 成功/失败
     */
    @PostMapping("/deletes")
    @ApiOperation(value = "批量删除项目", notes = "批量删除项目")
    @ApiResponse(code = 200, message = "返回结果")
    @LogRecord(title = "批量删除项目", action = "删除")
    @PreAuthorize("hasPermission('TenantWorkspaceController','tenant:workspace:admin:delete')")
    public DefaultRspDTO<NoBody> deleteWorkspaces(@Validated @RequestBody TenantWorkspaceDeletesDTO tenantWorkspaceDeletesDto) {
        workspaceService.deleteWorkspaces(SecurityUtils.getLoginName(),
                LocalDateTime.now(),
                tenantWorkspaceDeletesDto.getWorkspaceIds());
        return DefaultRspDTO.newSuccessInstance();
    }

    /**
     * 查询项目详情
     *
     * @param workspaceId 项目信息
     * @return 项目详情
     */
    @GetMapping("/info")
    @ApiOperation(value = "查询项目详情", notes = "查询项目详情")
    @ApiResponse(code = 200, message = "项目详情")
    @LogRecord(title = "查询项目详情", action = "查询")
    @PreAuthorize("hasPermission('TenantWorkspaceController','tenant:workspace:admin:query')")
    public DefaultRspDTO<TenantWorkspaceDTO> getWorkspaceInfo(
            @ApiParam(name = "workspaceId", value = "项目ID", required = true, example = "0179035dc433428c84ff434379374157")
            @NotBlank(message = "HAC00006") @RequestParam("workspaceId") String workspaceId) {
        TenantWorkspaceBO tenantWorkspace = workspaceService.getDetailWorkspaceInfo(workspaceId);
        TenantWorkspaceDTO tenantWorkspaceDto = new TenantWorkspaceDTO();
        if (JudgeUtils.isNotNull(tenantWorkspace)) {
            BeanUtils.copyProperties(tenantWorkspaceDto, tenantWorkspace);
        }
        return DefaultRspDTO.newSuccessInstance(tenantWorkspaceDto);
    }

    /**
     * 查询项目列表
     *
     * @param tenantId      租户ID
     * @param workspaceName 项目名称
     * @return 项目列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "查询项目列表", notes = "查询项目列表")
    @ApiResponse(code = 200, message = "项目列表")
    @LogRecord(title = "查询项目列表", action = "查询")
    public DefaultRspDTO<List<TenantWorkspaceDTO>> getWorkspaceList(
            @ApiParam(name = "tenantId", value = "租户ID", required = true, example = "0179035dc433428c84ff434379374157")
            @NotBlank(message = "HAC00004") @RequestParam("tenantId") String tenantId,
            @ApiParam(name = "workspaceName", value = "项目名称", required = false, example = "项目名称")
            @RequestParam(name = "workspaceName", required = false) String workspaceName) {
        TenantWorkspaceBO tenantWorkspace = new TenantWorkspaceBO();
        tenantWorkspace.setWorkspaceName(workspaceName);
        tenantWorkspace.setTenantId(tenantId);
        tenantWorkspace.setUserId(SecurityUtils.getLoginUserId());
        List<TenantWorkspaceBO> workspaces = workspaceService.getWorkspaceOwnList(tenantWorkspace);
        List<TenantWorkspaceDTO> tenantWorkspaces = new ArrayList<>();
        if (JudgeUtils.isNotEmpty(workspaces)) {
            tenantWorkspaces = BeanConvertUtil.convertList(workspaces, TenantWorkspaceDTO.class);
        }
        return DefaultRspDTO.newSuccessInstance(tenantWorkspaces);
    }

    /**
     * 查询首页我的项目列表
     *
     * @return 我的项目列表
     */
    @GetMapping("/index/list")
    @ApiOperation(value = "查询首页我的项目列表", notes = "查询首页我的项目列表")
    @ApiResponse(code = 200, message = "我的项目列表")
    @LogRecord(title = "查询首页我的项目列表", action = "查询")
    public DefaultRspDTO<List<TenantWorkspaceDTO>> getIndexOwnWorkspaces() {
        List<TenantWorkspaceBO> workspaces = workspaceService.getIndexOwnWorkspaces(SecurityUtils.getLoginUserId());
        List<TenantWorkspaceDTO> tenantWorkspaces = new ArrayList<>();
        if (JudgeUtils.isNotEmpty(workspaces)) {
            for (TenantWorkspaceBO workspace : workspaces) {
                TenantWorkspaceDTO tenantWorkspaceDto = new TenantWorkspaceDTO();
                BeanUtils.copyProperties(tenantWorkspaceDto, workspace);
                if (JudgeUtils.isNotEmpty(workspace.getWorkspaceRoles())) {
                    tenantWorkspaceDto.setWorkspaceRoles(BeanConvertUtil.convertList(workspace.getWorkspaceRoles(),
                            TenantWorkspaceRole.class));
                }
                tenantWorkspaces.add(tenantWorkspaceDto);
            }
        }
        return DefaultRspDTO.newSuccessInstance(tenantWorkspaces);
    }

    /**
     * 查询用户可管理项目列表
     *
     * @param tenantId      租户ID
     * @param workspaceName 项目名称
     * @return 用户可管理项目列表
     */
    @GetMapping("/admin/list")
    @ApiOperation(value = "查询用户可管理项目列表", notes = "查询用户可管理项目列表")
    @ApiResponse(code = 200, message = "用户可管理项目列表")
    @LogRecord(title = "查询可管理项目列表", action = "查询")
    public DefaultRspDTO<List<TenantWorkspaceDTO>> getWorkspaceAdminList(
            @ApiParam(name = "tenantId", value = "租户ID", required = true, example = "0179035dc433428c84ff434379374157")
            @RequestParam("tenantId") @NotBlank(message = "HAC00004") String tenantId,
            @ApiParam(name = "workspaceName", value = "项目名称", required = false, example = "项目名称")
            @RequestParam(name = "workspaceName", required = false) String workspaceName) {
        TenantWorkspaceBO tenantWorkspace = new TenantWorkspaceBO();
        tenantWorkspace.setWorkspaceName(workspaceName);
        tenantWorkspace.setTenantId(tenantId);
        tenantWorkspace.setUserId(SecurityUtils.getLoginUserId());
        List<TenantWorkspaceBO> workspaces = workspaceService.getWorkspaceAdminList(tenantWorkspace);
        List<TenantWorkspaceDTO> tenantWorkspaces = new ArrayList<>();
        if (JudgeUtils.isNotEmpty(workspaces)) {
            tenantWorkspaces = BeanConvertUtil.convertList(workspaces, TenantWorkspaceDTO.class);
        }
        return DefaultRspDTO.newSuccessInstance(tenantWorkspaces);
    }

    /**
     * 查询登录用户租户项目列表
     *
     * @param tenantId      租户ID
     * @param workspaceName 项目名称
     * @return 登录用户租户项目列表
     */
    @GetMapping("/own/list")
    @ApiOperation(value = "查询登录用户租户项目列表", notes = "查询登录用户租户项目列表")
    @ApiResponse(code = 200, message = "登录用户租户项目列表")
    @LogRecord(title = "查询我的租户项目列表", action = "查询")
    public DefaultRspDTO<List<TenantWorkspaceDTO>> getWorkspaceOwnList(
            @ApiParam(name = "tenantId", value = "租户ID", required = true, example = "0179035dc433428c84ff434379374157")
            @RequestParam("tenantId") @NotBlank(message = "HAC00004") String tenantId,
            @ApiParam(name = "workspaceName", value = "项目名称", required = false, example = "项目名称")
            @RequestParam(name = "workspaceName", required = false) String workspaceName) {
        TenantWorkspaceBO tenantWorkspace = new TenantWorkspaceBO();
        tenantWorkspace.setWorkspaceName(workspaceName);
        tenantWorkspace.setTenantId(tenantId);
        tenantWorkspace.setUserId(SecurityUtils.getLoginUserId());
        List<TenantWorkspaceBO> workspaces = workspaceService.getWorkspaceOwnList(tenantWorkspace);
        List<TenantWorkspaceDTO> tenantWorkspaces = new ArrayList<>();
        if (JudgeUtils.isNotEmpty(workspaces)) {
            tenantWorkspaces = BeanConvertUtil.convertList(workspaces, TenantWorkspaceDTO.class);
        }
        return DefaultRspDTO.newSuccessInstance(tenantWorkspaces);
    }

    /**
     * 查询项目列表
     *
     * @param workspaceName 项目名称
     * @return 项目列表
     */
    @GetMapping("/page")
    @ApiOperation(value = "分页查询项目列表", notes = "分页查询项目列表")
    @ApiResponse(code = 200, message = "分页项目列表")
    @LogNoneRecord
    @PreAuthorize("hasPermission('TenantWorkspaceController','tenant:workspace:admin:query')")
    public DefaultRspDTO<PageInfo<TenantWorkspaceDTO>> getWorkspaceListByPage(
            @ApiParam(name = "tenantId", value = "租户ID", required = true, example = "0179035dc433428c84ff434379374157")
            @RequestParam("tenantId") @NotBlank(message = "HAC00004") String tenantId,
            @ApiParam(name = "workspaceName", value = "项目名称", required = false, example = "项目名称")
            @RequestParam(name = "workspaceName", required = false) String workspaceName,
            @ApiParam(name = "pageNum", value = "第几页", required = false, defaultValue = "1", example = "1")
            @RequestParam(name = "pageNum", required = false, defaultValue = "1") Integer pageNum,
            @ApiParam(name = "pageSize", value = "一页多少条", required = false, defaultValue = "10", example = "10")
            @RequestParam(name = "pageSize", required = false, defaultValue = "10") Integer pageSize) {
        TenantWorkspaceBO tenantWorkspace = new TenantWorkspaceBO();
        tenantWorkspace.setWorkspaceName(workspaceName);
        tenantWorkspace.setTenantId(tenantId);
        tenantWorkspace.setUserId(SecurityUtils.getLoginUserId());
        PageInfo<TenantWorkspaceBO> workspacePage = workspaceService.getWorkspaceListByPage(pageNum, pageSize, tenantWorkspace);
        PageInfo pageInfo = new PageInfo<>(new ArrayList<>());
        BeanUtils.copyProperties(pageInfo, workspacePage);
        if (JudgeUtils.isNotEmpty(workspacePage.getList())) {
            pageInfo.setList(BeanConvertUtil.convertList(workspacePage.getList(), TenantWorkspaceDTO.class));
        }
        return DefaultRspDTO.newSuccessInstance(pageInfo);
    }


}
