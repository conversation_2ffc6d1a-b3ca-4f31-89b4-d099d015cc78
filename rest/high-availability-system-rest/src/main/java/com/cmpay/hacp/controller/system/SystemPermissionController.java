package com.cmpay.hacp.controller.system;

import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.hacp.api.SystemPermissionApi;
import com.cmpay.hacp.api.VersionApi;
import com.cmpay.hacp.bo.menu.PermMenuTreeMetaBO;
import com.cmpay.hacp.log.annotation.LogNoneRecord;
import com.cmpay.hacp.service.SystemPermissionService;
import com.cmpay.lemon.framework.security.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@Api(tags = "权限管理")
@RestController
@RequestMapping(VersionApi.VERSION_V1)
public class SystemPermissionController {


    @Autowired
    private SystemPermissionService systemPermissionService;

    @Value("${spring.application.name}")
    private String applicationName;

    /**
     * @return
     */
    @ApiOperation("查询用户权限详细信息")
    @LogNoneRecord
    @GetMapping(SystemPermissionApi.PERMISSION_LIST_DETAIL)
    public GenericRspDTO<PermMenuTreeMetaBO> queryUserPermissions() {
        String userId = SecurityUtils.getLoginUserId();
        PermMenuTreeMetaBO permMenuTreeMetaBO = systemPermissionService.queryUserPermissions(userId, applicationName);
        return GenericRspDTO.newSuccessInstance(permMenuTreeMetaBO);
    }

    /**
     * @return
     */
    @ApiOperation("查询用户权限")
    @LogNoneRecord
    @GetMapping(SystemPermissionApi.PERMISSION_LIST)
    public GenericRspDTO<List<String>> getUserPermissions() {
        String userId = SecurityUtils.getLoginUserId();
        List<String> userPermissions = systemPermissionService.getUserPermissions(userId, applicationName);
        return GenericRspDTO.newSuccessInstance(userPermissions);
    }
}
