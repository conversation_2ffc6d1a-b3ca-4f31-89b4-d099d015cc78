package com.cmpay.hacp.controller.system;

import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.hacp.api.SystemLoginApi;
import com.cmpay.hacp.api.VersionApi;
import com.cmpay.hacp.bo.system.UserLoginBO;
import com.cmpay.hacp.dto.system.LoginHistoryLogDTO;
import com.cmpay.hacp.dto.system.UserLoginReqDTO;
import com.cmpay.hacp.dto.system.UserLoginRspDTO;
import com.cmpay.hacp.service.SystemLoginService;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Api(tags = "对外提供登录接口")
@RestController
@RequestMapping(VersionApi.VERSION_V1)
public class SystemLoginController {


    @Autowired
    private SystemLoginService systemLoginService;

    /**
     * 登录效验接口，暴露给外部
     *
     * @param userLoginReqDTO
     * @return
     */
    @PostMapping(SystemLoginApi.LOGIN)
    public GenericRspDTO<UserLoginRspDTO> login(@RequestBody UserLoginReqDTO userLoginReqDTO) {
        UserLoginBO loginBO = systemLoginService.login(BeanUtils.copyPropertiesReturnDest(new UserLoginBO(), userLoginReqDTO));
        //返回登录的信息
        UserLoginRspDTO userInfoDTO = new UserLoginRspDTO();
        BeanUtils.copy(userInfoDTO, loginBO);
        LoginHistoryLogDTO loginHistoryLog = new LoginHistoryLogDTO();
        if (JudgeUtils.isNotNull(loginBO.getLoginHistory())) {
            BeanUtils.copyProperties(loginHistoryLog, loginBO.getLoginHistory());
            userInfoDTO.setLoginHistory(loginHistoryLog);
        }
        userInfoDTO.setUserInfo(loginBO.getUserInfo());
        return GenericRspDTO.newSuccessInstance(userInfoDTO);
    }

}
