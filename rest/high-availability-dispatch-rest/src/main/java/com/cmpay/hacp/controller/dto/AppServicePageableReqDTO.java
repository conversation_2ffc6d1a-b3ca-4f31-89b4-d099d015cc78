package com.cmpay.hacp.controller.dto;

import com.cmpay.hacp.bo.PageableReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class AppServicePageableReqDTO extends PageableReq {

    private String workspaceId;
    /**
     * @Fields appServiceName 业务服务名称
     */
    private String appServiceName;
    /**
     * @Fields appServiceDesc 业务服务描述
     */
    private String appServiceDesc;
    /**
     * @Fields listen 监听端口
     */
    private String listen;
    /**
     * @Fields domainNames 域名列表
     */
    private String domainNames;

    private Byte status;


}
