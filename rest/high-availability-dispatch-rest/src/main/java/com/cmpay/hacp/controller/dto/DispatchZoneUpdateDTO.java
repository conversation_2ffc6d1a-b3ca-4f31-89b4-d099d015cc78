package com.cmpay.hacp.controller.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @create 2024/05/15 11:22
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DispatchZoneUpdateDTO extends DispatchZoneAddDTO {
    /**
     * @Fields id 主键
     */
    @ApiModelProperty(value = "主键", required = true, example = "1")
    @NotNull(message = "HAC00009")
    private Long id;
}
