package com.cmpay.hacp.controller.dispatch;

import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.hacp.controller.dto.AppInstanceZoneReqDTO;
import com.cmpay.hacp.dispatch.bo.AppInstanceZoneBO;
import com.cmpay.hacp.dispatch.service.AppInstanceZoneService;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.log.annotation.LogNoneRecord;
import com.cmpay.hacp.log.annotation.LogRecord;
import com.cmpay.hacp.utils.TenantSecurityUtils;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/v1/app-instance-zone")
@Api(tags = "业务节点管理-节点机房配置-1层级")
public class AppInstanceZoneController {

    @Autowired
    private AppInstanceZoneService appInstanceZoneService;
    @ApiOperation(value = "add", notes = "添加业务节点机房", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @LogRecord(title = "新增业务节点机房", action = "新增")
    @PostMapping("/add")
    @PreAuthorize("hasPermission('AppInstanceZoneController','dispatch:instance-zone:add')")
    public DefaultRspDTO<NoBody> add(@RequestBody AppInstanceZoneReqDTO reqDTO) {
        TenantSecurityUtils.copyTenantSecurity(reqDTO);
        appInstanceZoneService.addAppInstanceZone(reqDTO);
        return DefaultRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "list", notes = "获取业务节机房列表", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @LogNoneRecord
    @GetMapping("/list")
    @PreAuthorize("hasPermission('AppInstanceZoneController','dispatch:instance-zone:query')")
    public GenericRspDTO<List<AppInstanceZoneBO>> getList(AppInstanceZoneReqDTO reqDTO) {
        TenantSecurityUtils.copyWorkspace(reqDTO);
        List<AppInstanceZoneBO> list = appInstanceZoneService.getAppInstanceZoneList(reqDTO);
        return GenericRspDTO.newInstance(MsgEnum.SUCCESS, list);
    }

    @ApiOperation(value = "delete", notes = "删除业务节点机房", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @LogRecord(title = "删除业务节点机房", action = "删除")
    @PostMapping("/delete")
    @PreAuthorize("hasPermission('AppInstanceZoneController','dispatch:instance-zone:delete')")
    public DefaultRspDTO<NoBody> delete(@RequestBody AppInstanceZoneReqDTO reqDTO) {
        TenantSecurityUtils.copyTenantSecurity(reqDTO);
        appInstanceZoneService.deleteAppInstanceZone(reqDTO);
        return DefaultRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "update", notes = "更新业务节点机房", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @LogRecord(title = "修改业务节点机房", action = "修改")
    @PostMapping("/update")
    @PreAuthorize("hasPermission('AppInstanceZoneController','dispatch:instance-zone:update')")
    public DefaultRspDTO<NoBody> update(@RequestBody AppInstanceZoneReqDTO reqDTO) {
        TenantSecurityUtils.copyTenantSecurity(reqDTO);
        appInstanceZoneService.updateAppInstanceZone(reqDTO);
        return DefaultRspDTO.newSuccessInstance();
    }
}