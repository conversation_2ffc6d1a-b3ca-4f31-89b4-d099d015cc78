/*
 * @ClassName DispatchZoneDO
 * @Description 
 * @version 1.0
 * @Date 2024-05-15 10:07:29
 */
package com.cmpay.hacp.controller.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@ApiModel
public class DispatchZoneDTO {

    /**
     * @Fields id 机房id
     */
    @ApiModelProperty("机房id")
    private Long id;

    /**
     * @Fields workspaceId 项目id
     */
    @ApiModelProperty("项目id")
    private String workspaceId;

    /**
     * @Fields zoneName 机房名称
     */
    @ApiModelProperty("机房名称")
    private String zoneName;

    /**
     * @Fields zoneLabel 机房标识
     */
    @ApiModelProperty("机房标识")
    private String zoneLabel;

    /**
     * @Fields operatorId 操作员
     */
    @ApiModelProperty("操作员Id")
    private String operatorId;
    /**
     * @Fields operatorName 操作员名称
     */
    @ApiModelProperty("操作员名称")
    private String operatorName;

    /**
     * @Fields createTime 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /**
     * @Fields updateTime 更新时间
     */
    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    /**
     * @Fields remarks 备注信息
     */
    @ApiModelProperty("备注信息")
    private String remarks;


}