package com.cmpay.hacp.controller.emergency;

import com.cmpay.hacp.bo.EmergencyTaskScriptBO;
import com.cmpay.hacp.utils.TenantSecurityUtils;
import com.cmpay.hacp.dto.emergency.EmergencyTaskScriptPageableReqDTO;
import com.cmpay.hacp.dto.emergency.EmergencyTaskScriptReqDTO;
import com.cmpay.hacp.dto.emergency.EmergencyTaskScriptUpdateReqDTO;
import com.cmpay.hacp.log.annotation.LogNoneRecord;
import com.cmpay.hacp.log.annotation.LogRecord;
import com.cmpay.hacp.service.EmergencyTaskScriptService;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import com.cmpay.lemon.framework.page.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 应急机器控制器
 *
 * <AUTHOR>
 * @create 2024/08/21 14:02:05
 * @since 1.0.0
 */

@RestController
@Api(tags = "应急调度任务脚本管理")
@RequestMapping("/v1/emergency/task-script")
@RequiredArgsConstructor
@Slf4j
public class EmergencyTaskScriptController {

    /**
     * 应急机器服务
     */
    private final EmergencyTaskScriptService emergencyTaskScriptService;

    /**
     * 新增
     *
     * @param reqDTO req dto
     * @return 成功/失败
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增", notes = "新增")
    @LogRecord(title = "新增任务脚本", action = "新增")
    @ApiResponse(code = 200, message = "返回结果")
    @PreAuthorize("hasPermission('EmergencyTaskScriptController','emergency:script:add')")
    public DefaultRspDTO<NoBody> add(@Validated @RequestBody EmergencyTaskScriptReqDTO reqDTO) {
        TenantSecurityUtils.copyTenantSecurity(reqDTO);
        emergencyTaskScriptService.add(BeanConvertUtil.convert(reqDTO, EmergencyTaskScriptBO.class));
        return DefaultRspDTO.newSuccessInstance();
    }


    /**
     * 修改
     *
     * @param reqDTO req dto
     * @return 成功/失败
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改", notes = "修改")
    @ApiResponse(code = 200, message = "返回结果")
    @LogRecord(title = "修改任务脚本", action = "修改")
    @PreAuthorize("hasPermission('EmergencyHostController','emergency:script:update')")
    public DefaultRspDTO<NoBody> update(@Validated @RequestBody EmergencyTaskScriptUpdateReqDTO reqDTO) {
        TenantSecurityUtils.copyTenantSecurity(reqDTO);
        emergencyTaskScriptService.update(BeanConvertUtil.convert(reqDTO, EmergencyTaskScriptBO.class));
        return DefaultRspDTO.newSuccessInstance();
    }

    /**
     * 删除
     *
     * @param reqDTO req dto
     * @return 成功/失败
     */
    @PostMapping("/delete")
    @ApiOperation(value = "删除", notes = "删除")
    @LogRecord(title = "删除任务脚本", action = "删除")
    @ApiResponse(code = 200, message = "返回结果")
    @PreAuthorize("hasPermission('EmergencyHostController','emergency:script:delete')")
    public DefaultRspDTO<NoBody> delete(@RequestBody EmergencyTaskScriptUpdateReqDTO reqDTO) {
        emergencyTaskScriptService.delete(reqDTO.getId());
        return DefaultRspDTO.newSuccessInstance();
    }

    /**
     * 查询详情
     *
     * @param reqDTO req dto
     * @return 详情
     */
    @GetMapping("/info")
    @ApiOperation(value = "查询详情", notes = "查询详情")
    @ApiResponse(code = 200, message = "详情")
    @LogRecord(title = "查询任务脚本详情", action = "查询")
    @PreAuthorize("hasPermission('EmergencyHostController','emergency:script:query')")
    public DefaultRspDTO<EmergencyTaskScriptBO> getInfo(EmergencyTaskScriptUpdateReqDTO reqDTO) {
        EmergencyTaskScriptBO detailInfo = emergencyTaskScriptService.getDetailInfo(reqDTO.getId());
        return DefaultRspDTO.newSuccessInstance(detailInfo);
    }

    /**
     * 查询分页列表
     *
     * @param reqDTO req dto
     * @return 查询分页列表
     */
    @GetMapping("/page")
    @ApiOperation(value = "查询分页列表", notes = "查询分页列表")
    @ApiResponse(code = 200, message = "列表")
    @LogNoneRecord
    @PreAuthorize("hasPermission('EmergencyHostController','emergency:script:query')")
    public DefaultRspDTO<PageInfo<EmergencyTaskScriptBO>> getPage(EmergencyTaskScriptPageableReqDTO reqDTO) {
        TenantSecurityUtils.copyWorkspace(reqDTO);
        PageInfo<EmergencyTaskScriptBO> page = emergencyTaskScriptService.getPage(reqDTO.getPageNum(), reqDTO.getPageSize()
                , BeanConvertUtil.convert(reqDTO, EmergencyTaskScriptBO.class));
        return DefaultRspDTO.newSuccessInstance(page);
    }

    /**
     * 查询列表
     *
     * @return 查询列表
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询列表", notes = "查询列表")
    @ApiResponse(code = 200, message = "列表")
    @LogNoneRecord
    public DefaultRspDTO<List<EmergencyTaskScriptBO>> getList(@RequestBody EmergencyTaskScriptPageableReqDTO reqDTO) {
        TenantSecurityUtils.copyWorkspace(reqDTO);
        List<EmergencyTaskScriptBO> list = emergencyTaskScriptService.getList(BeanConvertUtil.convert(reqDTO,EmergencyTaskScriptBO.class));
        return DefaultRspDTO.newSuccessInstance(list);
    }
 }
