package com.cmpay.hacp.controller.extend.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class TacticsDispatchPageableReqDTO  {
    // @ApiModelProperty(value = "状态",example = "状态")
    // private Integer status;
    //
    // /**
    //  * 策略类型，默认：0，异常：1，降级：2，兜底3
    //  */
    // @ApiModelProperty(value = "策略类型",example = "策略类型")
    // private Integer type;
    @NotNull(message = "HAC00028")
    private Integer pageNo;
    @NotNull(message = "HAC00029")
    private Integer pageSize;
}
