package com.cmpay.hacp.controller.extend.dto;

import com.cmpay.hacp.bo.PageableReq;
import com.cmpay.hacp.capable.TenantCapable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @create 2024/09/10 10:01
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EmergencyContainerPageableReqDTO extends PageableReq implements TenantCapable {

    @ApiModelProperty(value = "客户端id",example = "客户端id")
    private String clientId;

    /**
     * @Fields operatorId 操作员
     */
    private String operatorId;
    /**
     * @Fields operatorName 操作员名称
     */
    private String operatorName;
    /**
     * @Fields workspaceId 项目空间ID
     */
    private String workspaceId;
}
