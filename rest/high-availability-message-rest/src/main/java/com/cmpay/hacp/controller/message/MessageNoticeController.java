package com.cmpay.hacp.controller.message;

import com.cmpay.hacp.api.BaseApi;
import com.cmpay.hacp.api.VersionApi;
import com.cmpay.hacp.bo.MessageContentExtBO;
import com.cmpay.hacp.bo.MessageContentUserBO;
import com.cmpay.hacp.dto.message.MessageContentQueryPageDTO;
import com.cmpay.hacp.dto.message.MessageContentRspDTO;
import com.cmpay.hacp.log.annotation.LogNoneRecord;
import com.cmpay.hacp.log.annotation.LogRecord;
import com.cmpay.hacp.service.MessageNoticeService;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.framework.annotation.QueryBody;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import com.cmpay.lemon.framework.page.PageInfo;
import com.cmpay.lemon.framework.security.SecurityUtils;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @create 2024/09/29 9:32
 * @since 1.0.0
 */
@RestController
@RequestMapping(VersionApi.VERSION_V1 + BaseApi.BASE_MESSAGE_NOTICE)
public class MessageNoticeController {

    @Autowired
    private MessageNoticeService messageNoticeService;

    @GetMapping("/number-of-unread-message")
    @ApiOperation(value = "获取未读数量", notes = "获取未读数量")
    @ApiResponse(code = 200, message = "返回结果")
    @LogNoneRecord
    public DefaultRspDTO<Integer> getNumberOfUnreadMessages(){
        return DefaultRspDTO.newSuccessInstance(messageNoticeService.getNumberOfUnreadMessages(SecurityUtils.getLoginUserId()));
    }

    /**
     * 读取消息，分页，滚动时需要查询
     * @param requestPageDTO
     * @return
     */
    @LogNoneRecord
    @GetMapping("/get-messages")
    @ApiOperation(value = "获取消息", notes = "获取分页消息")
    @ApiResponse(code = 200, message = "返回结果")
    public DefaultRspDTO<PageInfo<MessageContentRspDTO>> queryMessage(@QueryBody MessageContentQueryPageDTO requestPageDTO){
        MessageContentUserBO queryUserBO = new MessageContentUserBO();
        queryUserBO.setUserId(SecurityUtils.getLoginUserId());
        queryUserBO.setStatus(requestPageDTO.getStatus());
        PageInfo<MessageContentExtBO> data = messageNoticeService.queryMessage(requestPageDTO.getPageNum(),requestPageDTO.getPageSize(),queryUserBO);
        PageInfo<MessageContentRspDTO> body = new PageInfo<>(BeanConvertUtil.convertList(data.getList(), MessageContentRspDTO.class));
        body.setTotal(data.getTotal());
        return DefaultRspDTO.newSuccessInstance(body);
    }


    /**
     * 单消息已读
     * @param messageId
     * @return
     */
    @PostMapping("/read-message/{id}")
    @ApiOperation(value = "单个已读", notes = "单个已读")
    @LogRecord(title = "消息已读操作", action = "修改")
    @ApiResponse(code = 200, message = "返回结果")
    public DefaultRspDTO<NoBody> readMessage(@PathVariable("id") Long messageId){
        messageNoticeService.readMessage(messageId,SecurityUtils.getLoginUserId());
        return DefaultRspDTO.newSuccessInstance();
    }

    /**
     * 全部已读
     * @return
     */
    @PostMapping("/read-message-all")
    @ApiOperation(value = "全部已读", notes = "全部已读")
    @LogRecord(title = "消息全部已读操作", action = "修改")
    @ApiResponse(code = 200, message = "返回结果")
    public DefaultRspDTO<NoBody> readMessage(){
        messageNoticeService.readMessage(null,SecurityUtils.getLoginUserId());
        return DefaultRspDTO.newSuccessInstance();
    }
}
