package com.cmpay.hacp.inspection.dto;

import com.cmpay.hacp.inspection.domain.model.enums.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class InspectionRuleReqDTO {

    @ApiModelProperty(value = "规则ID，新增时不需要传入", example = "1001")
    private String ruleId;

    @ApiModelProperty(value = "规则名称（非空）", required = true, example = "CPU 使用率阈值检查")
    @NotBlank(message = "规则名称不能为空")
    @Size(max = 128, message = "规则名称长度不能超过128个字符")
    private String name;

    @ApiModelProperty(value = "规则描述（允许为空）", example = "CPU使用率阈值检查")
    private String description;

    /**
     * @see RuleLevelEnum
     */
    @ApiModelProperty(value = "告警等级（1-低、2-中、3-高、4-严重）", required = true, example = "1")
    @NotNull(message = "告警等级不能为空")
    private RuleLevelEnum level;

    /**
     * @see RuleStatusEnum
     */
    @NotNull(message = "规则状态不能为空")
    @ApiModelProperty(value = "规则状态（0启用，1禁用）", required = true, example = "0")
    private RuleStatusEnum status;

    /**
     * @see RuleTypeEnum
     */
    @ApiModelProperty(value = "规则类型（1-指标、2-日志、3-可用性）", example = "1")
    private RuleTypeEnum type;

    /**
     * 规则标签关联
     */
    @ApiModelProperty(value = "标签ID列表", example = "[1, 2, 3]")
    private List<String> tagIds;

    /**
     * 规则插件关联
     */
    @ApiModelProperty(value = "插件ID", required = true, example = "1")
    @NotBlank(message = "插件ID不能为空")
    private String pluginId;

    /**
     * 目标类型：规则目标范围
     * @see RuleAngleViewEnum
     */
    @ApiModelProperty(value = "功能视角【1-中枢产品(基础设施服务)、2-业务应用(业务功能服务)、3-中间件(支撑服务)】", required = true, example = "1")
    @NotNull(message = "功能视角不能为空")
    private RuleAngleViewEnum angleView;

    /**
     * @see RuleDeployEnvEnum
     */
    @ApiModelProperty(value = "部署环境【1-虚拟机(部署在虚拟机上的服务)、2-容器(部署在K8s/Docker环境的服务)】", required = true, example = "1")
    @NotNull(message = "部署环境不能为空")
    private RuleDeployEnvEnum deployEnv;

    @ApiModelProperty(value = "规则插件参数配置")
    private List<RulePluginParamDTO> pluginParams;

    @ApiModelProperty(value = "规则监控字段", required = true)
    @NotNull(message = "监控字段不能为空")
    private RulePluginResultDTO pluginResult;

}
