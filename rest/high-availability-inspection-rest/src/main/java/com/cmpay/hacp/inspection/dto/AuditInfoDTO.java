package com.cmpay.hacp.inspection.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 审计信息DTO
 */
@Data
@ApiModel(description = "审计信息DTO")
public class AuditInfoDTO {

    @ApiModelProperty(value = "创建人", example = "admin")
    private String createdBy;

    @ApiModelProperty(value = "创建时间", example = "2023-01-01 12:00:00")
    private LocalDateTime createdTime;

    @ApiModelProperty(value = "更新人", example = "admin")
    private String updatedBy;

    @ApiModelProperty(value = "更新时间", example = "2023-01-01 12:00:00")
    private LocalDateTime updatedTime;
}
