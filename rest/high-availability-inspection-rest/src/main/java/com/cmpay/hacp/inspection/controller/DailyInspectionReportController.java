package com.cmpay.hacp.inspection.controller;

import com.cmpay.hacp.inspection.application.service.DailyInspectionReportService;
import com.cmpay.hacp.inspection.assembler.DailyInspectionReportDTOMapper;
import com.cmpay.hacp.inspection.dto.DailyInspectionReportDTO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.ReportDO;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 按日巡检报告
 */
@Slf4j
@RestController
@RequestMapping("/v1/inspection/report/daily")
@Api(tags = "按日巡检报告")
@RequiredArgsConstructor
public class DailyInspectionReportController {

    private final DailyInspectionReportService dailyInspectionReportService;
    private final DailyInspectionReportDTOMapper dailyReportDTOMapper;

    /**
     * 查询按日巡检报告
     *
     * @param date 巡检日期
     * @return 巡检报告数据
     */
    @ApiOperation(value = "查询按日巡检报告", notes = "根据指定日期查询巡检报告")
    @ApiResponse(code = 200, message = "成功")
    @GetMapping("/date/{date}")
    @PreAuthorize("hasPermission('DailyInspectionReportController','inspection:daily:query')")
    public DefaultRspDTO<Map<String, Object>> getDailyReportByDate(
            @ApiParam(name = "date", value = "巡检日期", example = "2025-05-08")
            @PathVariable(value = "date", required = false)
            @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate date) {

        // 如果未指定日期，则使用当前日期
        if (JudgeUtils.isNull(date)) {
            date = LocalDate.now();
        }

        // TODO: 调用服务层方法查询报告

        return DefaultRspDTO.newSuccessInstance(new HashMap<>());
    }

    /**
     * 查询按日巡检报告
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 巡检报告列表
     */
    @ApiOperation(value = "查询按日巡检报告", notes = "根据日期范围查询巡检报告")
    @ApiResponse(code = 200, message = "成功")
    @GetMapping("/range")
    @PreAuthorize("hasPermission('DailyInspectionReportController','inspection:daily:query')")
    public DefaultRspDTO<Map<String, Object>> listDailyReportsByDateRange(
            @ApiParam(name = "startDate", value = "开始日期", example = "2025-05-01")
            @RequestParam(value = "startDate", required = false)
            @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,

            @ApiParam(name = "endDate", value = "结束日期", example = "2025-05-20")
            @RequestParam(value = "endDate", required = false)
            @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {

        // 如果未指定开始日期，则使用当前日期减去30天
        if (JudgeUtils.isNull(startDate)) {
            startDate = LocalDate.now().minusDays(30);
        }

        // 如果未指定结束日期，则使用当前日期
        if (JudgeUtils.isNull(endDate)) {
            endDate = LocalDate.now();
        }

        // TODO: 调用服务层方法查询报告
        // TODO: 分页

        return DefaultRspDTO.newSuccessInstance(new HashMap<>());
    }

    /**
     * 获取指定日期的巡检报告详情
     *
     * @param date 巡检日期
     * @return 巡检报告详情
     */
    @ApiOperation(value = "获取巡检报告详情", notes = "根据日期获取巡检报告详情")
    @ApiResponse(code = 200, message = "成功")
    @GetMapping("/date/{date}/detail")
    @PreAuthorize("hasPermission('DailyInspectionReportController','inspection:daily:query')")
    public DefaultRspDTO<Map<String, Object>> getDailyReportDetailByDate(
            @ApiParam(name = "date", value = "巡检日期", example = "2024-08-20", required = true)
            @PathVariable("date")
            @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate date) {


        // TODO: 调用服务层方法获取报告详情

        return DefaultRspDTO.newSuccessInstance(new HashMap<>());
    }

    /**
     * 导出指定日期的巡检报告
     *
     * @param date   巡检日期
     * @param format 导出格式
     * @return 导出结果
     */
    @ApiOperation(value = "导出按日巡检报告", notes = "将按日巡检报告导出为指定格式")
    @ApiResponse(code = 200, message = "成功")
    @GetMapping("date/{date}/export")
    @PreAuthorize("hasPermission('InstanceInspectionReportController','inspection:daily:export')")
    public DefaultRspDTO<Map<String, Object>> exportReport(
            @ApiParam(name = "date", value = "巡检日期", example = "2025-05-01", required = true)
            @PathVariable("date") LocalDate date,

            @ApiParam(name = "format", value = "导出格式", example = "PDF", allowableValues = "PDF,EXCEL,HTML", required = true)
            @RequestParam("format") String format) {


        // TODO: 调用服务层方法导出报告


        return DefaultRspDTO.newSuccessInstance(new HashMap<>());
    }

    /**
     * 导出指定日期范围内的巡检报告
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param format    导出格式
     * @return 导出结果
     */
    @ApiOperation(value = "导出日期范围内的巡检报告", notes = "将指定日期范围内的所有巡检报告导出为指定格式")
    @ApiResponse(code = 200, message = "成功")
    @PreAuthorize("hasPermission('DailyInspectionReportController','inspection:daily:export')")
    @GetMapping("/range/export")
    public DefaultRspDTO<Map<String, Object>> exportReportByDateRange(
            @ApiParam(name = "startDate", value = "开始日期", example = "2025-05-01")
            @RequestParam(value = "startDate", required = false)
            @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,

            @ApiParam(name = "endDate", value = "结束日期", example = "2025-05-20")
            @RequestParam(value = "endDate", required = false)
            @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate,

            @ApiParam(name = "format", value = "导出格式", example = "PDF", allowableValues = "PDF,EXCEL,HTML,JSON", required = false)
            @RequestParam(value = "format", defaultValue = "PDF") String format) {

        // 如果未指定开始日期，则使用当前日期减去30天
        if (JudgeUtils.isNull(startDate)) {
            startDate = LocalDate.now().minusDays(30);
        }

        // 如果未指定结束日期，则使用当前日期
        if (JudgeUtils.isNull(endDate)) {
            endDate = LocalDate.now();
        }

        // 验证日期范围
        if (startDate.isAfter(endDate)) {
            return DefaultRspDTO.newInstance("HAI20000", "开始日期不能晚于结束日期");
        }

        // TODO: 调用服务层方法导出报告

        return DefaultRspDTO.newSuccessInstance(new HashMap<>());
    }
}
