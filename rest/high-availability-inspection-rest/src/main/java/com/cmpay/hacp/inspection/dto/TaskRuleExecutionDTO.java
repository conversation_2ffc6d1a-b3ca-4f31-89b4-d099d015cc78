package com.cmpay.hacp.inspection.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 规则执行配置DTO
 */
@Data
@ApiModel(description = "规则执行配置DTO")
public class TaskRuleExecutionDTO {

    @ApiModelProperty(value = "任务ID", required = true, example = "TASK-000001")
    @NotBlank(message = "任务ID不能为空")
    private String taskId;

    @ApiModelProperty(value = "规则ID", required = true, example = "RULE-000001")
    @NotBlank(message = "规则ID不能为空")
    private String ruleId;

    @ApiModelProperty(value = "规则名称", example = "CPU 使用率阈值检查")
    private String ruleName;

    @ApiModelProperty(value = "目标环境ID列表", required = true, example = "[\"ENV-000001\", \"ENV-000002\"]")
    @NotEmpty(message = "目标环境ID列表不能为空")
    private List<String> targetEnvironmentId;
}
