package com.cmpay.hacp.inspection.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 按日巡检报告DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "按日巡检报告")
public class DailyInspectionReportDTO {

    @ApiModelProperty(value = "报告日期")
    private LocalDate reportDate;

    @ApiModelProperty(value = "报告ID")
    private String reportId;

    @ApiModelProperty(value = "总检查数")
    private Integer totalChecks;

    @ApiModelProperty(value = "成功数")
    private Integer successCount;

    @ApiModelProperty(value = "告警数")
    private Integer warningCount;

    @ApiModelProperty(value = "失败数")
    private Integer failedCount;

    @ApiModelProperty(value = "通过率（百分比）")
    private Integer passRate;

    @ApiModelProperty(value = "报告生成时间")
    private LocalDateTime generateTime;

    @ApiModelProperty(value = "执行概况")
    private ExecutionSummaryDTO executionSummary;

    @ApiModelProperty(value = "执行结果分布")
    private ExecutionDistributionDTO executionDistribution;

    @ApiModelProperty(value = "通过率趋势")
    private PassRateTrendDTO passRateTrend;

    @ApiModelProperty(value = "异常汇总")
    private List<ExceptionSummaryDTO> exceptionSummary;

    @ApiModelProperty(value = "异常详情列表")
    private List<ExceptionDetailDTO> exceptionDetails;

    /**
     * 执行概况DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(description = "执行概况")
    public static class ExecutionSummaryDTO {
        @ApiModelProperty(value = "总任务数")
        private Integer totalTasks;

        @ApiModelProperty(value = "成功任务数")
        private Integer successTasks;

        @ApiModelProperty(value = "失败任务数")
        private Integer failedTasks;

        @ApiModelProperty(value = "总规则数")
        private Integer totalRules;

        @ApiModelProperty(value = "成功规则数")
        private Integer successRules;

        @ApiModelProperty(value = "失败规则数")
        private Integer failedRules;
    }

    /**
     * 执行结果分布DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(description = "执行结果分布")
    public static class ExecutionDistributionDTO {
        @ApiModelProperty(value = "总数")
        private Integer total;

        @ApiModelProperty(value = "通过率")
        private Integer successRate;

        @ApiModelProperty(value = "状态统计")
        private List<StatusCountDTO> statusCounts;

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        @ApiModel(description = "状态统计")
        public static class StatusCountDTO {
            @ApiModelProperty(value = "状态名称")
            private String status;

            @ApiModelProperty(value = "状态编码")
            private String statusCode;

            @ApiModelProperty(value = "数量")
            private Integer count;

            @ApiModelProperty(value = "百分比")
            private Double percentage;
        }
    }

    /**
     * 通过率趋势DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(description = "通过率趋势")
    public static class PassRateTrendDTO {
        @ApiModelProperty(value = "趋势数据点")
        private List<TrendPointDTO> trendPoints;

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        @ApiModel(description = "趋势数据点")
        public static class TrendPointDTO {
            @ApiModelProperty(value = "日期")
            private LocalDate date;

            @ApiModelProperty(value = "通过率")
            private Integer passRate;

            @ApiModelProperty(value = "总数")
            private Integer total;

            @ApiModelProperty(value = "成功数")
            private Integer success;
        }
    }

    /**
     * 异常汇总DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(description = "异常汇总")
    public static class ExceptionSummaryDTO {
        @ApiModelProperty(value = "异常级别")
        private String level;

        @ApiModelProperty(value = "异常级别名称")
        private String levelName;

        @ApiModelProperty(value = "异常数量")
        private Integer count;

        @ApiModelProperty(value = "异常类型列表")
        private List<String> exceptionTypes;
    }

    /**
     * 异常详情DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(description = "异常详情")
    public static class ExceptionDetailDTO {
        @ApiModelProperty(value = "规则ID")
        private String ruleId;

        @ApiModelProperty(value = "规则名称")
        private String ruleName;

        @ApiModelProperty(value = "异常描述")
        private String description;

        @ApiModelProperty(value = "资源名称")
        private String resourceName;

        @ApiModelProperty(value = "异常级别")
        private String level;

        @ApiModelProperty(value = "发生时间")
        private LocalDateTime occurTime;

        @ApiModelProperty(value = "建议措施")
        private String suggestion;
    }
}
